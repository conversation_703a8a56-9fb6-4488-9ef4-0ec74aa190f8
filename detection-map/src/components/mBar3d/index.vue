<template>
  <div class="three-bar-wrap">
    <div class="three-bar" ref="barDom"></div>
  </div>
</template>

<script>
import * as THREE from "three"
import gsap from "gsap"
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls"
import { emptyObject } from "@/mini3d"

import ring2 from "@/assets/texture/pie/ring2.png"
import ring3 from "@/assets/texture/pie/ring3.png"
import ring4 from "@/assets/texture/pie/ring4.png"

export default {
  name: "ThreeBar",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    colors: {
      type: Array,
      default: () => ["#17E6C3", "#40CFFF", "#1979FF", "#FFC472", "#FF6B6B"],
    },
    opacity: {
      type: Number,
      default: 1.0, // 提高默认不透明度
    },
    delay: {
      type: Number,
      default: 3000,
    },
  },
  data() {
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.axes = null
    this.barGroup = new THREE.Group()
    this.gridGroup = new THREE.Group()
    this.particles = null
    return {
      width: 300,
      height: 200,
      activeIndex: 0,
      timer: null,
      maxValue: 0,
    }
  },
  mounted() {
    this.width = this.$refs["barDom"].offsetWidth
    this.height = this.$refs["barDom"].offsetHeight
    this.maxValue = Math.max(...this.data.map(item => item.value))
    this.init()
  },
  beforeUnmount() {
    clearInterval(this.timer)
    this.destroy()
  },
  methods: {
    init() {
      this.scene = new THREE.Scene()
      // 确保场景背景透明
      this.scene.background = null
      this.initCamera()
      this.initRenderer()
      this.initLight()
      this.initAxes()
      this.initControls()
      this.createGrid()
      this.createBars()
      this.createParticles()
      this.createBackground()
      this.loop()
    },
    
    createGrid() {
      // 创建3D纹理底座，参考饼图的底座样式，缩小光圈范围
      this.createPlane({
        url: ring2,
        width: 8, // 缩小底座大小
        position: new THREE.Vector3(0, -0.01, 0),
        color: "#00ffff",
      })
      this.createPlane({
        url: ring3,
        width: 10, // 缩小底座大小
        position: new THREE.Vector3(0, -0.02, 0),
        color: "#00ffff",
      })
      this.createPlane({
        url: ring4,
        width: 9, // 缩小底座大小
        position: new THREE.Vector3(0, -0.03, 0),
        animate: true,
        color: "#00ffff",
      })

      this.scene.add(this.gridGroup)
    },

    createPlane(opt) {
      let defaultOpt = {
        url: "texture/ring1.png",
        width: 5.5,
        z: 0,
        position: new THREE.Vector3(0, 0, 0),
        animate: false,
        color: null,
      }
      let options = Object.assign(defaultOpt, opt)
      const geometry = new THREE.PlaneGeometry(options.width, options.width)
      const material = new THREE.MeshBasicMaterial({
        map: this.getTexture(options.url),
        transparent: true,
        side: THREE.DoubleSide,
        depthTest: false,
      })
      if (options.color) {
        material.color = new THREE.Color(options.color)
      }
      const mesh = new THREE.Mesh(geometry, material)
      mesh.position.copy(options.position)
      mesh.rotation.x = (-1 * Math.PI) / 2
      if (options.animate) {
        gsap.to(mesh.rotation, {
          z: 2 * Math.PI,
          repeat: -1,
          ease: "none",
          duration: 3,
        })
      }
      this.gridGroup.add(mesh)
    },

    getTexture(url) {
      const texture = new THREE.TextureLoader().load(url)
      texture.wrapS = texture.wrapT = THREE.RepeatWrapping
      return texture
    },

    createParticles() {
      // 创建浮动粒子效果
      const particleCount = 100
      const positions = new Float32Array(particleCount * 3)
      const colors = new Float32Array(particleCount * 3)

      for (let i = 0; i < particleCount; i++) {
        positions[i * 3] = (Math.random() - 0.5) * 20
        positions[i * 3 + 1] = Math.random() * 10
        positions[i * 3 + 2] = (Math.random() - 0.5) * 20

        const color = new THREE.Color()
        color.setHSL(Math.random() * 0.2 + 0.5, 0.7, 0.5)
        colors[i * 3] = color.r
        colors[i * 3 + 1] = color.g
        colors[i * 3 + 2] = color.b
      }

      const particleGeometry = new THREE.BufferGeometry()
      particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
      particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))

      const particleMaterial = new THREE.PointsMaterial({
        size: 0.05,
        vertexColors: true,
        transparent: true,
        opacity: 0.3, // 降低粒子不透明度
        blending: THREE.AdditiveBlending
      })

      this.particles = new THREE.Points(particleGeometry, particleMaterial)
      this.scene.add(this.particles)
    },

    createBackground() {
      // 移除背景球体，保持透明
      // 只保留少量星空效果作为装饰
      const starGeometry = new THREE.BufferGeometry()
      const starCount = 50 // 减少星星数量
      const starPositions = new Float32Array(starCount * 3)

      for (let i = 0; i < starCount; i++) {
        const radius = 30
        const theta = Math.random() * Math.PI * 2
        const phi = Math.random() * Math.PI

        starPositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta)
        starPositions[i * 3 + 1] = radius * Math.cos(phi)
        starPositions[i * 3 + 2] = radius * Math.sin(phi) * Math.sin(theta)
      }

      starGeometry.setAttribute('position', new THREE.BufferAttribute(starPositions, 3))

      const starMaterial = new THREE.PointsMaterial({
        color: 0x00ffff,
        size: 0.3,
        transparent: true,
        opacity: 0.3 // 降低透明度
      })

      const stars = new THREE.Points(starGeometry, starMaterial)
      this.scene.add(stars)
    },

    createBars() {
      const barWidth = 0.8 // 增加柱体宽度
      const barSpacing = 1.6 // 增加柱体间距
      const startX = -(this.data.length - 1) * barSpacing / 2

      this.data.forEach((item, index) => {
        const barHeight = (item.value / this.maxValue) * 5 // 增加最大高度为5

        // 创建柱体几何体 - 使用圆角效果
        const geometry = new THREE.BoxGeometry(barWidth, barHeight, barWidth)

        // 创建渐变材质 - 使用更亮的材质
        const color = new THREE.Color(this.colors[index % this.colors.length])
        const material = new THREE.MeshPhongMaterial({
          color: color,
          transparent: true,
          opacity: 1.0, // 提高不透明度
          shininess: 200,
          specular: 0x888888, // 增强高光
          emissive: color.clone().multiplyScalar(0.2), // 增强自发光
        })

        const bar = new THREE.Mesh(geometry, material)
        bar.position.set(startX + index * barSpacing, barHeight / 2 - 0.5, 0) // 往下调整0.5个单位
        bar.name = `bar${index}`
        bar.userData = { originalHeight: barHeight, index: index, data: item }
        bar.castShadow = true
        bar.receiveShadow = true

        // 添加内部发光效果 - 增强亮度
        const glowGeometry = new THREE.BoxGeometry(barWidth * 1.05, barHeight * 1.05, barWidth * 1.05)
        const glowMaterial = new THREE.MeshBasicMaterial({
          color: color,
          transparent: true,
          opacity: 0.3, // 增强发光效果
          side: THREE.BackSide
        })
        const glow = new THREE.Mesh(glowGeometry, glowMaterial)
        glow.position.copy(bar.position) // 发光效果跟随柱体位置
        glow.name = `glow${index}`

        // 移除单独的底部光环，使用统一的纹理底座
        this.barGroup.add(glow)
        this.barGroup.add(bar)

        // 添加数值标签
        this.createLabel(item.value, bar.position.clone().add(new THREE.Vector3(0, barHeight / 2 + 0.3, 0)))

        // 添加类别标签
        this.createCategoryLabel(item.name, new THREE.Vector3(startX + index * barSpacing, -1.0, 0)) // 往下调整类别标签位置
      })

      this.scene.add(this.barGroup)
      this.animateBars()
    },

    createLabel(value, position) {
      // 创建文字纹理
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = 128
      canvas.height = 64

      // 添加发光效果
      context.shadowColor = '#00ffff'
      context.shadowBlur = 15
      context.fillStyle = '#ffffff'
      context.font = 'bold 20px Arial'
      context.textAlign = 'center'
      // 绘制发光效果
      context.strokeStyle = '#00ffff'
      context.lineWidth = 2
      context.strokeText(value.toString(), 64, 40)
      context.fillText(value.toString(), 64, 40)

      const texture = new THREE.CanvasTexture(canvas)
      const material = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        alphaTest: 0.1
      })
      const sprite = new THREE.Sprite(material)
      sprite.position.copy(position)
      sprite.scale.set(0.8, 0.4, 1) // 增加数值标签大小

      this.barGroup.add(sprite)
    },

    createCategoryLabel(text, position) {
      // 创建类别标签
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = 128
      canvas.height = 32

      context.fillStyle = '#ffffff'
      context.font = 'bold 16px Arial'
      context.textAlign = 'center'
      // 添加发光效果
      context.shadowColor = '#00ffff'
      context.shadowBlur = 8
      context.fillText(text, 64, 20)

      const texture = new THREE.CanvasTexture(canvas)
      const material = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        alphaTest: 0.1
      })
      const sprite = new THREE.Sprite(material)
      sprite.position.copy(position)
      sprite.scale.set(1.2, 0.3, 1) // 增加类别标签大小

      this.barGroup.add(sprite)
    },

    animateBars() {
      let barIndex = 0
      this.barGroup.children.forEach((child, index) => {
        if (child.name.startsWith('bar')) {
          const originalScale = child.scale.y
          child.scale.y = 0
          gsap.to(child.scale, {
            y: originalScale,
            duration: 1.2,
            delay: barIndex * 0.15,
            ease: "elastic.out(1, 0.5)"
          })
          barIndex++
        }
      })

      // 开始循环高亮动画
      setTimeout(() => {
        this.timer = setInterval(() => {
          this.highlightBar()
        }, this.delay)
      }, 2000) // 等待初始动画完成
    },

    highlightBar() {
      // 重置所有柱子
      this.barGroup.children.forEach(child => {
        if (child.name.startsWith('bar')) {
          gsap.to(child.scale, { x: 1, z: 1, duration: 0.5 })
          gsap.to(child.material, { opacity: 1.0, duration: 0.5 }) // 保持高亮度
        }
        if (child.name.startsWith('glow')) {
          gsap.to(child.material, { opacity: 0.3, duration: 0.5 }) // 增强发光
        }
      })

      // 高亮当前柱子
      const currentBar = this.barGroup.getObjectByName(`bar${this.activeIndex}`)
      const currentGlow = this.barGroup.getObjectByName(`glow${this.activeIndex}`)

      if (currentBar) {
        gsap.to(currentBar.scale, { x: 1.1, z: 1.1, duration: 0.5 })
        gsap.to(currentBar.material, { opacity: 1, duration: 0.5 })
      }

      if (currentGlow) {
        gsap.to(currentGlow.material, { opacity: 0.6, duration: 0.5 }) // 更强的发光
      }

      this.activeIndex = (this.activeIndex + 1) % this.data.length
    },

    initCamera() {
      const rate = this.width / this.height
      this.camera = new THREE.PerspectiveCamera(45, rate, 0.1, 1000)
      this.camera.position.set(7, 6, 7) // 调整相机位置更近一些
      this.camera.lookAt(0, 0.5, 0) // 调整目标点，跟随柱状图的新中心
    },

    initRenderer() {
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true, // 启用透明背景
        premultipliedAlpha: false,
      })
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.setSize(this.width, this.height)
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      this.renderer.setClearColor(0x000000, 0) // 设置透明背景
      this.$refs["barDom"].appendChild(this.renderer.domElement)
    },

    initLight() {
      // 主光源 - 增强亮度
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5)
      directionalLight.position.set(10, 10, 5)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      directionalLight.shadow.camera.near = 0.1
      directionalLight.shadow.camera.far = 50
      directionalLight.shadow.camera.left = -10
      directionalLight.shadow.camera.right = 10
      directionalLight.shadow.camera.top = 10
      directionalLight.shadow.camera.bottom = -10

      // 环境光 - 大幅增强亮度
      const ambientLight = new THREE.AmbientLight(0xffffff, 1.2)

      // 蓝色点光源 - 增强亮度
      const pointLight1 = new THREE.PointLight(0x00ffff, 1.5, 20)
      pointLight1.position.set(-5, 8, 5)

      // 青色点光源 - 增强亮度
      const pointLight2 = new THREE.PointLight(0x17e6c3, 1.2, 15)
      pointLight2.position.set(5, 6, -3)

      // 黄色点光源 - 增强亮度
      const pointLight3 = new THREE.PointLight(0xffc472, 1.0, 12)
      pointLight3.position.set(0, 10, 0)

      // 添加额外的填充光
      const fillLight = new THREE.DirectionalLight(0xffffff, 0.8)
      fillLight.position.set(-10, 5, -5)

      this.scene.add(directionalLight)
      this.scene.add(ambientLight)
      this.scene.add(pointLight1)
      this.scene.add(pointLight2)
      this.scene.add(pointLight3)
      this.scene.add(fillLight)
    },

    initAxes() {
      this.axes = new THREE.AxesHelper(0)
      this.scene.add(this.axes)
    },

    initControls() {
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      this.controls.enableZoom = true
      this.controls.enablePan = false
      this.controls.maxPolarAngle = Math.PI / 2.5
      this.controls.minPolarAngle = Math.PI / 6
      this.controls.minDistance = 5 // 调整最小距离，允许更近的视角
      this.controls.maxDistance = 20 // 调整最大距离
      this.controls.target.set(0, 0.5, 0) // 调整控制器目标点
      this.controls.autoRotate = true // 启用自动旋转
      this.controls.autoRotateSpeed = 0.5 // 设置较慢的旋转速度，更优雅
    },

    loop() {
      this.controls.update()

      // 粒子动画
      if (this.particles) {
        const positions = this.particles.geometry.attributes.position.array
        for (let i = 0; i < positions.length; i += 3) {
          positions[i + 1] += 0.01 // Y轴缓慢上升
          if (positions[i + 1] > 10) {
            positions[i + 1] = 0 // 重置到底部
          }
        }
        this.particles.geometry.attributes.position.needsUpdate = true
        this.particles.rotation.y += 0.002
      }

      this.renderer.render(this.scene, this.camera)
      requestAnimationFrame(() => this.loop())
    },

    resize() {
      this.width = this.$refs["barDom"].offsetWidth
      this.height = this.$refs["barDom"].offsetHeight
      this.camera.aspect = this.width / this.height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(this.width, this.height)
    },

    destroy() {
      clearInterval(this.timer)
      if (this.renderer) {
        emptyObject(this.barGroup)
        emptyObject(this.gridGroup)
        if (this.particles) {
          this.scene.remove(this.particles)
          this.particles.geometry.dispose()
          this.particles.material.dispose()
        }
        this.renderer.dispose()
        this.renderer.forceContextLoss()
        this.controls.dispose()
        this.$refs["barDom"].innerHTML = ""
        this.scene = null
        this.camera = null
        this.renderer = null
        this.controls = null
        this.axes = null
        this.particles = null
      }
    },
  },
}
</script>

<style>
.three-bar-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.three-bar {
  width: 100%;
  height: 100%;
}
</style>
