<template>
  <div class="circle-progress-container">
    <div class="progress-grid">
      <div 
        v-for="(item, index) in progressData" 
        :key="index"
        class="progress-item"
      >
        <div class="circle-wrapper">
          <svg class="progress-circle" :width="circleSize" :height="circleSize">
            <!-- 渐变定义 -->
            <defs>
              <linearGradient :id="`gradient-${index}`" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" :stop-color="item.gradientColors[0]" />
                <stop offset="100%" :stop-color="item.gradientColors[1]" />
              </linearGradient>
            </defs>
            <!-- 外圈刻度线 -->
            <g v-for="i in 80" :key="`outer-${i}`">
              <line
                :x1="circleSize / 2 + (radius + 15) * Math.cos((i - 1) * 4.5 * Math.PI / 180)"
                :y1="circleSize / 2 + (radius + 15) * Math.sin((i - 1) * 4.5 * Math.PI / 180)"
                :x2="circleSize / 2 + (radius + 18) * Math.cos((i - 1) * 4.5 * Math.PI / 180)"
                :y2="circleSize / 2 + (radius + 18) * Math.sin((i - 1) * 4.5 * Math.PI / 180)"
                :stroke="item.gradientColors[0]"
                stroke-width="1"
                :opacity="0.8"
              />
            </g>
            <!-- 背景圆环 -->
            <circle
              :cx="circleSize / 2"
              :cy="circleSize / 2"
              :r="radius"
              fill="none"
              :stroke="item.bgColor"
              :stroke-width="strokeWidth"
              stroke-linecap="round"
            />
            <!-- 进度圆环 -->
            <circle
              :cx="circleSize / 2"
              :cy="circleSize / 2"
              :r="radius"
              fill="none"
              :stroke="`url(#gradient-${index})`"
              :stroke-width="strokeWidth"
              stroke-linecap="round"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="getStrokeDashoffset(item.animatedValue)"
              class="progress-stroke"
            />
            <!-- 刻度线 -->
            <g v-for="i in 60" :key="i">
              <line
                :x1="circleSize / 2 + (radius - 8) * Math.cos((i - 1) * 6 * Math.PI / 180)"
                :y1="circleSize / 2 + (radius - 8) * Math.sin((i - 1) * 6 * Math.PI / 180)"
                :x2="circleSize / 2 + (radius - 2) * Math.cos((i - 1) * 6 * Math.PI / 180)"
                :y2="circleSize / 2 + (radius - 2) * Math.sin((i - 1) * 6 * Math.PI / 180)"
                :stroke="i % 5 === 0 ? item.gradientColors[0] : item.tickColor"
                :stroke-width="i % 5 === 0 ? 2 : 1"
                :opacity="i % 5 === 0 ? 0.8 : 0.4"
              />
            </g>
          </svg>
          <!-- 中心数值 -->
          <div class="center-value">
            <span class="value" :style="{ color: item.gradientColors[0] }">
              {{ Math.round(item.animatedValue) }}
            </span>
          </div>
        </div>
        <!-- 标题 -->
        <div class="progress-title" :style="{ color: item.gradientColors[0] }">
          {{ item.title }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import gsap from 'gsap'

export default {
  name: 'CircleProgress',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const circleSize = 108  // 缩小10%（120 * 0.9 = 108）
    const strokeWidth = 5
    const radius = (circleSize - strokeWidth - 30) / 2  // 减去外圈空间
    const circumference = 2 * Math.PI * radius
    
    const progressData = reactive([
      {
        title: '总设备数',
        value: 698,
        animatedValue: 0,
        color: '#00BFFF', // 亮蓝色
        gradientColors: ['#00BFFF', '#0080FF'], // 亮蓝色渐变
        bgColor: 'rgba(0, 191, 255, 0.15)',
        tickColor: 'rgba(0, 191, 255, 0.4)'
      },
      {
        title: '在线数',
        value: 656,
        animatedValue: 0,
        color: '#00E5CC', // 青绿色
        gradientColors: ['#00E5CC', '#00B8A3'], // 青绿色渐变
        bgColor: 'rgba(0, 229, 204, 0.15)',
        tickColor: 'rgba(0, 229, 204, 0.4)'
      },
      {
        title: '告警次数',
        value: 684,
        animatedValue: 0,
        color: '#FF4081', // 粉红色
        gradientColors: ['#FF4081', '#E91E63'], // 粉红色渐变
        bgColor: 'rgba(255, 64, 129, 0.15)',
        tickColor: 'rgba(255, 64, 129, 0.4)'
      }
    ])

    const getStrokeDashoffset = (value) => {
      const maxValue = 1000 // 假设最大值为1000
      const progress = Math.min(value / maxValue, 1)
      return circumference - (progress * circumference)
    }

    const animateProgress = () => {
      progressData.forEach((item, index) => {
        // 重置动画值
        item.animatedValue = 0
        
        // 延迟动画，创建波浪效果
        gsap.to(item, {
          animatedValue: item.value,
          duration: 2,
          delay: index * 0.3,
          ease: "power2.out",
          onComplete: () => {
            // 添加轻微的脉冲效果
            gsap.to(item, {
              animatedValue: item.value * 1.05,
              duration: 0.3,
              yoyo: true,
              repeat: 1,
              ease: "power2.inOut"
            })
          }
        })
      })
    }

    const startContinuousAnimation = () => {
      // 初始动画
      animateProgress()
      
      // 每5秒重复动画
      const interval = setInterval(() => {
        animateProgress()
      }, 5000)
      
      return interval
    }

    let animationInterval = null

    onMounted(() => {
      // 延迟启动动画，确保组件完全渲染
      setTimeout(() => {
        animationInterval = startContinuousAnimation()
      }, 500)
    })

    onBeforeUnmount(() => {
      if (animationInterval) {
        clearInterval(animationInterval)
      }
    })

    return {
      circleSize,
      strokeWidth,
      radius,
      circumference,
      progressData,
      getStrokeDashoffset
    }
  }
}
</script>

<style lang="scss" scoped>
.circle-progress-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start; /* 改为顶部对齐 */
  justify-content: flex-start;
  padding: 10px; /* 减少内边距 */
  padding-left: 10px;
}

.progress-grid {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 130px;
  gap: 35px;
  padding: 0 10px; /* 再减少5px，总共左移15px */
}

.progress-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  padding-top: 15px;
}

.circle-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circle {
  transform: rotate(-90deg);
}

.progress-stroke {
  transition: stroke-dashoffset 0.3s ease;
}

.center-value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.value {
  font-size: 24px;
  font-weight: bold;
  font-family: 'Arial', sans-serif;
}

.progress-title {
  margin-top: -5px; /* 负边距，让文字更靠近圆环 */
  font-size: 12px; /* 稍微缩小字体 */
  font-weight: 600;
  text-align: center;
  letter-spacing: 1px;
  font-family: 'Arial', sans-serif;
  opacity: 1;
  color: inherit;
  white-space: nowrap;
  flex-shrink: 0;
}

// 添加呼吸灯效果
@keyframes breathe {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.progress-item:hover .progress-circle {
  animation: breathe 2s ease-in-out infinite;
}
</style>
