<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="detection-admin" />
        <module name="detection-platform" />
        <module name="detection-quartz" />
        <module name="detection-system" />
        <module name="detection-common" />
        <module name="detection-vnc" />
        <module name="detection-framework" />
        <module name="detection-generator" />
        <module name="detection-api-app" />
      </profile>
    </annotationProcessing>
  </component>
</project>