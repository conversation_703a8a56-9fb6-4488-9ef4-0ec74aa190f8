// 动态字段映射配置 - 通过查询数据库DDL获取字段和备注

import { getBatchTableFields } from '@/api/system/tableStructure'

// 缓存字段映射，避免重复请求
let fieldMappingsCache = {}
let isLoading = false

// 支持的表名列表
const SUPPORTED_TABLES = [
  'sc_service_base_info',
  'sc_service_water_usage',
  'sc_service_wastewater_station',
  'sc_service_equipment_info',
  'sc_service_pipeline_network',
  'sc_service_septic_tank',
  'sc_service_biochemical_system_info',
  'sc_service_control_system',
  'sc_service_secondary_settling_tank_info',
  'sc_service_other_treatment_units',
  'sc_service_discharge_info',
  'sc_service_system_evaluation'
]

// 初始化字段映射
export async function initFieldMappings() {
  if (isLoading || Object.keys(fieldMappingsCache).length > 0) {
    return fieldMappingsCache
  }

  isLoading = true

  try {
    const response = await getBatchTableFields(SUPPORTED_TABLES)
    if (response.code === 200 && response.data) {
      fieldMappingsCache = response.data
      console.log('字段映射获取成功:', fieldMappingsCache)
    }
  } catch (error) {
    console.error('获取字段映射失败:', error)
    // 如果获取失败，使用默认映射
    fieldMappingsCache = getDefaultFieldMappings()
  } finally {
    isLoading = false
  }

  return fieldMappingsCache
}

// 获取默认字段映射（当数据库查询失败时使用）
function getDefaultFieldMappings() {
  return {
    sc_service_base_info: {
      id: 'ID',
      facilityName: '设施名称',
      facilityType: '设施类型',
      branchCompany: '分公司',
      highwayName: '所在高速',
      constructionYear: '建成时间',
      hasInterchange: '互通情况',
      buildingComposition: '建筑物构成',
      remark: '备注',
      createTime: '创建时间',
      updateTime: '更新时间',
      creator: '创建人',
      modifier: '修改人'
    },
    sc_service_water_usage: {
      id: 'ID',
      serviceAreaId: '服务区ID',
      waterSource: '水源',
      dailyUsage: '日常用水量',
      peakUsage: '高峰期用水量',
      remark: '备注',
      createTime: '创建时间',
      updateTime: '更新时间',
      creator: '创建人',
      modifier: '修改人'
    },
    // 其他表的默认映射...
  }

}

// 获取字段的中文名称
export async function getFieldDisplayName(tableName, fieldName) {
  // 确保字段映射已初始化
  const mappings = await initFieldMappings()

  const tableMapping = mappings[tableName]
  if (tableMapping && tableMapping[fieldName]) {
    return tableMapping[fieldName]
  }

  // 如果没有映射，返回原字段名
  return fieldName
}

// 同步版本的获取字段显示名称（用于已经初始化的情况）
export function getFieldDisplayNameSync(tableName, fieldName) {
  const tableMapping = fieldMappingsCache[tableName]
  if (tableMapping && tableMapping[fieldName]) {
    return tableMapping[fieldName]
  }
  return fieldName
}

// 获取字段备注（中文说明）
export function getFieldCommentSync(tableName, fieldName) {
  const tableMapping = fieldMappingsCache[tableName]
  console.log(`获取字段备注: 表=${tableName}, 字段=${fieldName}, 映射=`, tableMapping)

  if (tableMapping && tableMapping[fieldName]) {
    console.log(`找到字段备注: ${fieldName} -> ${tableMapping[fieldName]}`)
    return tableMapping[fieldName]
  }

  console.log(`未找到字段备注，返回原字段名: ${fieldName}`)
  return fieldName
}

// 格式化字段值显示
export function formatFieldValue(fieldName, value) {
  if (value === null || value === undefined) {
    return '-'
  }

  // 特殊字段格式化
  switch (fieldName) {
    case 'hasInterchange':
      return value ? '有' : '无'
    case 'createTime':
    case 'updateTime':
    case 'evaluationDate':
      if (typeof value === 'string' && value.includes('T')) {
        return value.replace('T', ' ').substring(0, 19)
      }
      return value
    case 'dailyUsage':
    case 'peakUsage':
    case 'designCapacity':
    case 'actualCapacity':
      return value + (typeof value === 'number' ? ' t/d' : '')
    case 'pipeDiameter':
      return value + (typeof value === 'number' ? ' mm' : '')
    default:
      return value
  }
}
