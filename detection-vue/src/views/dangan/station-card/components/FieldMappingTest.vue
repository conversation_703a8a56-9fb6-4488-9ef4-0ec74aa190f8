<template>
  <div class="field-mapping-test">
    <el-card header="字段映射测试">
      <el-form :inline="true">
        <el-form-item label="选择表名">
          <el-select v-model="selectedTable" placeholder="请选择表名" @change="loadTableFields">
            <el-option
              v-for="table in tableList"
              :key="table"
              :label="table"
              :value="table"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="refreshMappings">刷新映射</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="fieldList" border v-loading="loading">
        <el-table-column prop="fieldName" label="字段名" width="200" />
        <el-table-column prop="fieldComment" label="字段注释" width="200" />
        <el-table-column prop="dataType" label="数据类型" width="150" />
        <el-table-column label="映射状态" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.fieldComment ? 'success' : 'warning'">
              {{ scope.row.fieldComment ? '已映射' : '未映射' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div style="margin-top: 20px;">
        <h3>字段映射缓存状态</h3>
        <pre>{{ JSON.stringify(fieldMappingsCache, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getTableFields } from '@/api/system/tableStructure'
import { initFieldMappings } from './FieldMappings.js'
import { ElMessage } from 'element-plus'

const selectedTable = ref('')
const fieldList = ref([])
const loading = ref(false)
const fieldMappingsCache = ref({})

const tableList = [
  'sc_service_base_info',
  'sc_service_water_usage',
  'sc_service_wastewater_station',
  'sc_service_equipment_info',
  'sc_service_pipeline_network',
  'sc_service_septic_tank',
  'sc_service_biochemical_system_info',
  'sc_service_control_system',
  'sc_service_secondary_settling_tank_info',
  'sc_service_other_treatment_units',
  'sc_service_discharge_info',
  'sc_service_system_evaluation'
]

const loadTableFields = async () => {
  if (!selectedTable.value) return
  
  loading.value = true
  try {
    const response = await getTableFields(selectedTable.value)
    if (response.code === 200) {
      const fields = response.data
      fieldList.value = Object.keys(fields).map(fieldName => ({
        fieldName,
        fieldComment: fields[fieldName],
        dataType: 'varchar', // 这里可以扩展获取具体数据类型
        mapped: !!fields[fieldName]
      }))
    }
  } catch (error) {
    console.error('获取表字段失败:', error)
    ElMessage.error('获取表字段失败')
  } finally {
    loading.value = false
  }
}

const refreshMappings = async () => {
  try {
    // 清空缓存，强制重新获取
    fieldMappingsCache.value = {}
    const mappings = await initFieldMappings()
    fieldMappingsCache.value = mappings
    ElMessage.success('字段映射刷新成功')
    
    // 如果有选中的表，重新加载字段
    if (selectedTable.value) {
      await loadTableFields()
    }
  } catch (error) {
    console.error('刷新字段映射失败:', error)
    ElMessage.error('刷新字段映射失败')
  }
}

onMounted(async () => {
  await refreshMappings()
})
</script>

<style scoped>
.field-mapping-test {
  padding: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  font-size: 12px;
}
</style>
