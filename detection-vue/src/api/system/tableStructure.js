import request from '@/utils/request'

// 获取表的字段信息和备注
export function getTableFields(tableName) {
  return request({
    url: '/system/tableStructure/fields/' + tableName,
    method: 'get'
  })
}

// 批量获取多个表的字段信息
export function getBatchTableFields(tableNames) {
  return request({
    url: '/system/tableStructure/batchFields',
    method: 'post',
    data: tableNames
  })
}

// 获取表的详细信息
export function getTableInfo(tableName) {
  return request({
    url: '/system/tableStructure/info/' + tableName,
    method: 'get'
  })
}
