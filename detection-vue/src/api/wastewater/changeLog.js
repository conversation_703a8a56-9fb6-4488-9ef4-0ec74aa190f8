import request from '@/utils/request'

// 查询服务区/收费站档案变更日志列表
export function listServiceChangeLog(query) {
  return request({
    url: '/wastewater/changeLog/list',
    method: 'get',
    params: query
  })
}

// 查询服务区/收费站档案变更日志详细
export function getServiceChangeLog(id) {
  return request({
    url: '/wastewater/changeLog/' + id,
    method: 'get'
  })
}

// 根据表名和记录ID查询变更日志列表
export function listChangeLogByRecord(tableName, recordId, query) {
  return request({
    url: '/wastewater/changeLog/listByRecord/' + tableName + '/' + recordId,
    method: 'get',
    params: query
  })
}

// 根据设施名称查询变更日志列表
export function listChangeLogByFacility(facilityName, query) {
  return request({
    url: '/wastewater/changeLog/listByFacility/' + facilityName,
    method: 'get',
    params: query
  })
}

// 根据设施名称和表名查询变更日志列表
export function listChangeLogByFacilityAndTable(facilityName, tableName, query) {
  return request({
    url: '/wastewater/changeLog/listByFacilityAndTable/' + facilityName + '/' + tableName,
    method: 'get',
    params: query
  })
}

// 删除服务区/收费站档案变更日志
export function delServiceChangeLog(id) {
  return request({
    url: '/wastewater/changeLog/' + id,
    method: 'delete'
  })
}

// 导出服务区/收费站档案变更日志
export function exportServiceChangeLog(query) {
  return request({
    url: '/wastewater/changeLog/export',
    method: 'post',
    data: query
  })
}
