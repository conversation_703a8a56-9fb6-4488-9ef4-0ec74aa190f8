package com.tunnel.common.core.domain;

import java.io.Serializable;

/**
 * 通用响应对象
 * 
 * <AUTHOR>
 */
public class Response<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 成功状态码 */
    public static final int SUCCESS = 200;
    
    /** 失败状态码 */
    public static final int ERROR = 500;
    
    /** 状态码 */
    private int code;
    
    /** 返回消息 */
    private String message;
    
    /** 数据对象 */
    private T data;
    
    /** 是否成功 */
    private boolean success;
    
    /**
     * 初始化一个新创建的 Response 对象，使其表示一个空消息。
     */
    public Response() {
    }
    
    /**
     * 初始化一个新创建的 Response 对象
     *
     * @param code 状态码
     * @param message 返回内容
     */
    public Response(int code, String message) {
        this.code = code;
        this.message = message;
        this.success = code == SUCCESS;
    }
    
    /**
     * 初始化一个新创建的 Response 对象
     *
     * @param code 状态码
     * @param message 返回内容
     * @param data 数据对象
     */
    public Response(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = code == SUCCESS;
    }
    
    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static <T> Response<T> success() {
        return Response.success("操作成功");
    }
    
    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static <T> Response<T> success(T data) {
        return Response.success("操作成功", data);
    }
    
    /**
     * 返回成功消息
     *
     * @param message 返回内容
     * @return 成功消息
     */
    public static <T> Response<T> success(String message) {
        return Response.success(message, null);
    }
    
    /**
     * 返回成功消息
     *
     * @param message 返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static <T> Response<T> success(String message, T data) {
        return new Response<>(SUCCESS, message, data);
    }
    
    /**
     * 返回错误消息
     *
     * @return 错误消息
     */
    public static <T> Response<T> error() {
        return Response.error("操作失败");
    }
    
    /**
     * 返回错误消息
     *
     * @param message 返回内容
     * @return 错误消息
     */
    public static <T> Response<T> error(String message) {
        return Response.error(message, null);
    }
    
    /**
     * 返回错误消息
     *
     * @param message 返回内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static <T> Response<T> error(String message, T data) {
        return new Response<>(ERROR, message, data);
    }
    
    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param message 返回内容
     * @return 错误消息
     */
    public static <T> Response<T> error(int code, String message) {
        return new Response<>(code, message, null);
    }
    
    // Getter and Setter methods
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
        this.success = code == SUCCESS;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
}