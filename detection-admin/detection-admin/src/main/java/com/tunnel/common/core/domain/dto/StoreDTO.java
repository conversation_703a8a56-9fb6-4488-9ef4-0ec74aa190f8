package com.tunnel.common.core.domain.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 门店信息DTO
 * 
 * <AUTHOR>
 */
public class StoreDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 门店编码 */
    private String code;
    
    /** 门店名称 */
    private String name;
    
    /** 门店类型 */
    private String type;
    
    /** 门店状态 */
    private String status;
    
    /** 省份编码 */
    private String provinceCode;
    
    /** 省份名称 */
    private String provinceName;
    
    /** 城市编码 */
    private String cityCode;
    
    /** 城市名称 */
    private String cityName;
    
    /** 详细地址 */
    private String address;
    
    /** 联系电话 */
    private String phone;
    
    /** 公司编码 */
    private String companyCode;
    
    /** 加盟商编码 */
    private String franchiseeCode;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新时间 */
    private Date updateTime;
    
    // Getter and Setter methods
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getProvinceCode() {
        return provinceCode;
    }
    
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }
    
    public String getProvinceName() {
        return provinceName;
    }
    
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }
    
    public String getCityCode() {
        return cityCode;
    }
    
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }
    
    public String getCityName() {
        return cityName;
    }
    
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getCompanyCode() {
        return companyCode;
    }
    
    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }
    
    public String getFranchiseeCode() {
        return franchiseeCode;
    }
    
    public void setFranchiseeCode(String franchiseeCode) {
        this.franchiseeCode = franchiseeCode;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}