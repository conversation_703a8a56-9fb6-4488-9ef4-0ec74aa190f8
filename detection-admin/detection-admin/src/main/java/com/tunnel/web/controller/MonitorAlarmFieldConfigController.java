package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonitorAlarmFieldConfig;
import com.tunnel.service.MonitorAlarmFieldConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监测预警指标配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@RestController
@RequestMapping("/detection/field/config")
public class MonitorAlarmFieldConfigController extends BaseController
{
    @Autowired
    private MonitorAlarmFieldConfigService monitorAlarmFieldConfigService;

    /**
     * 查询监测预警指标配置列表
     */
    @PostMapping("/selectByPage")
    public TableDataInfo list(@RequestBody MonitorAlarmFieldConfig monitorAlarmFieldConfig)
    {
        List<MonitorAlarmFieldConfig> list = monitorAlarmFieldConfigService.selectMonitorAlarmFieldConfigList(monitorAlarmFieldConfig);
        return getDataTable(list);
    }

    /**
     * 导出监测预警指标配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @Log(title = "监测预警指标配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonitorAlarmFieldConfig monitorAlarmFieldConfig)
    {
        List<MonitorAlarmFieldConfig> list = monitorAlarmFieldConfigService.selectMonitorAlarmFieldConfigList(monitorAlarmFieldConfig);
        ExcelUtil<MonitorAlarmFieldConfig> util = new ExcelUtil<MonitorAlarmFieldConfig>(MonitorAlarmFieldConfig.class);
        util.exportExcel(response, list, "监测预警指标配置数据");
    }

    /**
     * 获取监测预警指标配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(monitorAlarmFieldConfigService.selectMonitorAlarmFieldConfigById(id));
    }

    /**
     * 新增监测预警指标配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "监测预警指标配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonitorAlarmFieldConfig monitorAlarmFieldConfig)
    {
        return toAjax(monitorAlarmFieldConfigService.insertMonitorAlarmFieldConfig(monitorAlarmFieldConfig));
    }

    /**
     * 修改监测预警指标配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "监测预警指标配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonitorAlarmFieldConfig monitorAlarmFieldConfig)
    {
        return toAjax(monitorAlarmFieldConfigService.updateMonitorAlarmFieldConfig(monitorAlarmFieldConfig));
    }

    /**
     * 删除监测预警指标配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "监测预警指标配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(monitorAlarmFieldConfigService.deleteMonitorAlarmFieldConfigByIds(ids));
    }
}
