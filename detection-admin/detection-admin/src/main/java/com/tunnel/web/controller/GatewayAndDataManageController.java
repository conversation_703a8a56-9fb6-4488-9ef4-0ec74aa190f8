package com.tunnel.web.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.domain.Gateway;
import com.tunnel.domain.SecomeaField;
import com.tunnel.web.controller.dto.GatewayManageConditionDTO;
import com.tunnel.web.service.GatewayAndDataManageService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 网关和数据管理服务
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/gateway/manage")
public class GatewayAndDataManageController extends BaseController
{
    @Resource
    private GatewayAndDataManageService gatewayAndDataManageService;

    /**
     *  网关下拉列表
     */
    @GetMapping("/gateway/downList/list")
    public TableDataInfo gatewayDownListList(GatewayManageConditionDTO gatewayManageCondition)
    {
        List<Gateway> list = gatewayAndDataManageService.gatewayDownListList(gatewayManageCondition);
        return getDataTable(list);
    }

    /**
     *  获取指定网关下所有控制点位
     */
    @GetMapping("/gateway/control/point/all")
    public TableDataInfo gatewayControlPointAll(GatewayManageConditionDTO gatewayManageCondition)
    {
        List<SecomeaField> list = gatewayAndDataManageService.contorlPointAll(gatewayManageCondition);
        return getDataTable(list);
    }

    /**
     *  获取指定网关下所有控制点位
     */
    @GetMapping("/gateway/control/point/real/data/{controlPoint}")
    public TableDataInfo gatewayControlPointRealDataAll(GatewayManageConditionDTO gatewayManageCondition)
    {
        List<Map<String, Object>> list = gatewayAndDataManageService.controlPointRealDataList(gatewayManageCondition);
        return getDataTable(list);
    }


}
