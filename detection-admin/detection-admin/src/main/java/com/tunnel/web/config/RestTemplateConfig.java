package com.tunnel.web.config;

import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * 支持Nacos服务发现和负载均衡
 * 
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 创建支持负载均衡的RestTemplate
     * @LoadBalanced注解使RestTemplate支持服务发现
     */
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * 创建不支持负载均衡的RestTemplate（用于直接URL调用）
     */
    @Bean("directRestTemplate")
    public RestTemplate directRestTemplate() {
        return new RestTemplate();
    }
}