package com.tunnel.web.service;

import com.tunnel.domain.MonitorFactor;
import com.tunnel.domain.MonitorStation;
import com.tunnel.web.controller.dto.SignalPointConditionDTO;

import java.util.List;
import java.util.Map;

/**
 * 单点监测 服务
 *
 * <AUTHOR>
 * @date 2025/8/14  17:48
 * @since 1.0.0
 */
public interface SignalPointCheckService {

    List<MonitorStation> selectMonitorStationList(SignalPointConditionDTO signalPointCondition);

    List<MonitorFactor> selectMonitorFactorList(SignalPointConditionDTO signalPointCondition);

    List<Map<String, Object>> selectMonitorFactorDataList(SignalPointConditionDTO signalPointCondition);

}
