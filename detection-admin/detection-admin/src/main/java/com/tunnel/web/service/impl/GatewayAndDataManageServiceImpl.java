package com.tunnel.web.service.impl;

import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.exception.user.ParamRequireException;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.domain.Gateway;
import com.tunnel.domain.ProcessingGatewayUserRel;
import com.tunnel.domain.SecomeaField;
import com.tunnel.mapper.GatewayMapper;
import com.tunnel.service.GatewayService;
import com.tunnel.service.ProcessingGatewayUserRelService;
import com.tunnel.service.SecomeaFieldService;
import com.tunnel.service.RequirementInfoMergeService;
import com.tunnel.web.controller.dto.GatewayManageConditionDTO;
import com.tunnel.web.remote.dto.UrlResult;
import com.tunnel.web.remote.facade.VncServiceFacade;
import com.tunnel.web.service.GatewayAndDataManageService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/14  15:23
 * @since 1.0.0
 */
@Service
public class GatewayAndDataManageServiceImpl implements GatewayAndDataManageService {

    @Resource
    private GatewayService gatewayService;
    @Resource
    private ProcessingGatewayUserRelService processingGatewayUserRelService;
    @Resource
    private SecomeaFieldService secomeaFieldService;
    @Resource
    private RequirementInfoMergeService requirementInfoMergeService;
    @Resource
    private VncServiceFacade vncServiceFacade;
    @Resource
    private GatewayMapper gatewayMapper;

    @Override
    public List<Gateway> gatewayDownListList(GatewayManageConditionDTO gatewayManageCondition) {
        gatewayManageCondition.check();
        List<Gateway> gatewayList = gatewayService.selectProcessingGatewayAll();
        if (CollectionUtils.isEmpty(gatewayList)) {
            return Collections.emptyList();
        }

        ProcessingGatewayUserRel processingGatewayUserRel = ProcessingGatewayUserRel.builder().userName(gatewayManageCondition.getUserName()).build();
        List<ProcessingGatewayUserRel> processingGatewayUserRelList = processingGatewayUserRelService.selectProcessingGatewayUserRelList(processingGatewayUserRel);
        if (CollectionUtils.isEmpty(processingGatewayUserRelList)) {
            return Collections.emptyList();
        }

        Map<String, ProcessingGatewayUserRel> processingGatewayUserRelMap = processingGatewayUserRelList.stream().collect(Collectors.toMap(ProcessingGatewayUserRel::getCode, Function.identity(), (v1, v2) -> v1));
        gatewayList.removeIf(n -> !processingGatewayUserRelMap.containsKey(n.getCode()));
        return gatewayList;
    }

    @Override
    public List<SecomeaField> contorlPointAll(GatewayManageConditionDTO gatewayManageCondition) {
        gatewayManageCondition.check();
        if (StringUtils.isEmpty(gatewayManageCondition.getProcessingGatewayCode())) {
            throw new ParamRequireException("网关编码不能为空");
        }

        SecomeaField secomeaField = SecomeaField.builder().processingGatewayCode(gatewayManageCondition.getProcessingGatewayCode())
                .monitorType(gatewayManageCondition.getMonitorType()).build();
        List<SecomeaField> list =secomeaFieldService.selectSecomeaFieldList(secomeaField);
        return list;
    }

    @Override
    public List<Map<String, Object>> controlPointRealDataList(GatewayManageConditionDTO gatewayManageCondition) {
        gatewayManageCondition.check();
        if (StringUtils.isEmpty(gatewayManageCondition.getProcessingGatewayCode())) {
            throw new RuntimeException("网关编码不能为空");
        }
        if (StringUtils.isEmpty(gatewayManageCondition.getPointAddr())) {
            throw new RuntimeException("控制点位不能为空");
        }
        if (gatewayManageCondition.getStartDateTime() == null && gatewayManageCondition.getEndDateTime() == null) {
            throw new RuntimeException("开始时间和结束时间至少一个有值");
        }
        SecomeaField secomeaFieldQuery = SecomeaField.builder().processingGatewayCode(gatewayManageCondition.getPointAddr())
                .monitorType(gatewayManageCondition.getMonitorType()).build();
        List<SecomeaField> list =secomeaFieldService.selectSecomeaFieldList(secomeaFieldQuery);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        SecomeaField secomeaField = list.get(0);

        String field = secomeaField.getPointAddr().toLowerCase().replaceAll("\\.", "_");

        return requirementInfoMergeService.selectRequirementReportMap(gatewayManageCondition.getStartDateTime(), gatewayManageCondition.getEndDateTime(), field);
    }

    @Override
    public UrlResult getVncUrl(String mac) {
        if (StringUtils.isEmpty(mac)) {
            throw new RuntimeException("网关编码不能为空");
        }
        List<Gateway> gatewayList = gatewayMapper.selectByCode(mac);
        if (CollectionUtils.isEmpty(gatewayList)) {
            throw new ServiceException("网关不存在");
        }
        return vncServiceFacade.getVncUrl(mac);
    }
}
