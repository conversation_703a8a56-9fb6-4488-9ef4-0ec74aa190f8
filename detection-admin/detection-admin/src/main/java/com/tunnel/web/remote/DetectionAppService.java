package com.tunnel.web.remote;

import com.alibaba.fastjson2.JSONObject;
import com.tunnel.common.core.domain.Response;
import com.tunnel.web.remote.dto.RequirementRefreshDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Detection API服务调用客户端
 * 支持服务发现和Token传递
 * 
 * <AUTHOR>
 */
@FeignClient(name = "detection-api-app", configuration = com.tunnel.web.config.FeignConfig.class)
public interface DetectionAppService {

    /**
     * 刷新需求数据
     * @param data 请求数据
     * @return 响应结果
     */
    @PostMapping("/requirement/refresh")
    Response<String> refreshRequirement(@RequestBody RequirementRefreshDto data);

    /**
     * 设备上线
     * @param request 设备连接请求
     * @return 响应结果
     */
    @PostMapping("/device/connection/online")
    Response<String> deviceOnline(@RequestBody Object request);

    /**
     * 设备离线
     * @param request 设备连接请求
     * @return 响应结果
     */
    @PostMapping("/device/connection/offline")
    Response<String> deviceOffline(@RequestBody Object request);
}