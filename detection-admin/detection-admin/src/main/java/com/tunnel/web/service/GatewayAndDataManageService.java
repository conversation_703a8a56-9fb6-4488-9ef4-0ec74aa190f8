package com.tunnel.web.service;

import com.tunnel.domain.Gateway;
import com.tunnel.domain.SecomeaField;
import com.tunnel.web.controller.dto.GatewayManageConditionDTO;
import com.tunnel.web.remote.dto.UrlResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/14  15:23
 * @since 1.0.0
 */
public interface GatewayAndDataManageService {

    List<Gateway> gatewayDownListList(GatewayManageConditionDTO gatewayManageCondition);

    List<SecomeaField> contorlPointAll(GatewayManageConditionDTO gatewayManageCondition);

    List<Map<String, Object>> controlPointRealDataList(GatewayManageConditionDTO gatewayManageCondition);

    UrlResult getVncUrl(String code);
}
