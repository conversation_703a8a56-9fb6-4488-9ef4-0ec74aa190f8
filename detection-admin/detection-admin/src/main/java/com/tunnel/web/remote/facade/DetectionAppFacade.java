package com.tunnel.web.remote.facade;

import com.alibaba.fastjson2.JSON;
import com.tunnel.common.core.domain.Response;
import com.tunnel.web.remote.DetectionAppService;
import com.tunnel.web.remote.dto.RequirementRefreshDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/5  18:15
 * @since 1.0.0
 */
@Slf4j
@Service
public class DetectionAppFacade {

    // 注入Feign客户端
    @Autowired
    private DetectionAppService detectionAppService;

    @Async
    public void requirementRefresh(RequirementRefreshDto requirementRefresh) {
        log.info("请求参数: {}", JSON.toJSONString(requirementRefresh));
        Response<String> response = detectionAppService.refreshRequirement(requirementRefresh);
        log.info("Feign调用返回结果: {}", response);
    }

    @Async
    public void deviceOnline(String deviceId, String topic, String dataFrom) {
        log.info("设备上线请求: deviceId={}, topic={}, dataFrom={}", deviceId, topic, dataFrom);
        try {
            DeviceConnectionRequest request = new DeviceConnectionRequest();
            request.setDeviceId(deviceId);
            request.setTopic(topic);
            request.setDataFrom(dataFrom);
            Response<String> response = detectionAppService.deviceOnline(request);
            log.info("设备上线调用返回结果: {}", response);
        } catch (Exception e) {
            log.error("设备上线调用失败", e);
        }
    }

    @Async
    public void deviceOffline(String deviceId, String topic, String dataFrom) {
        log.info("设备离线请求: deviceId={}, topic={}, dataFrom={}", deviceId, topic, dataFrom);
        try {
            DeviceConnectionRequest request = new DeviceConnectionRequest();
            request.setDeviceId(deviceId);
            request.setTopic(topic);
            request.setDataFrom(dataFrom);
            Response<String> response = detectionAppService.deviceOffline(request);
            log.info("设备离线调用返回结果: {}", response);
        } catch (Exception e) {
            log.error("设备离线调用失败", e);
        }
    }

    /**
     * 设备连接请求DTO
     */
    public static class DeviceConnectionRequest {
        private String deviceId;
        private String topic;
        private String dataFrom;

        // Getters and Setters
        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public String getTopic() {
            return topic;
        }

        public void setTopic(String topic) {
            this.topic = topic;
        }

        public String getDataFrom() {
            return dataFrom;
        }

        public void setDataFrom(String dataFrom) {
            this.dataFrom = dataFrom;
        }
    }
}
