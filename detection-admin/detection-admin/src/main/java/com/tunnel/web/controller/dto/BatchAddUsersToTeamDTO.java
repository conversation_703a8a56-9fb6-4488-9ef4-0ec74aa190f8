package com.tunnel.web.controller.dto;

/**
 * 批量添加用户到分组请求对象
 */
public class BatchAddUsersToTeamDTO {
    private Long teamId;
    private Long[] userIds;

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long[] getUserIds() {
        return userIds;
    }

    public void setUserIds(Long[] userIds) {
        this.userIds = userIds;
    }
}
