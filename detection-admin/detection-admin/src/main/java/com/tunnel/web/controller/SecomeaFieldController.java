package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.SecomeaField;
import com.tunnel.service.SecomeaFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 控制点位Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/field")
public class SecomeaFieldController extends BaseController
{
    @Autowired
    private SecomeaFieldService secomeaFieldService;

    /**
     * 查询控制点位列表
     */
    @PreAuthorize("@ss.hasPermi('domain:field:list')")
    @GetMapping("/list")
    public TableDataInfo list(SecomeaField secomeaField)
    {
        startPage();
        List<SecomeaField> list = secomeaFieldService.selectSecomeaFieldList(secomeaField);
        return getDataTable(list);
    }

    /**
     * 导出控制点位列表
     */
    @PreAuthorize("@ss.hasPermi('domain:field:export')")
    @Log(title = "控制点位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecomeaField secomeaField)
    {
        List<SecomeaField> list = secomeaFieldService.selectSecomeaFieldList(secomeaField);
        ExcelUtil<SecomeaField> util = new ExcelUtil<SecomeaField>(SecomeaField.class);
        util.exportExcel(response, list, "控制点位数据");
    }

    /**
     * 获取控制点位详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:field:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(secomeaFieldService.selectSecomeaFieldById(id));
    }

    /**
     * 新增控制点位
     */
    @PreAuthorize("@ss.hasPermi('domain:field:add')")
    @Log(title = "控制点位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecomeaField secomeaField)
    {
        return toAjax(secomeaFieldService.insertSecomeaField(secomeaField));
    }

    /**
     * 修改控制点位
     */
    @PreAuthorize("@ss.hasPermi('domain:field:edit')")
    @Log(title = "控制点位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecomeaField secomeaField)
    {
        return toAjax(secomeaFieldService.updateSecomeaField(secomeaField));
    }

    /**
     * 删除控制点位
     */
    @PreAuthorize("@ss.hasPermi('domain:field:remove')")
    @Log(title = "控制点位", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(secomeaFieldService.deleteSecomeaFieldByIds(ids));
    }
}
