package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonitorAlarmFactorConfig;
import com.tunnel.service.MonitorAlarmFactorConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监测预警配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/factor/config")
public class MonitorAlarmFactorConfigController extends BaseController
{
    @Autowired
    private MonitorAlarmFactorConfigService monitorAlarmFactorConfigService;

    /**
     * 查询监测预警配置列表
     */
    @PostMapping("/selectByPage")
    public TableDataInfo selectByPage(@RequestBody MonitorAlarmFactorConfig monitorAlarmFactorConfig)
    {
        List<MonitorAlarmFactorConfig> list = monitorAlarmFactorConfigService.selectMonitorAlarmConfigList(monitorAlarmFactorConfig);
        return getDataTable(list);
    }

    /**
     * 导出监测预警配置列表
     */
    @PreAuthorize("@ss.hasPermi('domain:config:export')")
    @Log(title = "监测预警配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonitorAlarmFactorConfig monitorAlarmFactorConfig)
    {
        List<MonitorAlarmFactorConfig> list = monitorAlarmFactorConfigService.selectMonitorAlarmConfigList(monitorAlarmFactorConfig);
        ExcelUtil<MonitorAlarmFactorConfig> util = new ExcelUtil<MonitorAlarmFactorConfig>(MonitorAlarmFactorConfig.class);
        util.exportExcel(response, list, "监测预警配置数据");
    }

    /**
     * 获取监测预警配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(monitorAlarmFactorConfigService.selectMonitorAlarmConfigById(id));
    }

    /**
     * 新增监测预警配置
     */
    @PreAuthorize("@ss.hasPermi('domain:config:add')")
    @Log(title = "监测预警配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonitorAlarmFactorConfig monitorAlarmFactorConfig)
    {
        return toAjax(monitorAlarmFactorConfigService.insertMonitorAlarmConfig(monitorAlarmFactorConfig));
    }

    /**
     * 修改监测预警配置
     */
    @PreAuthorize("@ss.hasPermi('domain:config:edit')")
    @Log(title = "监测预警配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonitorAlarmFactorConfig monitorAlarmFactorConfig)
    {
        return toAjax(monitorAlarmFactorConfigService.updateMonitorAlarmConfig(monitorAlarmFactorConfig));
    }

    /**
     * 删除监测预警配置
     */
    @PreAuthorize("@ss.hasPermi('domain:config:remove')")
    @Log(title = "监测预警配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(monitorAlarmFactorConfigService.deleteMonitorAlarmConfigByIds(ids));
    }
}
