package com.tunnel.web.controller.common;

import com.alibaba.fastjson2.JSONObject;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.exception.base.BaseException;
import com.tunnel.common.utils.file.AliYunOSSFileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartFile;

@Api("通用上传")
@RestController
@RequestMapping("/report/upload")
public class UploadController {

    @ApiOperation(value = "上传图片")
    @PostMapping("/uploadPicture")
    public AjaxResult downloadImportTemplate(MultipartFile file){
        JSONObject jsonObject =null;
        try {
            jsonObject= AliYunOSSFileUtil.uploadPicture(file, 0);
        }catch (MaxUploadSizeExceededException e){
            throw new BaseException("超过最大限制(kb):"+e.getMaxUploadSize()/1024);
        }catch (Exception e){
            throw new BaseException("上传失败");
        }
        return AjaxResult.success(String.valueOf(jsonObject.get("filePath")));
    }
}
