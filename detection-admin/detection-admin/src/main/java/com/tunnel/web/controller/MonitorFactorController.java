package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonitorFactor;
import com.tunnel.service.MonitorFactorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监测因子Controller
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/factor")
public class MonitorFactorController extends BaseController {
    @Autowired
    private MonitorFactorService monitorFactorService;

    /**
     * 查询监测因子列表
     */
    @PreAuthorize("@ss.hasPermi('domain:factor:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody MonitorFactor monitorFactor) {
        startPage();
        List<MonitorFactor> list = monitorFactorService.selectMonitorFactorList(monitorFactor);
        return getDataTable(list);
    }

    /**
     * 导出监测因子列表
     */
    @PreAuthorize("@ss.hasPermi('domain:factor:export')")
    @Log(title = "监测因子", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonitorFactor monitorFactor) {
        List<MonitorFactor> list = monitorFactorService.selectMonitorFactorList(monitorFactor);
        ExcelUtil<MonitorFactor> util = new ExcelUtil<MonitorFactor>(MonitorFactor.class);
        util.exportExcel(response, list, "监测因子数据");
    }

    /**
     * 获取监测因子详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:factor:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(monitorFactorService.selectMonitorFactorById(id));
    }

    /**
     * 新增监测因子
     */
    @PreAuthorize("@ss.hasPermi('domain:factor:add')")
    @Log(title = "监测因子", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonitorFactor monitorFactor) {
        return toAjax(monitorFactorService.insertMonitorFactor(monitorFactor));
    }

    /**
     * 修改监测因子
     */
    @PreAuthorize("@ss.hasPermi('domain:factor:edit')")
    @Log(title = "监测因子", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonitorFactor monitorFactor) {
        return toAjax(monitorFactorService.updateMonitorFactor(monitorFactor));
    }

    /**
     * 删除监测因子
     */
    @PreAuthorize("@ss.hasPermi('domain:factor:remove')")
    @Log(title = "监测因子", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(monitorFactorService.deleteMonitorFactorByIds(ids));
    }
}
