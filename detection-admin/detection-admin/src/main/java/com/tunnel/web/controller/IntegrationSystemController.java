package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.IntegrationSystem;
import com.tunnel.service.IntegrationSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 系统信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/system")
public class IntegrationSystemController extends BaseController
{
    @Autowired
    private IntegrationSystemService integrationSystemService;

    /**
     * 查询系统信息列表
     */
    @PostMapping("/selectByPage")
    public TableDataInfo selectByPage(@RequestBody IntegrationSystem integrationSystem)
    {
        List<IntegrationSystem> list = integrationSystemService.selectIntegrationSystemList(integrationSystem);
        return getDataTable(list);
    }

    @GetMapping("/list")
    public TableDataInfo list(@RequestBody IntegrationSystem integrationSystem)
    {
        integrationSystem.setPageNum(null);
        integrationSystem.setPageSize(null);
        List<IntegrationSystem> list = integrationSystemService.selectIntegrationSystemList(integrationSystem);
        return getDataTable(list);
    }

    @PostMapping("/listByMonitorStationCode")
    public TableDataInfo listByMonitorStationCode(@RequestBody IntegrationSystem integrationSystem)
    {
        List<IntegrationSystem> list = integrationSystemService.selectIntegrationSystemList(integrationSystem);
        return getDataTable(list);
    }

    /**
     * 导出系统信息列表
     */
    @PreAuthorize("@ss.hasPermi('domain:system:export')")
    @Log(title = "系统信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IntegrationSystem integrationSystem)
    {
        List<IntegrationSystem> list = integrationSystemService.selectIntegrationSystemList(integrationSystem);
        ExcelUtil<IntegrationSystem> util = new ExcelUtil<IntegrationSystem>(IntegrationSystem.class);
        util.exportExcel(response, list, "系统信息数据");
    }

    /**
     * 获取系统信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:system:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(integrationSystemService.selectIntegrationSystemById(id));
    }

    /**
     * 新增系统信息
     */
    @PreAuthorize("@ss.hasPermi('domain:system:add')")
    @Log(title = "系统信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IntegrationSystem integrationSystem)
    {
        return toAjax(integrationSystemService.insertIntegrationSystem(integrationSystem));
    }

    /**
     * 修改系统信息
     */
    @PreAuthorize("@ss.hasPermi('domain:system:edit')")
    @Log(title = "系统信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IntegrationSystem integrationSystem)
    {
        return toAjax(integrationSystemService.updateIntegrationSystem(integrationSystem));
    }

    /**
     * 删除系统信息
     */
    @PreAuthorize("@ss.hasPermi('domain:system:remove')")
    @Log(title = "系统信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(integrationSystemService.deleteIntegrationSystemByIds(ids));
    }
}
