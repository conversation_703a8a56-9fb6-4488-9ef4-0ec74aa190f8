package com.tunnel.web.config;

import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Feign配置类
 * 支持Token传递和服务发现
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class FeignConfig {

    @Value("${token.header}")
    private String tokenHeader;

    /**
     * Feign请求拦截器 - 自动传递Token
     */
    @Bean
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            // 获取当前请求的Token
            String token = getTokenFromRequest();
            if (StringUtils.isNotEmpty(token)) {
                // 将Token添加到Feign请求头中
                requestTemplate.header(tokenHeader, token);
                log.debug("Feign请求添加Token: {}", tokenHeader);
            } else {
                log.debug("当前请求无Token，跳过Token传递");
            }
            // 添加其他通用请求头
            requestTemplate.header("Content-Type", "application/json;charset=UTF-8");
        };
    }

    /**
     * 从当前请求中获取Token
     */
    private String getTokenFromRequest() {
        try {
            // 方式1: 从Spring Security上下文获取
            if (SecurityUtils.getAuthentication() != null) {
                // 这里可以从SecurityContext中获取Token
                // 具体实现需要根据你的TokenService来调整
            }
            // 方式2: 从当前HTTP请求中获取
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String token = request.getHeader(tokenHeader);
                if (StringUtils.isNotEmpty(token)) {
                    return token;
                }
                
                // 尝试从Bearer格式中提取
                String bearerToken = request.getHeader("Authorization");
                if (StringUtils.isNotEmpty(bearerToken) && bearerToken.startsWith("Bearer ")) {
                    return bearerToken;
                }
            }
        } catch (Exception e) {
            log.warn("获取Token失败: {}", e.getMessage());
        }
        
        return null;
    }
}