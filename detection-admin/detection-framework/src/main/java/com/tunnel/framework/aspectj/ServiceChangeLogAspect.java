package com.tunnel.framework.aspectj;

import com.alibaba.fastjson2.JSON;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.spring.SpringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务区/收费站档案变更日志记录切面
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Aspect
@Component
public class ServiceChangeLogAspect {
    
    private static final Logger log = LoggerFactory.getLogger(ServiceChangeLogAspect.class);
    
    // 线程本地变量，用于存储操作前的数据
    private static final ThreadLocal<Map<String, Object>> BEFORE_DATA = new ThreadLocal<>();

    /**
     * 定义切点：服务区/收费站相关的增删改操作
     */
    @Pointcut("execution(* com.tunnel.service.impl.WastewaterFacilityServiceImpl.insertServiceBaseInfo(..))" +
              "|| execution(* com.tunnel.service.impl.WastewaterFacilityServiceImpl.updateServiceBaseInfo(..))" +
              "|| execution(* com.tunnel.service.impl.WastewaterFacilityServiceImpl.deleteServiceBaseInfoByIds(..))")
    public void serviceChangePointcut() {}

    /**
     * 在方法执行前记录原始数据
     */
    @Before("serviceChangePointcut()")
    public void doBefore(JoinPoint joinPoint) {
        try {
            String methodName = joinPoint.getSignature().getName();
            Object[] args = joinPoint.getArgs();
            
            Map<String, Object> beforeData = new HashMap<>();
            beforeData.put("methodName", methodName);
            beforeData.put("args", args);
            
            // 如果是更新或删除操作，需要先查询原始数据
            if (methodName.contains("update") || methodName.contains("delete")) {
                Object originalData = getOriginalData(joinPoint, args);
                beforeData.put("originalData", originalData);
            }
            
            BEFORE_DATA.set(beforeData);
        } catch (Exception e) {
            log.error("记录变更前数据失败", e);
        }
    }

    /**
     * 在方法执行后记录变更日志
     */
    @AfterReturning(pointcut = "serviceChangePointcut()", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, Object result) {
        try {
            Map<String, Object> beforeData = BEFORE_DATA.get();
            if (beforeData == null) {
                return;
            }
            
            String methodName = (String) beforeData.get("methodName");
            Object[] args = (Object[]) beforeData.get("args");
            
            // 判断操作是否成功
            if (!isOperationSuccess(result)) {
                return;
            }
            
            // 记录变更日志
            recordChangeLog(methodName, args, beforeData.get("originalData"));
            
        } catch (Exception e) {
            log.error("记录变更日志失败", e);
        } finally {
            BEFORE_DATA.remove();
        }
    }

    /**
     * 获取原始数据
     */
    private Object getOriginalData(JoinPoint joinPoint, Object[] args) {
        try {
            String methodName = joinPoint.getSignature().getName();
            String className = joinPoint.getTarget().getClass().getSimpleName();

            // 根据不同的方法和参数类型获取原始数据
            if (className.contains("WastewaterFacilityServiceImpl")) {
                if (methodName.contains("update") && args.length > 0) {
                    Object entity = args[0];
                    if (entity != null) {
                        // 通过反射获取ID
                        try {
                            Method getIdMethod = entity.getClass().getMethod("getId");
                            Object id = getIdMethod.invoke(entity);
                            if (id != null) {
                                // 这里需要调用查询方法获取原始数据
                                // 由于循环依赖问题，暂时返回null，在实际使用中需要优化
                                return null;
                            }
                        } catch (Exception e) {
                            // 忽略反射异常
                        }
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.error("获取原始数据失败", e);
            return null;
        }
    }

    /**
     * 判断操作是否成功
     */
    private boolean isOperationSuccess(Object result) {
        if (result instanceof Integer) {
            return (Integer) result > 0;
        }
        return result != null;
    }

    /**
     * 记录变更日志
     */
    private void recordChangeLog(String methodName, Object[] args, Object originalData) {
        try {
            Object changeLogService = SpringUtils.getBean("serviceChangeLogServiceImpl");

            String tableName = getTableName(methodName, args);
            String recordId = getRecordId(args);
            String operationType = getOperationType(methodName);
            String oldValues = originalData != null ? JSON.toJSONString(originalData) : null;
            String newValues = getNewValues(args);
            String remark = generateRemark(methodName, tableName);

            if (tableName != null && recordId != null && changeLogService != null) {
                Long operatorId = null;
                try {
                    operatorId = SecurityUtils.getUserId();
                } catch (Exception e) {
                    // 获取不到用户ID时使用null
                }

                // 使用反射调用recordChangeLog方法
                Method recordMethod = changeLogService.getClass().getMethod("recordChangeLog",
                    String.class, String.class, String.class, String.class, String.class, String.class, Long.class);
                recordMethod.invoke(changeLogService, tableName, recordId, operationType,
                                  oldValues, newValues, remark, operatorId);
            }

        } catch (Exception e) {
            log.error("记录变更日志失败", e);
        }
    }

    /**
     * 获取表名
     */
    private String getTableName(String methodName, Object[] args) {
        if (args.length > 0) {
            Object firstArg = args[0];
            String className = firstArg.getClass().getSimpleName();

            switch (className) {
                case "ServiceBaseInfo": return "sc_service_base_info";
                case "ServiceWaterUsage": return "sc_service_water_usage";
                case "ServiceWastewaterStation": return "sc_service_wastewater_station";
                case "ServiceEquipmentInfo": return "sc_service_equipment_info";
                case "ServicePipelineNetwork": return "sc_service_pipeline_network";
                case "ServiceSepticTank": return "sc_service_septic_tank";
                case "ServiceBiochemicalSystemInfo": return "sc_service_biochemical_system_info";
                case "ServiceControlSystem": return "sc_service_control_system";
                case "ServiceSecondarySettlingTankInfo": return "sc_service_secondary_settling_tank_info";
                case "ServiceOtherTreatmentUnits": return "sc_service_other_treatment_units";
                case "ServiceDischargeInfo": return "sc_service_discharge_info";
                case "ServiceSystemEvaluation": return "sc_service_system_evaluation";
                default: return null;
            }
        }
        return null;
    }

    /**
     * 获取记录ID
     */
    private String getRecordId(Object[] args) {
        if (args.length > 0) {
            Object firstArg = args[0];
            
            // 如果是删除操作，参数可能是ID数组
            if (firstArg instanceof Long[]) {
                Long[] ids = (Long[]) firstArg;
                if (ids.length > 0) {
                    return String.valueOf(ids[0]); // 取第一个ID
                }
            } else if (firstArg instanceof Long) {
                return String.valueOf(firstArg);
            }
            
            // 通过反射获取ID字段
            try {
                Method getIdMethod = firstArg.getClass().getMethod("getId");
                Object id = getIdMethod.invoke(firstArg);
                return id != null ? String.valueOf(id) : null;
            } catch (Exception e) {
                // 忽略异常
            }
        }
        return null;
    }

    /**
     * 获取操作类型
     */
    private String getOperationType(String methodName) {
        if (methodName.contains("insert") || methodName.contains("add")) {
            return "INSERT";
        } else if (methodName.contains("update") || methodName.contains("edit")) {
            return "UPDATE";
        } else if (methodName.contains("delete") || methodName.contains("remove")) {
            return "DELETE";
        }
        return "UNKNOWN";
    }

    /**
     * 获取新值
     */
    private String getNewValues(Object[] args) {
        if (args.length > 0 && args[0] != null) {
            // 如果是删除操作，不记录新值
            Object firstArg = args[0];
            if (firstArg instanceof Long[] || firstArg instanceof Long) {
                return null;
            }
            return JSON.toJSONString(firstArg);
        }
        return null;
    }

    /**
     * 生成备注
     */
    private String generateRemark(String methodName, String tableName) {
        String operation = getOperationType(methodName);
        String tableDesc = getTableDescription(tableName);
        return String.format("%s%s", operation.equals("INSERT") ? "新增" : 
                           operation.equals("UPDATE") ? "修改" : "删除", tableDesc);
    }

    /**
     * 获取表描述
     */
    private String getTableDescription(String tableName) {
        if (tableName == null) return "数据";
        
        switch (tableName) {
            case "sc_service_base_info": return "基本信息";
            case "sc_service_water_usage": return "用水情况";
            case "sc_service_wastewater_station": return "污水处理站";
            case "sc_service_equipment_info": return "设备信息";
            case "sc_service_pipeline_network": return "管网信息";
            case "sc_service_septic_tank": return "化粪池/隔油池";
            case "sc_service_biochemical_system_info": return "生化系统";
            case "sc_service_control_system": return "控制系统";
            case "sc_service_secondary_settling_tank_info": return "二沉池";
            case "sc_service_other_treatment_units": return "其他处理单元";
            case "sc_service_discharge_info": return "排放信息";
            case "sc_service_system_evaluation": return "系统评价";
            default: return "数据";
        }
    }
}
