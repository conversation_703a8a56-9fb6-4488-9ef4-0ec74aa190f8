package com.tunnel.biz.dto;

import lombok.Data;

/**
 * <AUTHOR>  app道路接口
 * 2025年07月13日 11:10
 * 上报统一对象
 */

@Data
public class ReportDTO {
    /**
     * 上报类型
     * 0: 桥梁
     * 1: 隧道
     * 2: 涵洞
     * 3: 边坡
     * 4: 路面
     */
    private Integer type;

    /**
     * 记录ID
     * 新增时为null，修改时必填
     */
    private Long id;

    /**
     * 上报数据
     * 根据type不同，分别对应不同的实体类数据
     */
    private Object data;

}
