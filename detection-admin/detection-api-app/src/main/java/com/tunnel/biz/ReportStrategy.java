package com.tunnel.biz;


import com.tunnel.biz.dto.QueryReportDTO;
import com.tunnel.domain.dto.AuditRequest;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportStrategy {

    /**
     * 新增上报数据
     * @param data 数据
     * @return 是否成功
     */
    Boolean create(Object data);

    /**
     * 修改上报数据
     * @param id 修改id
     * @param data 数据
     * @return 是否成功
     */
    Boolean update(Long id, Object data);

    /**
     * 删除记录
     * @param id 记录ID
     * @return 删除结果
     */
    Boolean delete(Long id);
    
    /**
     * 分页查询数据
     *
     * @param queryReportDTO 查询参数（包含companyName、type、roadCodeOrName等）
     * @return 分页数据
     */
    List<Object> query(QueryReportDTO queryReportDTO);


    /**
     * 分页查询维修数据
     *
     * @param queryReportDTO 查询参数（包含companyName、type、roadCodeOrName等）
     * @return 分页数据
     */
    List<Object> queryRepair(QueryReportDTO queryReportDTO);


    void audit(AuditRequest auditRequest);
}
