package com.tunnel.biz.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tunnel.biz.ReportStrategy;
import com.tunnel.biz.dto.QueryReportDTO;
import com.tunnel.domain.dto.AuditRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2025年07月13日 11:15
 * 涵洞信息上报
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HoleReportStrategy implements ReportStrategy {

    @Resource
    private final ObjectMapper objectMapper;

//    @Resource
//    private CheckHoleService checkHoleService;
//
//    @Resource
//    private RepairRecordService repairRecordService;

    @Override
    public Boolean create(Object data) {
        try {
//            CheckHole checkHole = objectMapper.convertValue(data, CheckHole.class);
//            int record = checkHoleService.insertCheckHole(checkHole);
//            return record > 0;
            return true;
        } catch (Exception e) {
            log.error("涵洞信息上报新增失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean update(Long id, Object data) {
        try {
//            CheckHole checkHole = objectMapper.convertValue(data, CheckHole.class);
//            checkHole.setId(id);
//            //查询当前记录
//            CheckHole hole = checkHoleService.selectCheckHoleById(id);
//            if(Objects.isNull(hole)){
//                throw new ServiceException("检测信息不存在");
//            }
//            if(Objects.equals(hole.getCheckStatus(), 2)){
//                checkHole.setCheckStatus(0);
//                checkHole.setRemark("");
//            }
//            int record = checkHoleService.updateCheckHole(checkHole);
//            return record > 0;
            return true;
        } catch (Exception e) {
            log.error("涵洞信息上报修改失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean delete(Long id) {
//        return checkHoleService.deleteCheckHoleById(id) > 0;
        return true;
    }

    @Override
    public List<Object> query(QueryReportDTO queryReportDTO) {
//        CheckHole checkHole = new CheckHole();
//        checkHole.setCompanyName(queryReportDTO.getCompanyName());
//        checkHole.setRoadCodeOrName(queryReportDTO.getRoadCodeOrName());
//        checkHole.setCheckStatus(queryReportDTO.getCheckStatus());
//        checkHole.setRiskLevel(queryReportDTO.getRiskLevel());
//        List<CheckHole> list = checkHoleService.selectCheckHoleList(checkHole);
        return new ArrayList<>();
    }

    @Override
    public List<Object> queryRepair(QueryReportDTO queryReportDTO) {
//        CheckHole checkHole = new CheckHole();
//        checkHole.setCompanyName(queryReportDTO.getCompanyName());
//        checkHole.setRoadCodeOrName(queryReportDTO.getRoadCodeOrName());
//        List<HoleRepairDTO> list = repairRecordService.selectCheckHoleWithRepairList(checkHole);
//        return new ArrayList<>(list);
        return new ArrayList<>();
    }

    @Override
    public void audit(AuditRequest auditRequest) {
        //checkHoleService.auditCheckHole(auditRequest);
    }
}
