package com.tunnel.biz.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tunnel.biz.ReportStrategy;
import com.tunnel.biz.dto.QueryReportDTO;
import com.tunnel.domain.dto.AuditRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2025年07月13日 11:16
 * 路面信息上报
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RoadReportStrategy implements ReportStrategy {

    @Resource
    private final ObjectMapper objectMapper;

//    @Resource
//    private CheckRoadService checkRoadService;
//
//    @Resource
//    private RepairRecordService repairRecordService;

    @Override
    public Boolean create(Object data) {
        try {
//            CheckRoad checkRoad = objectMapper.convertValue(data, CheckRoad.class);
//            int record = checkRoadService.insertCheckRoad(checkRoad);
//            return record > 0;
            return true;
        } catch (Exception e) {
            log.error("路面信息上报新增失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean update(Long id, Object data) {
        try {
//            CheckRoad checkRoad = objectMapper.convertValue(data, CheckRoad.class);
//            checkRoad.setId(id);
//            //查询当前记录
//            CheckRoad road = checkRoadService.selectCheckRoadById(id);
//            if(Objects.isNull(road)){
//                throw new ServiceException("检测信息不存在");
//            }
//            if(Objects.equals(road.getCheckStatus(), 2)){
//                checkRoad.setCheckStatus(0);
//                checkRoad.setRemark("");
//            }
//            int record = checkRoadService.updateCheckRoad(checkRoad);
//            return record > 0;
            return true;
        } catch (Exception e) {
            log.error("路面信息上报修改失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean delete(Long id) {
//        return checkRoadService.deleteCheckRoadById(id) > 0;
        return true;
    }

    @Override
    public List<Object> query(QueryReportDTO queryReportDTO) {
//        CheckRoad checkRoad = new CheckRoad();
//        checkRoad.setCompanyName(queryReportDTO.getCompanyName());
//        checkRoad.setRoadCodeOrName(queryReportDTO.getRoadCodeOrName());
//        checkRoad.setCheckStatus(queryReportDTO.getCheckStatus());
//        checkRoad.setRiskLevel(queryReportDTO.getRiskLevel());
//        List<CheckRoad> list = checkRoadService.selectCheckRoadList(checkRoad);
//        return new ArrayList<>(list);
        return new ArrayList<>();
    }

    @Override
    public List<Object> queryRepair(QueryReportDTO queryReportDTO) {
//        CheckRoad checkRoad = new CheckRoad();
//        checkRoad.setCompanyName(queryReportDTO.getCompanyName());
//        checkRoad.setRoadCodeOrName(queryReportDTO.getRoadCodeOrName());
//        List<RoadRepairDTO> list = repairRecordService.selectCheckRoadWithRepairList(checkRoad);
//        return new ArrayList<>(list);
        return new ArrayList<>();
    }

    @Override
    public void audit(AuditRequest auditRequest) {
        //checkRoadService.auditCheckRoad(auditRequest);
    }
}
