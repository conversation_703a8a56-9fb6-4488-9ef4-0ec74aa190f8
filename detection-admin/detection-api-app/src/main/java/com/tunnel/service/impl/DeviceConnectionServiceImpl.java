package com.tunnel.service.impl;

import com.tunnel.controller.DeviceConnectionController.DeviceConnectionRequest;
import com.tunnel.domain.Requirement;
import com.tunnel.framework.event.RefreshMqttTopic;
import com.tunnel.mapper.RequirementMapper;
import com.tunnel.service.DeviceConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备连接管理服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Service
public class DeviceConnectionServiceImpl implements DeviceConnectionService {

    @Resource
    private RefreshMqttTopic refreshMqttTopic;

    @Resource
    private RequirementMapper requirementMapper;

    @Override
    public void deviceOnline(String deviceId, String topic, String dataFrom) {
        log.info("设备上线: deviceId={}, topic={}, dataFrom={}", deviceId, topic, dataFrom);
        
        if (topic == null || topic.trim().isEmpty()) {
            log.warn("设备{}的topic为空，跳过上线操作", deviceId);
            return;
        }
        
        // 添加MQTT主题订阅
        List<String> addTopics = Collections.singletonList(topic);
        refreshMqttTopic.refreshMqttPahoMessageDrivenChannelAdapter(addTopics, null);
        
        log.info("设备{}上线成功，已订阅主题: {}", deviceId, topic);
    }

    @Override
    public void deviceOffline(String deviceId, String topic, String dataFrom) {
        log.info("设备离线: deviceId={}, topic={}, dataFrom={}", deviceId, topic, dataFrom);
        
        if (topic == null || topic.trim().isEmpty()) {
            log.warn("设备{}的topic为空，跳过离线操作", deviceId);
            return;
        }
        
        // 取消MQTT主题订阅
        List<String> delTopics = Collections.singletonList(topic);
        refreshMqttTopic.refreshMqttPahoMessageDrivenChannelAdapter(null, delTopics);
        
        log.info("设备{}离线成功，已取消订阅主题: {}", deviceId, topic);
    }

    @Override
    public void batchDeviceOnline(List<DeviceConnectionRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            log.warn("批量设备上线请求为空");
            return;
        }
        
        log.info("批量设备上线，设备数量: {}", requests.size());
        
        // 收集所有需要添加的主题
        List<String> addTopics = requests.stream()
                .filter(req -> req.getTopic() != null && !req.getTopic().trim().isEmpty())
                .map(DeviceConnectionRequest::getTopic)
                .distinct()
                .collect(Collectors.toList());
        
        if (CollectionUtils.isNotEmpty(addTopics)) {
            refreshMqttTopic.refreshMqttPahoMessageDrivenChannelAdapter(addTopics, null);
            log.info("批量设备上线成功，已订阅主题: {}", addTopics);
        } else {
            log.warn("批量设备上线，没有有效的主题需要订阅");
        }
    }

    @Override
    public void batchDeviceOffline(List<DeviceConnectionRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            log.warn("批量设备离线请求为空");
            return;
        }
        
        log.info("批量设备离线，设备数量: {}", requests.size());
        
        // 收集所有需要删除的主题
        List<String> delTopics = requests.stream()
                .filter(req -> req.getTopic() != null && !req.getTopic().trim().isEmpty())
                .map(DeviceConnectionRequest::getTopic)
                .distinct()
                .collect(Collectors.toList());
        
        if (CollectionUtils.isNotEmpty(delTopics)) {
            refreshMqttTopic.refreshMqttPahoMessageDrivenChannelAdapter(null, delTopics);
            log.info("批量设备离线成功，已取消订阅主题: {}", delTopics);
        } else {
            log.warn("批量设备离线，没有有效的主题需要取消订阅");
        }
    }

    @Override
    public void initializeOnlineDevices() {
        log.info("开始初始化所有在线设备连接...");
        
        try {
            // 查询所有status=1的设备
            Requirement query = new Requirement();
            query.setStatus(1); // 在线状态
            List<Requirement> onlineDevices = requirementMapper.selectScRequirementList(query);
            
            if (CollectionUtils.isEmpty(onlineDevices)) {
                log.info("没有找到在线状态的设备");
                return;
            }
            
            log.info("找到{}个在线状态的设备，开始重新连接...", onlineDevices.size());
            
            // 收集所有在线设备的主题
            List<String> onlineTopics = onlineDevices.stream()
                    .filter(device -> device.getTopic() != null && !device.getTopic().trim().isEmpty())
                    .map(Requirement::getTopic)
                    .distinct()
                    .collect(Collectors.toList());
            
            if (CollectionUtils.isNotEmpty(onlineTopics)) {
                // 批量订阅所有在线设备的主题
                refreshMqttTopic.refreshMqttPahoMessageDrivenChannelAdapter(onlineTopics, null);
                log.info("成功重新连接{}个在线设备，订阅主题: {}", onlineDevices.size(), onlineTopics);
            } else {
                log.warn("在线设备中没有有效的主题需要订阅");
            }
            
        } catch (Exception e) {
            log.error("初始化在线设备连接失败", e);
            throw new RuntimeException("初始化在线设备连接失败", e);
        }
    }
}
