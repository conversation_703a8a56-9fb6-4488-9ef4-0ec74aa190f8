package com.tunnel.service;

import com.tunnel.controller.DeviceConnectionController.DeviceConnectionRequest;

import java.util.List;

/**
 * 设备连接管理服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface DeviceConnectionService {

    /**
     * 设备上线 - 订阅MQTT主题
     * 
     * @param deviceId 设备ID
     * @param topic MQTT主题
     * @param dataFrom 数据来源
     */
    void deviceOnline(String deviceId, String topic, String dataFrom);

    /**
     * 设备离线 - 取消订阅MQTT主题
     * 
     * @param deviceId 设备ID
     * @param topic MQTT主题
     * @param dataFrom 数据来源
     */
    void deviceOffline(String deviceId, String topic, String dataFrom);

    /**
     * 批量设备上线
     * 
     * @param requests 设备连接请求列表
     */
    void batchDeviceOnline(List<DeviceConnectionRequest> requests);

    /**
     * 批量设备离线
     * 
     * @param requests 设备连接请求列表
     */
    void batchDeviceOffline(List<DeviceConnectionRequest> requests);

    /**
     * 初始化所有在线设备连接
     * 在应用启动时调用，重新连接所有status=1的设备
     */
    void initializeOnlineDevices();
}
