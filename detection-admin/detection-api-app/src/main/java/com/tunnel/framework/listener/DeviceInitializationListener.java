package com.tunnel.framework.listener;

import com.tunnel.service.DeviceConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 设备初始化监听器
 * 在应用启动完成后自动初始化所有在线设备连接
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Component
public class DeviceInitializationListener implements ApplicationListener<ApplicationReadyEvent> {

    @Resource
    private DeviceConnectionService deviceConnectionService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("应用启动完成，开始初始化设备连接...");
        
        try {
            // 延迟3秒执行，确保所有Bean都已初始化完成
            Thread.sleep(3000);
            
            // 初始化所有在线设备连接
            deviceConnectionService.initializeOnlineDevices();
            
            log.info("设备连接初始化完成");
        } catch (Exception e) {
            log.error("设备连接初始化失败", e);
        }
    }
}
