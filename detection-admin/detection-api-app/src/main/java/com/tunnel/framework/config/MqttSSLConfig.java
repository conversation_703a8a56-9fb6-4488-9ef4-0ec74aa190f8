package com.tunnel.framework.config;

import com.tunnel.common.core.domain.entity.SysDictData;
import com.tunnel.common.utils.uuid.IdUtils;
import com.tunnel.framework.handler.SubscribeHandler;
import com.tunnel.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * mqtt ssl方式连接配置
 *
 *<AUTHOR>
 *@date 2025/8/1 11:27
 *@version 1.0.0
 */
@Slf4j
@Configuration
public class MqttSSLConfig implements ApplicationContextAware {
   

    @Value("${mqtt.broker-url}")
    private String url;

    /**
     * ca证书路径,PEM格式
     */
    @Value("${mqtt.ssl.ca-cert-path}")
    private String caCertPath;

    @Value("${mqtt.ssl.client-cert-path}")
    private String clientCertPath;

    @Value("${mqtt.ssl.key-store-password:}")
    private String keyStorePassword;
    @Value("${mqtt.ssl.subscribe-topic:test/topic/#}")
    private String subscribeTopic;
    @javax.annotation.Resource
    private ISysDictDataService sysDictDataService;

    @javax.annotation.Resource(name = "mqttSubscribeHandler")
    private SubscribeHandler subscribeHandler;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Bean
    public MqttPahoClientFactory mqttClientFactory() throws Exception {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();

        // 配置SSL上下文
        SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
        log.info("mqtt使用SSL方式连接");
        TrustManagerFactory trustManagerFactory = loadCert(caCertPath);

        // 加载密钥库（客户端证书和私钥）
        KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(
                KeyManagerFactory.getDefaultAlgorithm());
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        try (InputStream inputStream = getInputStream(clientCertPath)){
            keyStore.load(inputStream, keyStorePassword.toCharArray());
            log.info("加载密钥证书PKCS12格式");
        }
        keyManagerFactory.init(keyStore, keyStorePassword.toCharArray());
        log.info("初始化密钥证书PKCS12格式");

        // 初始化SSL上下文
        sslContext.init(keyManagerFactory.getKeyManagers(), trustManagerFactory.getTrustManagers(), null);
        SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

        // 配置连接选项
        options.setSocketFactory(sslSocketFactory);
        options.setServerURIs(new String[]{url});
        options.setCleanSession(true);
        options.setConnectionTimeout(30);
        options.setKeepAliveInterval(60);

        factory.setConnectionOptions(options);
        return factory;
    }

    // 配置MQTT客户端
    @Bean
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }

    /**
     * 配置消息接收器（在这里指定接收端clientId）
     * 启动时不订阅任何主题，由设备初始化监听器负责订阅在线设备的主题
     */
    @Bean
    public MqttPahoMessageDrivenChannelAdapter mqttInbound() throws Exception {
        // 参数：接收端clientId、客户端工厂、空的主题数组（启动时不订阅任何主题）
        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(
                getClientId(), mqttClientFactory(), new String[0]);
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter()); // 消息转换器
        adapter.setQos(1); // 订阅QoS级别
        adapter.setOutputChannel(mqttInputChannel()); // 消息输出到指定通道
        return adapter;
    }

    @Bean
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public MessageHandler handler() {

        return message -> {
            String topic = message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC).toString();
            String payload = message.getPayload().toString();
            System.out.println("收到MQTT消息 - 主题: " + topic + ", 内容: " + payload);
            subscribeHandler.handler(payload, topic);
        };
    }

    private InputStream getInputStream(String path) throws IOException {
        if (path.startsWith("classpath:")) {
            path = path.substring("classpath:".length() + 1);
            Resource caCertResource = new ClassPathResource(path);
            log.info("读取classPath路径{}下的文件", path);
            return caCertResource.getInputStream();
        } else {
            log.info("读取文件路径{}下的文件", path);
            return Files.newInputStream(Paths.get(path));
        }
    }

    private TrustManagerFactory loadCert(String certPath) throws CertificateException, IOException, KeyStoreException, NoSuchAlgorithmException {
        // 使用CertificateFactory解析PEM证书
        List<X509Certificate> caCerts = new ArrayList<>();
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        try (InputStream is = getInputStream(certPath)) {
            // 支持包含多个证书的PEM文件
            Collection<? extends Certificate> certs = cf.generateCertificates(is);
            for (java.security.cert.Certificate cert : certs) {
                if (cert instanceof X509Certificate) {
                    caCerts.add((X509Certificate) cert);
                }
            }
        }
        // 2. 创建临时密钥库并添加证书
        KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
        trustStore.load(null, null); // 初始化空密钥库

        // 将证书添加到信任库
        for (int i = 0; i < caCerts.size(); i++) {
            X509Certificate cert = caCerts.get(i);
            trustStore.setCertificateEntry("ca-" + i, cert);
        }

        // 3. 初始化信任管理器工厂
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(
                TrustManagerFactory.getDefaultAlgorithm()
        );
        trustManagerFactory.init(trustStore);
        log.info("加载信任证书pem格式");
        return trustManagerFactory;
    }

    public String[] getConfigTopic() {
        List<String> topicList;
        SysDictData dictData = new SysDictData();
        dictData.setDictType("mqtt_bind_topic");
        List<SysDictData> dictDataList = sysDictDataService.selectDictDataList(dictData);
        if (CollectionUtils.isEmpty(dictDataList)) {
            topicList = Collections.singletonList(subscribeTopic);
        } else {
            topicList = dictDataList.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
        }
        log.info("获取配置的topic:{}", topicList);
        String[] topics = new String[topicList.size()];
        return topicList.toArray(topics);
    }

    private String getClientId() {
        return IdUtils.fastSimpleUUID() ;
    }
}