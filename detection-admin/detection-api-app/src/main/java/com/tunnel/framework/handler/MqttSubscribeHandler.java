package com.tunnel.framework.handler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tunnel.domain.RequirementReport;
import com.tunnel.domain.RequirementStatus;
import com.tunnel.domain.RequirementWarning;
import com.tunnel.service.RequirementReportService;
import com.tunnel.service.RequirementStatusService;
import com.tunnel.service.RequirementWarningService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/1  18:43
 * @since 1.0.0
 */
@Slf4j
@Service(value = "mqttSubscribeHandler")
public class MqttSubscribeHandler implements SubscribeHandler{

    @Resource
    private RequirementStatusService requirementStatusService;
    @Resource
    private RequirementWarningService requirementWarningService;
    @Resource
    private RequirementReportService requirementReportService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handler(String content, String topic) {
        JSONObject json = JSON.parseObject(content);
        JSONObject handlerData = keyHandler(json);
        log.info("处理过后的数据为:{}", handlerData);
        String key = handlerData.getString("t");

        RequirementStatus requirementStatus = JSON.to(RequirementStatus.class, handlerData);
        List<RequirementStatus> requirementStatusExistList = requirementStatusService.listByKey(key);
        if (CollectionUtils.isEmpty(requirementStatusExistList)) {
            requirementStatus.setKey(key);
            requirementStatus.setTopic(topic);
            requirementStatusService.insertRequirementStatus(requirementStatus);
        }
        RequirementWarning requirementWarning = JSON.to(RequirementWarning.class, handlerData);
        List<RequirementWarning> requirementWarningExistList = requirementWarningService.listByKey(key);
        if (CollectionUtils.isEmpty(requirementWarningExistList)) {
            requirementWarning.setKey(key);
            requirementWarning.setTopic(topic);
            requirementWarningService.insertRequirementWarning(requirementWarning);
        }
        RequirementReport requirementReport = JSON.to(RequirementReport.class, handlerData);
        List<RequirementReport> requirementReportExistList = requirementReportService.listByKey(key);
        if (CollectionUtils.isEmpty(requirementReportExistList)) {
            requirementReport.setKey(key);
            requirementReport.setTopic(topic);
            requirementReportService.insertRequirementReport(requirementReport);
        }
    }

    private JSONObject keyHandler(JSONObject data) {
        JSONObject handlerData = new JSONObject();
        data.forEach((key, value) -> {
            // 先按小写处理
            String key1 = key.toLowerCase();
            // 再处理特殊字符
            key1 = key1.replaceFirst("\\.", "_");
            handlerData.put(key1, value);
        });
        return handlerData;
    }
}
