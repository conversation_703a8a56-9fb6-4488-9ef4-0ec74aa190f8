package com.tunnel.framework.event;

import com.tunnel.framework.config.MqttSSLConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/5  17:33
 * @since 1.0.0
 */
@Slf4j
@Component
public class RefreshMqttTopic implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Resource
    private MqttSSLConfig mqttSSLConfig;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 刷新MQTT的消息接收器的订阅（使用配置文件中的主题）
     * 注意：此方法已废弃，建议使用明确指定主题的方法
     */
    @Deprecated
    public void refreshMqttPahoMessageDrivenChannelAdapter() {
        refreshMqttPahoMessageDrivenChannelAdapterWithConfig();
    }

    /**
     * 使用配置文件中的主题刷新MQTT订阅
     */
    public void refreshMqttPahoMessageDrivenChannelAdapterWithConfig() {
        MqttPahoMessageDrivenChannelAdapter adapter = this.applicationContext.getBean(MqttPahoMessageDrivenChannelAdapter.class);
        String[] alreadyExistTopics = adapter.getTopic();
        List<String> addTopicList = new ArrayList<>();
        List<String> removeTopicList = new ArrayList<>();

        String[] configTopics = mqttSSLConfig.getConfigTopic();
        for (String topic : alreadyExistTopics) {
            if (!ArrayUtils.contains(configTopics, topic)) {
                removeTopicList.add(topic);
            }
        }
        for (String topic : configTopics) {
            if (!ArrayUtils.contains(alreadyExistTopics, topic)) {
                addTopicList.add(topic);
            }
        }

        log.info("MqttTopicRefresh using config topics - addTopics: {}, removeTopics: {}", addTopicList, removeTopicList);

        if (CollectionUtils.isNotEmpty(removeTopicList)) {
            String[] removeTopicArray = new String[removeTopicList.size()];
            adapter.removeTopic(removeTopicList.toArray(removeTopicArray));
            log.info("MqttTopicRefresh removeTopic: {}", removeTopicList);
        }
        if (CollectionUtils.isNotEmpty(addTopicList)) {
            String[] addTopicArray = new String[addTopicList.size()];
            adapter.addTopic(addTopicList.toArray(addTopicArray));
            log.info("MqttTopicRefresh addTopic: {}", addTopicList);
        }
    }



    /**
     * 刷新MQTT的消息接收器的订阅（只处理明确指定的主题）
     * 不再自动使用配置文件中的主题，只处理明确传入的主题列表
     */
    public void refreshMqttPahoMessageDrivenChannelAdapter(List<String> needAddTopics, List<String> needDelTopics) {
        MqttPahoMessageDrivenChannelAdapter adapter = this.applicationContext.getBean(MqttPahoMessageDrivenChannelAdapter.class);
        List<String> addTopicList = new ArrayList<>();
        List<String> removeTopicList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(needAddTopics)) {
            addTopicList.addAll(needAddTopics);
        }
        if (CollectionUtils.isNotEmpty(needDelTopics)) {
            removeTopicList.addAll(needDelTopics);
        }
        // 如果没有指定任何主题，则不执行任何操作
        if (CollectionUtils.isEmpty(needAddTopics) && CollectionUtils.isEmpty(needDelTopics)) {
            log.info("MqttTopicRefresh: 没有指定需要添加或删除的主题，跳过操作");
            return;
        }
        log.info("MqttTopicRefreshTask.refreshMqttTopic is : {}", adapter.isActive());
        if (CollectionUtils.isNotEmpty(addTopicList)) {
            String[] addTopicArray = new String[addTopicList.size()];
            adapter.addTopic(addTopicList.toArray(addTopicArray));
            log.info("MqttTopicRefresh addTopic: {}", addTopicList);
        }
        if (CollectionUtils.isNotEmpty(removeTopicList)) {
            String[] removeTopicArray = new String[removeTopicList.size()];
            adapter.removeTopic(removeTopicList.toArray(removeTopicArray));
            log.info("MqttTopicRefresh del Topic: {}", removeTopicList);
        }
        adapter.start();
    }
}
