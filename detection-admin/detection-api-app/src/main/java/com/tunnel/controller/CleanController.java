package com.tunnel.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.service.AirQualityCleanService;
import com.tunnel.service.NoiseQualityCleanService;
import com.tunnel.service.SecomeaCleanService;
import com.tunnel.service.WaterQualityCleanService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/clean")
public class CleanController {

    @Resource
    private SecomeaCleanService secomeaCleanService;
    
    @Resource
    private WaterQualityCleanService waterQualityCleanService;

    @Resource
    private NoiseQualityCleanService noiseQualityCleanService;

    @Resource
    private AirQualityCleanService airQualityCleanService;

    @GetMapping("/month")
    @Anonymous
    public String runMonth(@RequestParam("ym") String yyyyMM) {
        secomeaCleanService.cleanMonth(yyyyMM);
        return "OK";
    }

    @GetMapping("/range")
    @Anonymous
    public String runRange(@RequestParam("from") String from,
                           @RequestParam("to") String to) {
        secomeaCleanService.cleanRange(from, to);
        return "OK";
    }

    @GetMapping("/monitor/month")
    @Anonymous
    public String runWaterQualityMonth(@RequestParam("ym") String yyyyMM) {
        waterQualityCleanService.cleanMonth(yyyyMM);
        return "OK";
    }

    @GetMapping("/monitor/range")
    @Anonymous
    public String runWaterQualityRange(@RequestParam("from") String from,
                                       @RequestParam("to") String to) {
        waterQualityCleanService.cleanRange(from, to);
        return "OK";
    }

    /**
     * 清洗噪声监测数据
     */
    @PostMapping("/noise")
    @Anonymous
    public AjaxResult cleanNoiseData(@RequestParam String fromYyyyMM, @RequestParam String toYyyyMM) {
        try {
            noiseQualityCleanService.cleanRange(fromYyyyMM, toYyyyMM);
            return AjaxResult.success("噪声监测数据清洗完成");
        } catch (Exception e) {
            return AjaxResult.error("噪声监测数据清洗失败: " + e.getMessage());
        }
    }

    /**
     * 清洗空气监测数据
     */
    @PostMapping("/air")
    @Anonymous
    public AjaxResult cleanAirData(@RequestParam String fromYyyyMM, @RequestParam String toYyyyMM) {
        try {
            airQualityCleanService.cleanRange(fromYyyyMM, toYyyyMM);
            return AjaxResult.success("空气监测数据清洗完成");
        } catch (Exception e) {
            return AjaxResult.error("空气监测数据清洗失败: " + e.getMessage());
        }
    }

    /**
     * 清洗单月水质监测数据
     */
    @PostMapping("/water/month")
    @Anonymous
    public AjaxResult cleanWaterMonth(@RequestParam String yyyyMM) {
        try {
            waterQualityCleanService.cleanMonth(yyyyMM);
            return AjaxResult.success("水质监测数据清洗完成");
        } catch (Exception e) {
            return AjaxResult.error("水质监测数据清洗失败: " + e.getMessage());
        }
    }

    /**
     * 清洗单月噪声监测数据
     */
    @PostMapping("/noise/month")
    @Anonymous
    public AjaxResult cleanNoiseMonth(@RequestParam String yyyyMM) {
        try {
            noiseQualityCleanService.cleanMonth(yyyyMM);
            return AjaxResult.success("噪声监测数据清洗完成");
        } catch (Exception e) {
            return AjaxResult.error("噪声监测数据清洗失败: " + e.getMessage());
        }
    }

    /**
     * 清洗单月空气监测数据
     */
    @PostMapping("/air/month")
    @Anonymous
    public AjaxResult cleanAirMonth(@RequestParam String yyyyMM) {
        try {
            airQualityCleanService.cleanMonth(yyyyMM);
            return AjaxResult.success("空气监测数据清洗完成");
        } catch (Exception e) {
            return AjaxResult.error("空气监测数据清洗失败: " + e.getMessage());
        }
    }

}


