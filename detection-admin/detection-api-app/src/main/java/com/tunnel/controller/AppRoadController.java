package com.tunnel.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.domain.Road;
import com.tunnel.service.RoadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>  app道路接口
 */
@RestController
@RequestMapping("/report/road")
@Api(tags = "基础路线管理")
public class AppRoadController extends BaseController {

    @Resource
    private RoadService roadService;

    /**
     * 获取所有公司列表
     */
    @GetMapping("/selectAllCompany")
    @ApiOperation(value = "获取所有公司列表", notes = "获取所有公司列表")
    @Operation(summary = "获取所有公司列表", description = "获取所有公司列表")
    public AjaxResult selectAllCompany() {
        return success(roadService.selectAllCompany(new Road()));
    }

    /**
     * 查询基础项目路线信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取基础项目路线信息列表", notes = "获取全部基础项目路线信息数据")
    @Operation(summary = "获取基础项目路线信息列表", description = "获取全部基础项目路线信息数据")
    public TableDataInfo list(Road road) {
        startPage();
        List<Road> list = roadService.selectRoadList(road);
        return getDataTable(list);
    }
}
