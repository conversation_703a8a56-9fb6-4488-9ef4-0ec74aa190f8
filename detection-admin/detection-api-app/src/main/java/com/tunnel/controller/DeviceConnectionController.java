package com.tunnel.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.service.DeviceConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备连接管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@RestController
@RequestMapping("/device/connection")
public class DeviceConnectionController extends BaseController {

    @Resource
    private DeviceConnectionService deviceConnectionService;

    /**
     * 设备上线 - 订阅MQTT主题
     */
    @PostMapping("/online")
    @Anonymous
    public AjaxResult deviceOnline(@RequestBody DeviceConnectionRequest request) {
        try {
            deviceConnectionService.deviceOnline(request.getDeviceId(), request.getTopic(), request.getDataFrom());
            return AjaxResult.success("设备上线成功");
        } catch (Exception e) {
            log.error("设备上线失败", e);
            return AjaxResult.error("设备上线失败: " + e.getMessage());
        }
    }

    /**
     * 设备离线 - 取消订阅MQTT主题
     */
    @PostMapping("/offline")
    @Anonymous
    public AjaxResult deviceOffline(@RequestBody DeviceConnectionRequest request) {
        try {
            deviceConnectionService.deviceOffline(request.getDeviceId(), request.getTopic(), request.getDataFrom());
            return AjaxResult.success("设备离线成功");
        } catch (Exception e) {
            log.error("设备离线失败", e);
            return AjaxResult.error("设备离线失败: " + e.getMessage());
        }
    }

    /**
     * 批量设备上线
     */
    @PostMapping("/batch/online")
    @Anonymous
    public AjaxResult batchDeviceOnline(@RequestBody List<DeviceConnectionRequest> requests) {
        try {
            deviceConnectionService.batchDeviceOnline(requests);
            return AjaxResult.success("批量设备上线成功");
        } catch (Exception e) {
            log.error("批量设备上线失败", e);
            return AjaxResult.error("批量设备上线失败: " + e.getMessage());
        }
    }

    /**
     * 批量设备离线
     */
    @PostMapping("/batch/offline")
    @Anonymous
    public AjaxResult batchDeviceOffline(@RequestBody List<DeviceConnectionRequest> requests) {
        try {
            deviceConnectionService.batchDeviceOffline(requests);
            return AjaxResult.success("批量设备离线成功");
        } catch (Exception e) {
            log.error("批量设备离线失败", e);
            return AjaxResult.error("批量设备离线失败: " + e.getMessage());
        }
    }

    /**
     * 设备连接请求DTO
     */
    public static class DeviceConnectionRequest {
        private String deviceId;
        private String topic;
        private String dataFrom;

        // Getters and Setters
        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public String getTopic() {
            return topic;
        }

        public void setTopic(String topic) {
            this.topic = topic;
        }

        public String getDataFrom() {
            return dataFrom;
        }

        public void setDataFrom(String dataFrom) {
            this.dataFrom = dataFrom;
        }
    }
}
