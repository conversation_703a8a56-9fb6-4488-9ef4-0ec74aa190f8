package com.tunnel.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.constant.Constants;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.domain.entity.SysMenu;
import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.core.domain.model.LoginBody;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.framework.web.service.SysLoginService;
import com.tunnel.framework.web.service.SysPermissionService;
import com.tunnel.system.service.ISysMenuService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * App登录验证
 *
 * <AUTHOR>
 */
@RestController
public class AppLoginController {
    @Resource
    private SysLoginService loginService;

    @Resource
    private ISysMenuService menuService;

    @Resource
    private SysPermissionService permissionService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    @Anonymous
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        if(Objects.equals("HBGS01",loginBody.getUsername())){
            throw new ServiceException("当前账户没有登录小程序的权限");
        }
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌1
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
