package com.tunnel.controller;

import com.tunnel.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 2025年07月13日 18:09
 */
@RestController
@RequestMapping("/report/repairRecord")
@Api(tags = "维修记录管理")
public class AppRepairRecordController extends BaseController {

    //@Resource
    //private RepairRecordService repairRecordService;


//    @PostMapping("/insert")
//    @ApiOperation(value = "新增维修记录")
//    @Log(title = "维修记录", businessType = BusinessType.INSERT)
//    public AjaxResult add(@RequestBody RepairRecord repairRecord) {
//        return toAjax(repairRecordService.insertRepairRecord(repairRecord));
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改维修记录")
//    @Log(title = "维修记录", businessType = BusinessType.UPDATE)
//    public AjaxResult edit(@RequestBody RepairRecord repairRecord) {
//        return toAjax(repairRecordService.updateRepairRecord(repairRecord));
//    }
}
