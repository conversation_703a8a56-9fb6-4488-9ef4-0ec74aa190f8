package com.tunnel.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.ArrayList;

/**
 * 桩号工具类，用于计算百米段和公里段
 *
 * <AUTHOR>
 */
public class StakeCodeUtil {

    private static final Logger log = LoggerFactory.getLogger(StakeCodeUtil.class);

    /**
     * 根据起始桩号计算百米段
     * 支持多种格式：K2320+260、2320260等
     * 例如：K2320+260 -> K2320+200~K2320+300
     *
     * @param startCode 起始桩号
     * @return 百米段
     */
    public static String calculateHundredSection(String startCode) {
        if (StringUtils.isBlank(startCode)) {
            return "";
        }
        try {
            // 将桩号转换为总米数
            long totalMeters = parseStakeCodeToMeters(startCode);
            if (totalMeters < 0) {
                return "";
            }

            // 计算百米段的起始和结束（按100米分段）
            long startMeters = (totalMeters / 100) * 100;
            long endMeters = startMeters + 100;

            // 转换回桩号格式
            String startStake = formatMetersToStakeCode(startMeters);
            String endStake = formatMetersToStakeCode(endMeters);

            return startStake + "~" + endStake;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 根据起始和结束桩号计算百米段（优化版，支持实际桩号范围）
     * 如果结束桩号不是整百米，使用实际结束桩号
     *
     * @param startCode 起始桩号
     * @param endCode 结束桩号
     * @return 百米段
     */
    public static String calculateHundredSectionWithEndCode(String startCode, String endCode) {
        if (StringUtils.isBlank(startCode)) {
            return "";
        }
        try {
            // 将起始桩号转换为总米数
            long totalStartMeters = parseStakeCodeToMeters(startCode);
            if (totalStartMeters < 0) {
                return "";
            }

            // 计算百米段的起始
            long startMeters = (totalStartMeters / 100) * 100;
            String startStake = formatMetersToStakeCode(startMeters);

            // 如果有结束桩号，使用实际结束桩号；否则使用标准百米段结束
            if (StringUtils.isNotBlank(endCode)) {
                long totalEndMeters = parseStakeCodeToMeters(endCode);
                if (totalEndMeters > totalStartMeters) {
                    // 如果结束桩号在同一百米段内，直接使用实际结束桩号
                    if (totalEndMeters / 100 == totalStartMeters / 100) {
                        return startStake + "~" + endCode.trim().toUpperCase();
                    }
                }
            }

            // 默认使用标准百米段结束
            long endMeters = startMeters + 100;
            String endStake = formatMetersToStakeCode(endMeters);
            return startStake + "~" + endStake;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 根据起始桩号计算公里段
     * 支持多种格式：K2320+260、2320260等
     * 例如：K2320+260 -> K2320+000~K2321+000
     *
     * @param startCode 起始桩号
     * @return 公里段
     */
    public static String calculateThousandSection(String startCode) {
        if (StringUtils.isBlank(startCode)) {
            return "";
        }
        try {
            // 将桩号转换为总米数
            long totalMeters = parseStakeCodeToMeters(startCode);
            if (totalMeters < 0) {
                return "";
            }

            // 计算公里段的起始和结束（按1000米分段）
            long startMeters = (totalMeters / 1000) * 1000;
            long endMeters = startMeters + 1000;

            // 转换回桩号格式
            String startStake = formatMetersToStakeCode(startMeters);
            String endStake = formatMetersToStakeCode(endMeters);

            return startStake + "~" + endStake;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 根据起始和结束桩号计算公里段（优化版，支持实际桩号范围）
     * 如果结束桩号不是整千米，使用实际结束桩号
     *
     * @param startCode 起始桩号
     * @param endCode 结束桩号
     * @return 公里段
     */
    public static String calculateThousandSectionWithEndCode(String startCode, String endCode) {
        if (StringUtils.isBlank(startCode)) {
            return "";
        }
        try {
            // 将起始桩号转换为总米数
            long totalStartMeters = parseStakeCodeToMeters(startCode);
            if (totalStartMeters < 0) {
                return "";
            }

            // 计算公里段的起始
            long startMeters = (totalStartMeters / 1000) * 1000;
            String startStake = formatMetersToStakeCode(startMeters);

            // 如果有结束桩号，使用实际结束桩号；否则使用标准公里段结束
            if (StringUtils.isNotBlank(endCode)) {
                long totalEndMeters = parseStakeCodeToMeters(endCode);
                if (totalEndMeters > totalStartMeters) {
                    // 如果结束桩号在同一公里段内，直接使用实际结束桩号
                    if (totalEndMeters / 1000 == totalStartMeters / 1000) {
                        return startStake + "~" + endCode.trim().toUpperCase();
                    }
                }
            }

            // 默认使用标准公里段结束
            long endMeters = startMeters + 1000;
            String endStake = formatMetersToStakeCode(endMeters);
            return startStake + "~" + endStake;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 格式化桩号为标准K格式（例如：2320000 -> K2320+000）
     * 支持多种输入格式的自动识别和转换
     *
     * 支持的格式示例：
     * - 纯数字：2320000 -> K2320+000, 2320260 -> K2320+260
     * - K+数字：K2320000 -> K2320+000, K2320260 -> K2320+260
     * - +号分隔：2320+260 -> K2320+260, K2320+60 -> K2320+060, K23+260 -> K23+260
     * - 小数格式：23.260 -> K23+260, K23.260 -> K23+260
     * - 空格分隔：K2320 260 -> K2320+260, 2320 260 -> K2320+260
     * - 标准格式：K2320+260 -> K2320+260（不变）
     */
    public static String formatStakeCode(String stakeCode) {
        if (stakeCode == null || stakeCode.trim().isEmpty()) {
            return "";
        }

        stakeCode = stakeCode.trim().toUpperCase(); // 统一转为大写

        // 如果已经是K开头的标准格式（K数字+三位数字），直接返回
        if (stakeCode.startsWith("K") && stakeCode.contains("+") && stakeCode.matches("K\\d+\\+\\d{3}")) {
            return stakeCode;
        }

        try {
            // 处理纯数字格式（如：2320000, 2320260, 2320000.0）
            if (stakeCode.matches("\\d+(\\.0+)?")) {
                // 去掉小数点和后面的零
                String cleanNumber = stakeCode.replaceAll("\\.0+$", "");
                long value = Long.parseLong(cleanNumber);
                // 转换为公里和米
                long km = value / 1000;
                long m = value % 1000;
                return String.format("K%d+%03d", km, m);
            }

            // 处理已经是K开头但没有+号的格式（如：K2320000, K2320260）
            if (stakeCode.startsWith("K") && stakeCode.matches("K\\d+")) {
                String numberPart = stakeCode.substring(1);
                long value = Long.parseLong(numberPart);
                long km = value / 1000;
                long m = value % 1000;
                return String.format("K%d+%03d", km, m);
            }

            // 处理带+号但格式不标准的情况（如：2320+260, K2320+260, K2320+60）
            if (stakeCode.contains("+")) {
                String cleanCode = stakeCode.replace("K", "");
                String[] parts = cleanCode.split("\\+");
                if (parts.length == 2) {
                    long km = Long.parseLong(parts[0]);
                    long m = Long.parseLong(parts[1]);
                    return String.format("K%d+%03d", km, m);
                }
            }

            // 处理小数点格式（如：23.260, 23.200）- 公里为单位
            if (stakeCode.contains(".")) {
                double value = Double.parseDouble(stakeCode.replace("K", ""));
                long totalMeters = Math.round(value * 1000);
                long km = totalMeters / 1000;
                long m = totalMeters % 1000;
                return String.format("K%d+%03d", km, m);
            }

            // 处理带空格的格式（如：K2320 260, 2320 260）
            if (stakeCode.contains(" ")) {
                String cleanCode = stakeCode.replace("K", "").replace(" ", "");
                if (cleanCode.matches("\\d+")) {
                    long value = Long.parseLong(cleanCode);
                    long km = value / 1000;
                    long m = value % 1000;
                    return String.format("K%d+%03d", km, m);
                }
            }

            // 处理类似 K23+260 这种已经是正确格式但+前数字不够的情况
            if (stakeCode.startsWith("K") && stakeCode.contains("+")) {
                String cleanCode = stakeCode.substring(1); // 去掉K
                String[] parts = cleanCode.split("\\+");
                if (parts.length == 2) {
                    long km = Long.parseLong(parts[0]);
                    long m = Long.parseLong(parts[1]);
                    return String.format("K%d+%03d", km, m);
                }
            }

        } catch (NumberFormatException e) {
            log.warn("桩号格式化失败，无法解析数字: {}", stakeCode);
        } catch (Exception e) {
            log.warn("桩号格式化失败: {}, 错误: {}", stakeCode, e.getMessage());
        }

        // 如果所有格式化都失败，返回原始值
        log.warn("桩号格式化失败，使用原始值: {}", stakeCode);
        return stakeCode;
    }

    /**
     * 修正数据集合中的百米段和公里段，确保它们不超出实际的最大结束桩号
     * 这是为了保证hundred_section和thousand_section有统一的逻辑
     *
     * @param dataList 数据列表，泛型T必须有getEndCode, getHundredSection, getThousandSection,
     *                 setHundredSection, setThousandSection等方法
     * @param <T> 数据类型，支持所有检测数据实体类
     */
    public static <T> void correctSectionsWithMaxEndCode(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        try {
            // 找到最大的实际结束桩号
            String maxActualEndCode = null;
            for (T record : dataList) {
                String endCode = getEndCodeByReflection(record);
                if (endCode != null && !endCode.trim().isEmpty()) {
                    if (maxActualEndCode == null || endCode.compareTo(maxActualEndCode) > 0) {
                        maxActualEndCode = endCode;
                    }
                }
            }

            if (maxActualEndCode == null) {
                return;
            }

            log.info("找到最大实际结束桩号: {}", maxActualEndCode);

            // 解析最大结束桩号为米数
            long maxEndMeters = parseStakeCodeToMeters(maxActualEndCode);

            if (maxEndMeters > 0) {
                // 遍历所有记录，修正超出实际结束桩号的段位
                for (T record : dataList) {
                    boolean needUpdate = false;

                    // 检查并修正百米段
                    String hundredSection = getHundredSectionByReflection(record);
                    if (hundredSection != null && hundredSection.contains("~")) {
                        String[] parts = hundredSection.split("~");
                        if (parts.length == 2) {
                            String hundredEndCode = parts[1].trim();
                            long hundredEndMeters = parseStakeCodeToMeters(hundredEndCode);
                            if (hundredEndMeters > maxEndMeters) {
                                // 百米段结束超出了实际结束桩号，需要修正
                                String newHundredSection = parts[0].trim() + "~" + maxActualEndCode;
                                setHundredSectionByReflection(record, newHundredSection);
                                needUpdate = true;
                                log.debug("修正百米段：{} -> {}", hundredSection, newHundredSection);
                            }
                        }
                    }

                    // 检查并修正公里段
                    String thousandSection = getThousandSectionByReflection(record);
                    if (thousandSection != null && thousandSection.contains("~")) {
                        String[] parts = thousandSection.split("~");
                        if (parts.length == 2) {
                            String thousandEndCode = parts[1].trim();
                            long thousandEndMeters = parseStakeCodeToMeters(thousandEndCode);
                            if (thousandEndMeters > maxEndMeters) {
                                // 公里段结束超出了实际结束桩号，需要修正
                                String newThousandSection = parts[0].trim() + "~" + maxActualEndCode;
                                setThousandSectionByReflection(record, newThousandSection);
                                needUpdate = true;
                                log.debug("修正公里段：{} -> {}", thousandSection, newThousandSection);
                            }
                        }
                    }

                    if (needUpdate) {
                        String startCode = getStartCodeByReflection(record);
                        String endCode = getEndCodeByReflection(record);
                        String newHundredSection = getHundredSectionByReflection(record);
                        String newThousandSection = getThousandSectionByReflection(record);
                        log.info("修正记录段位：起始={}, 结束={}, 百米段={}, 公里段={}",
                                startCode, endCode, newHundredSection, newThousandSection);
                    }
                }
            }
        } catch (Exception e) {
            log.error("修正段位时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 通过反射获取对象的endCode属性
     */
    private static <T> String getEndCodeByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getEndCode");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过反射获取对象的startCode属性
     */
    private static <T> String getStartCodeByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getStartCode");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过反射获取对象的hundredSection属性
     */
    private static <T> String getHundredSectionByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getHundredSection");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过反射获取对象的thousandSection属性
     */
    private static <T> String getThousandSectionByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getThousandSection");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过反射设置对象的hundredSection属性
     */
    private static <T> void setHundredSectionByReflection(T obj, String value) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("setHundredSection", String.class);
            method.invoke(obj, value);
        } catch (Exception e) {
            log.warn("设置hundredSection失败: {}", e.getMessage());
        }
    }

    /**
     * 通过反射设置对象的thousandSection属性
     */
    private static <T> void setThousandSectionByReflection(T obj, String value) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("setThousandSection", String.class);
            method.invoke(obj, value);
        } catch (Exception e) {
            log.warn("设置thousandSection失败: {}", e.getMessage());
        }
    }

    /**
     * 将桩号转换为总米数
     * 支持格式：K2320+260、2320260、K2320000等
     */
    public static long parseStakeCodeToMeters(String stakeCode) {
        if (StringUtils.isBlank(stakeCode)) {
            return -1;
        }

        stakeCode = stakeCode.trim().toUpperCase();

        try {
            // 处理K2320+260格式
            if (stakeCode.startsWith("K") && stakeCode.contains("+")) {
                String cleanCode = stakeCode.substring(1); // 去掉K
                String[] parts = cleanCode.split("\\+");
                if (parts.length == 2) {
                    long km = Long.parseLong(parts[0]);
                    long m = Long.parseLong(parts[1]);
                    return km * 1000 + m;
                }
            }

            // 处理纯数字格式（如：2320260）
            if (stakeCode.matches("\\d+")) {
                return Long.parseLong(stakeCode);
            }

            // 处理K开头但没有+号的格式（如：K2320260）
            if (stakeCode.startsWith("K") && stakeCode.matches("K\\d+")) {
                String numberPart = stakeCode.substring(1);
                return Long.parseLong(numberPart);
            }

            return -1;
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * 将总米数转换为桩号格式
     * 例如：2320260 -> K2320+260
     */
    private static String formatMetersToStakeCode(long totalMeters) {
        long km = totalMeters / 1000;
        long m = totalMeters % 1000;
        return String.format("K%d+%03d", km, m);
    }

    /**
     * 根据roadType按顺序分段处理段位信息
     * 按照数据顺序，当路面类型发生变化时分为不同的段
     * 
     * @param dataList 需要处理段位的数据列表（已按桩号排序）
     */
    public static <T> void processSectionsByRoadType(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 按照路面类型的变化将数据分段
        List<List<T>> roadTypeSegments = new ArrayList<>();
        List<T> currentSegment = new ArrayList<>();
        String currentRoadType = null;

        for (T record : dataList) {
            String recordRoadType = getRoadTypeByReflection(record);
            if (recordRoadType == null) {
                recordRoadType = "default";
            }
            
            // 如果路面类型发生变化，开始新的段
            if (currentRoadType == null || !currentRoadType.equals(recordRoadType)) {
                // 保存之前的段（如果不为空）
                if (!currentSegment.isEmpty()) {
                    roadTypeSegments.add(new ArrayList<>(currentSegment));
                    currentSegment.clear();
                }
                currentRoadType = recordRoadType;
            }
            
            // 将当前记录添加到当前段
            currentSegment.add(record);
        }
        
        // 添加最后一段
        if (!currentSegment.isEmpty()) {
            roadTypeSegments.add(currentSegment);
        }

        log.info("按路面类型顺序分段处理段位信息，共分为 {} 段", roadTypeSegments.size());

        // 对每个路面类型段处理段位信息
        for (int i = 0; i < roadTypeSegments.size(); i++) {
            List<T> segment = roadTypeSegments.get(i);
            if (!segment.isEmpty()) {
                String roadType = getRoadTypeByReflection(segment.get(0));
                if (roadType == null) {
                    roadType = "default";
                }
                String startCode = getStartCodeByReflection(segment.get(0));
                String endCode = getEndCodeByReflection(segment.get(segment.size() - 1));
                
                log.info("处理第 {} 段：路面类型 '{}', 桩号范围 {} ~ {}, 共 {} 条记录", i + 1, roadType, startCode, endCode, segment.size());
                
                // 对当前段的数据计算段位信息
                calculateSectionsForGroup(segment, roadType + "_段" + (i + 1));
            }
        }
    }

    /**
     * 为指定分组计算段位信息
     * 在路面类型范围内，如果满足完整区间就按整数倍划分，否则使用实际范围
     * 
     * @param records 同一roadType的记录列表
     * @param roadType 路面类型
     */
    private static <T> void calculateSectionsForGroup(List<T> records, String roadType) {
        if (records == null || records.isEmpty()) {
            return;
        }
        try {
            // 按起始桩号排序，确保处理顺序正确
            records.sort((a, b) -> {
                String aStartCode = getStartCodeByReflection(a);
                String bStartCode = getStartCodeByReflection(b);
                if (aStartCode == null) return 1;
                if (bStartCode == null) return -1;
                return aStartCode.compareTo(bStartCode);
            });

            // 获取该路面类型的桩号范围
            String groupMinStakeCode = getStartCodeByReflection(records.get(0));
            String groupMaxStakeCode = records.stream()
                    .map(StakeCodeUtil::getEndCodeByReflection)
                    .filter(code -> code != null && !code.trim().isEmpty())
                    .max(String::compareTo)
                    .orElse(getStartCodeByReflection(records.get(records.size() - 1)));

            log.info("路面类型 '{}' 桩号范围：{} ~ {}", roadType, groupMinStakeCode, groupMaxStakeCode);

            // 为每条记录计算段位信息
            for (int i = 0; i < records.size(); i++) {
                T record = records.get(i);
                String startCode = getStartCodeByReflection(record);
                String endCode = getEndCodeByReflection(record);
                
                if (startCode != null && !startCode.trim().isEmpty()) {
                    // 计算百米段
                    String hundredSection = calculateOptimalHundredSection(startCode, endCode, groupMinStakeCode, groupMaxStakeCode);
                    setHundredSectionByReflection(record, hundredSection);
                    
                    // 计算公里段
                    String thousandSection = calculateOptimalThousandSection(startCode, endCode, groupMinStakeCode, groupMaxStakeCode);
                    setThousandSectionByReflection(record, thousandSection);
                }
            }
            log.info("路面类型 '{}' 的段位信息计算完成，处理 {} 条记录", roadType, records.size());

            // 输出该分组最后一行数据的段位信息用于验证
            if (!records.isEmpty()) {
                T lastRecord = records.get(records.size() - 1);
                String startCode = getStartCodeByReflection(lastRecord);
                String endCode = getEndCodeByReflection(lastRecord);
                String hundredSection = getHundredSectionByReflection(lastRecord);
                String thousandSection = getThousandSectionByReflection(lastRecord);
                log.info("路面类型 '{}' 最后一条记录段位信息：起始桩号={}, 结束桩号={}, 百米段={}, 公里段={}", 
                        roadType, startCode, endCode, hundredSection, thousandSection);
            }
        } catch (Exception e) {
            log.error("处理路面类型 '{}' 的段位信息时发生错误", roadType, e);
        }
    }

    /**
     * 计算优化的百米段
     * 在路面类型分组内，如果理论百米段范围超出分组实际范围，则使用分组实际范围
     */
    private static String calculateOptimalHundredSection(String startCode, String endCode, 
                                                  String groupMinCode, String groupMaxCode) {
        try {
            long startMeters = parseStakeCodeToMeters(startCode);
            long groupMinMeters = parseStakeCodeToMeters(groupMinCode);
            long groupMaxMeters = parseStakeCodeToMeters(groupMaxCode);
            
            if (startMeters < 0 || groupMinMeters < 0 || groupMaxMeters < 0) {
                return calculateHundredSection(startCode);
            }

            // 计算理论百米段范围
            long hundredStart = (startMeters / 100) * 100;
            long hundredEnd = hundredStart + 100;
            
            // 调整百米段范围，确保不超出当前路面类型分组的实际范围
            long actualHundredStart = Math.max(hundredStart, groupMinMeters);
            long actualHundredEnd = Math.min(hundredEnd, groupMaxMeters);
            
            // 如果调整后的范围与理论范围相同，说明是完整的100米区间
            if (actualHundredStart == hundredStart && actualHundredEnd == hundredEnd) {
                // 使用完整的100米区间
                return formatMetersToStakeCode(hundredStart) + "~" + formatMetersToStakeCode(hundredEnd);
            } else {
                // 使用调整后的实际范围
                return formatMetersToStakeCode(actualHundredStart) + "~" + formatMetersToStakeCode(actualHundredEnd);
            }
            
        } catch (Exception e) {
            log.warn("计算百米段失败，使用默认方法：{}", e.getMessage());
            return calculateHundredSection(startCode);
        }
    }

    /**
     * 计算优化的公里段
     * 在路面类型分组内，如果理论公里段范围超出分组实际范围，则使用分组实际范围
     */
    private static String calculateOptimalThousandSection(String startCode, String endCode, 
                                                   String groupMinCode, String groupMaxCode) {
        try {
            long startMeters = parseStakeCodeToMeters(startCode);
            long groupMinMeters = parseStakeCodeToMeters(groupMinCode);
            long groupMaxMeters = parseStakeCodeToMeters(groupMaxCode);
            
            if (startMeters < 0 || groupMinMeters < 0 || groupMaxMeters < 0) {
                return calculateThousandSection(startCode);
            }

            // 计算理论公里段范围
            long thousandStart = (startMeters / 1000) * 1000;
            long thousandEnd = thousandStart + 1000;
            
            // 调整公里段范围，确保不超出当前路面类型分组的实际范围
            long actualThousandStart = Math.max(thousandStart, groupMinMeters);
            long actualThousandEnd = Math.min(thousandEnd, groupMaxMeters);
            
            // 如果调整后的范围与理论范围相同，说明是完整的1000米区间
            if (actualThousandStart == thousandStart && actualThousandEnd == thousandEnd) {
                // 使用完整的1000米区间
                return formatMetersToStakeCode(thousandStart) + "~" + formatMetersToStakeCode(thousandEnd);
            } else {
                // 使用调整后的实际范围
                return formatMetersToStakeCode(actualThousandStart) + "~" + formatMetersToStakeCode(actualThousandEnd);
            }
            
        } catch (Exception e) {
            log.warn("计算公里段失败，使用默认方法：{}", e.getMessage());
            return calculateThousandSection(startCode);
        }
    }

    /**
     * 通过反射获取对象的roadType属性
     */
    private static <T> String getRoadTypeByReflection(T obj) {
        try {
            java.lang.reflect.Method method = obj.getClass().getMethod("getRoadType");
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }
} 