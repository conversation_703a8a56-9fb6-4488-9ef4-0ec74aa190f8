package com.tunnel.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 根据用户角色获取可查看审核状态
 *
 * <AUTHOR>
 * 2025年07月26日 12:42
 */
@Slf4j
public class CommonUtil {

    /**
     * 格式化时间值为标准格式
     *
     * @param timeValue 时间值
     * @return 格式化后的时间字符串
     */
    public static String formatTimeValue(Object timeValue) {
        if (timeValue == null) {
            return "";
        }
        try {
            // 如果是Date类型
            if (timeValue instanceof Date) {
                return DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", (Date) timeValue);
            }
            // 如果是字符串类型，尝试解析并格式化
            String timeStr = timeValue.toString();
            // 如果是ISO格式 (2022-02-17T16:46:10)
            if (timeStr.contains("T")) {
                // 替换T为空格，并截取到秒
                timeStr = timeStr.replace("T", " ");
                if (timeStr.contains(".")) {
                    timeStr = timeStr.substring(0, timeStr.indexOf("."));
                }
                return timeStr;
            }
            // 其他格式直接返回
            return timeStr;
        } catch (Exception e) {
            // 如果解析失败，返回原始字符串
            return timeValue.toString();
        }
    }

}
