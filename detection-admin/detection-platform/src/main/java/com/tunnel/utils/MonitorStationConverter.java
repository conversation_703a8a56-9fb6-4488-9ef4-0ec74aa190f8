package com.tunnel.utils;

import com.tunnel.domain.MonitorStation;
import com.tunnel.domain.dto.MonitorStationDTO;
import com.tunnel.domain.vo.MonitorStationVO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 监测站点转换工具类
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
public class MonitorStationConverter {

    /**
     * DTO转Entity
     */
    public static MonitorStation dtoToEntity(MonitorStationDTO dto) {
        if (dto == null) {
            return null;
        }
        MonitorStation entity = new MonitorStation();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * Entity转VO
     */
    public static MonitorStationVO entityToVO(MonitorStation entity) {
        if (entity == null) {
            return null;
        }
        MonitorStationVO vo = new MonitorStationVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * Entity列表转VO列表
     */
    public static List<MonitorStationVO> entityListToVOList(List<MonitorStation> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        List<MonitorStationVO> voList = new ArrayList<>();
        for (MonitorStation entity : entityList) {
            voList.add(entityToVO(entity));
        }
        return voList;
    }

    /**
     * Entity转DTO
     */
    public static MonitorStationDTO entityToDTO(MonitorStation entity) {
        if (entity == null) {
            return null;
        }
        MonitorStationDTO dto = new MonitorStationDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * VO转DTO
     */
    public static MonitorStationDTO voToDTO(MonitorStationVO vo) {
        if (vo == null) {
            return null;
        }
        MonitorStationDTO dto = new MonitorStationDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }

    /**
     * DTO转VO
     */
    public static MonitorStationVO dtoToVO(MonitorStationDTO dto) {
        if (dto == null) {
            return null;
        }
        MonitorStationVO vo = new MonitorStationVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }
}
