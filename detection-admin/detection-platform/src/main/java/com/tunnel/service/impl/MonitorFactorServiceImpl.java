package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorFactor;
import com.tunnel.mapper.MonitorFactorMapper;
import com.tunnel.service.MonitorFactorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测因子Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorFactorServiceImpl implements MonitorFactorService
{
    @Autowired
    private MonitorFactorMapper monitorFactorMapper;

    /**
     * 查询监测因子
     * 
     * @param id 监测因子主键
     * @return 监测因子
     */
    @Override
    public MonitorFactor selectMonitorFactorById(Long id)
    {
        return monitorFactorMapper.selectMonitorFactorById(id);
    }

    /**
     * 查询监测因子列表
     * 
     * @param monitorFactor 监测因子
     * @return 监测因子
     */
    @Override
    public List<MonitorFactor> selectMonitorFactorList(MonitorFactor monitorFactor)
    {
        return monitorFactorMapper.selectMonitorFactorList(monitorFactor);
    }

    /**
     * 新增监测因子
     * 
     * @param monitorFactor 监测因子
     * @return 结果
     */
    @Override
    public int insertMonitorFactor(MonitorFactor monitorFactor)
    {
        monitorFactor.setCreateTime(DateUtils.getNowDate());
        return monitorFactorMapper.insertMonitorFactor(monitorFactor);
    }

    /**
     * 修改监测因子
     * 
     * @param monitorFactor 监测因子
     * @return 结果
     */
    @Override
    public int updateMonitorFactor(MonitorFactor monitorFactor)
    {
        monitorFactor.setUpdateTime(DateUtils.getNowDate());
        return monitorFactorMapper.updateMonitorFactor(monitorFactor);
    }

    /**
     * 批量删除监测因子
     * 
     * @param ids 需要删除的监测因子主键
     * @return 结果
     */
    @Override
    public int deleteMonitorFactorByIds(Long[] ids)
    {
        return monitorFactorMapper.deleteMonitorFactorByIds(ids);
    }

    /**
     * 删除监测因子信息
     * 
     * @param id 监测因子主键
     * @return 结果
     */
    @Override
    public int deleteMonitorFactorById(Long id)
    {
        return monitorFactorMapper.deleteMonitorFactorById(id);
    }
}
