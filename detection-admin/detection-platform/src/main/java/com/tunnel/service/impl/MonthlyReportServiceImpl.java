package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonthlyReport;
import com.tunnel.mapper.MonthlyReportMapper;
import com.tunnel.service.MonthlyReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 月报Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonthlyReportServiceImpl implements MonthlyReportService
{
    @Autowired
    private MonthlyReportMapper monthlyReportMapper;

    /**
     * 查询月报
     * 
     * @param id 月报主键
     * @return 月报
     */
    @Override
    public MonthlyReport selectMonthlyReportById(Long id)
    {
        return monthlyReportMapper.selectMonthlyReportById(id);
    }

    /**
     * 查询月报列表
     * 
     * @param monthlyReport 月报
     * @return 月报
     */
    @Override
    public List<MonthlyReport> selectMonthlyReportList(MonthlyReport monthlyReport)
    {
        return monthlyReportMapper.selectMonthlyReportList(monthlyReport);
    }

    /**
     * 新增月报
     * 
     * @param monthlyReport 月报
     * @return 结果
     */
    @Override
    public int insertMonthlyReport(MonthlyReport monthlyReport)
    {
        monthlyReport.setCreateTime(DateUtils.getNowDate());
        return monthlyReportMapper.insertMonthlyReport(monthlyReport);
    }

    /**
     * 修改月报
     * 
     * @param monthlyReport 月报
     * @return 结果
     */
    @Override
    public int updateMonthlyReport(MonthlyReport monthlyReport)
    {
        monthlyReport.setUpdateTime(DateUtils.getNowDate());
        return monthlyReportMapper.updateMonthlyReport(monthlyReport);
    }

    /**
     * 批量删除月报
     * 
     * @param ids 需要删除的月报主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyReportByIds(Long[] ids)
    {
        return monthlyReportMapper.deleteMonthlyReportByIds(ids);
    }

    /**
     * 删除月报信息
     * 
     * @param id 月报主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyReportById(Long id)
    {
        return monthlyReportMapper.deleteMonthlyReportById(id);
    }
}
