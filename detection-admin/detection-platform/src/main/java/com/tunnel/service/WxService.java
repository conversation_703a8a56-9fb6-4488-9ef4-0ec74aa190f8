package com.tunnel.service;

import com.alibaba.fastjson2.JSONObject;
import com.tunnel.common.core.redis.RedisCache;
import com.tunnel.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.security.MessageDigest;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Service
public class WxService {
    private static final Logger log = LoggerFactory.getLogger(WxService.class);

    @Autowired
    private RedisCache redisCache;
 
    @Value("${wx.appId:wx63de2012dca0b9b8}")
    private  String appId;
 
    @Value("${wx.appSecret:9c4372a0cbba88062c9846fbfd813f43}")
    private  String appSecret;
    // 你在微信后台配置的Token，和配置页面填写的一致！
    @Value("${wx.token:23ijejd23898x823db237}")
    private String token;
 
    private static RestTemplate restTemplate = new RestTemplate();
 
 
    /**
     * 获取access_token,每个公众账号一天请求2000次Access_Token
     * @return
     */
    public String getAccessToken() {
        String accessToken = redisCache.getCacheObject("wxAccessToken");
        if (StringUtils.isBlank(accessToken)) {
            String interfaceUrl = "https://api.weixin.qq.com/cgi-bin/token";
            String url = interfaceUrl + "?grant_type=client_credential" + "&appid=" + appId + "&secret=" + appSecret;
            String result = restTemplate.getForObject(url, String.class);
            JSONObject jsonObject = JSONObject.parseObject(result);
            log.info("access_token：{}", jsonObject);
            accessToken = jsonObject.getString("access_token");
            if (StringUtils.isNotBlank(accessToken)) {
                redisCache.setCacheObject("wxAccessToken", accessToken, 1, TimeUnit.HOURS);
            }
        }
        log.info("accessToken：{}", accessToken);
        return accessToken;
    }


    /**
     * 消息推送配置 验证请求的合法性
     * @param signature
     * @param timestamp
     * @param nonce
     * @return
     */
    public boolean checkSignature(String signature, String timestamp, String nonce) {
        try {
            String[] arr = new String[]{token, timestamp, nonce};
            Arrays.sort(arr); // 字典序排序
            StringBuilder sb = new StringBuilder();
            for (String s : arr) {
                sb.append(s);
            }

            // SHA1加密
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] digest = md.digest(sb.toString().getBytes());
            StringBuilder hexStr = new StringBuilder();
            for (byte b : digest) {
                String shaHex = Integer.toHexString(b & 0xFF);
                if (shaHex.length() < 2) {
                    hexStr.append(0);
                }
                hexStr.append(shaHex);
            }

            return hexStr.toString().equals(signature);
        } catch (Exception e) {
            return false;
        }
    }
}