package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorType;
import com.tunnel.mapper.MonitorTypeMapper;
import com.tunnel.service.MonitorTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorTypeServiceImpl implements MonitorTypeService
{
    @Autowired
    private MonitorTypeMapper monitorTypeMapper;

    /**
     * 查询监测类型
     * 
     * @param id 监测类型主键
     * @return 监测类型
     */
    @Override
    public MonitorType selectMonitorTypeById(Long id)
    {
        return monitorTypeMapper.selectMonitorTypeById(id);
    }

    /**
     * 查询监测类型列表
     * 
     * @param monitorType 监测类型
     * @return 监测类型
     */
    @Override
    public List<MonitorType> selectMonitorTypeList(MonitorType monitorType)
    {
        return monitorTypeMapper.selectMonitorTypeList(monitorType);
    }

    /**
     * 新增监测类型
     * 
     * @param monitorType 监测类型
     * @return 结果
     */
    @Override
    public int insertMonitorType(MonitorType monitorType)
    {
        monitorType.setCreateTime(DateUtils.getNowDate());
        return monitorTypeMapper.insertMonitorType(monitorType);
    }

    /**
     * 修改监测类型
     * 
     * @param monitorType 监测类型
     * @return 结果
     */
    @Override
    public int updateMonitorType(MonitorType monitorType)
    {
        monitorType.setUpdateTime(DateUtils.getNowDate());
        return monitorTypeMapper.updateMonitorType(monitorType);
    }

    /**
     * 批量删除监测类型
     * 
     * @param ids 需要删除的监测类型主键
     * @return 结果
     */
    @Override
    public int deleteMonitorTypeByIds(Long[] ids)
    {
        return monitorTypeMapper.deleteMonitorTypeByIds(ids);
    }

    /**
     * 删除监测类型信息
     * 
     * @param id 监测类型主键
     * @return 结果
     */
    @Override
    public int deleteMonitorTypeById(Long id)
    {
        return monitorTypeMapper.deleteMonitorTypeById(id);
    }
}
