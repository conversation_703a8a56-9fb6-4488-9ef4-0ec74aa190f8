package com.tunnel.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.IntegrationSystem;
import com.tunnel.mapper.IntegrationSystemMapper;
import com.tunnel.service.IntegrationSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class IntegrationSystemServiceImpl implements IntegrationSystemService
{
    @Autowired
    private IntegrationSystemMapper integrationSystemMapper;

    /**
     * 查询系统信息
     * 
     * @param id 系统信息主键
     * @return 系统信息
     */
    @Override
    public IntegrationSystem selectIntegrationSystemById(Long id)
    {
        return integrationSystemMapper.selectIntegrationSystemById(id);
    }

    /**
     * 查询系统信息列表
     * 
     * @param integrationSystem 系统信息
     * @return 系统信息
     */
    @Override
    public List<IntegrationSystem> selectIntegrationSystemList(IntegrationSystem integrationSystem)
    {
        if (integrationSystem.getPageNum() != null && integrationSystem.getPageSize() != null) {
            Page page = PageHelper.startPage(integrationSystem.getPageNum(), integrationSystem.getPageSize());
        }
        return integrationSystemMapper.selectIntegrationSystemList(integrationSystem);
    }

    /**
     * 新增系统信息
     * 
     * @param integrationSystem 系统信息
     * @return 结果
     */
    @Override
    public int insertIntegrationSystem(IntegrationSystem integrationSystem)
    {
        integrationSystem.setCreateTime(DateUtils.getNowDate());
        return integrationSystemMapper.insertIntegrationSystem(integrationSystem);
    }

    /**
     * 修改系统信息
     * 
     * @param integrationSystem 系统信息
     * @return 结果
     */
    @Override
    public int updateIntegrationSystem(IntegrationSystem integrationSystem)
    {
        integrationSystem.setUpdateTime(DateUtils.getNowDate());
        return integrationSystemMapper.updateIntegrationSystem(integrationSystem);
    }

    /**
     * 批量删除系统信息
     * 
     * @param ids 需要删除的系统信息主键
     * @return 结果
     */
    @Override
    public int deleteIntegrationSystemByIds(Long[] ids)
    {
        return integrationSystemMapper.deleteIntegrationSystemByIds(ids);
    }

    /**
     * 删除系统信息信息
     * 
     * @param id 系统信息主键
     * @return 结果
     */
    @Override
    public int deleteIntegrationSystemById(Long id)
    {
        return integrationSystemMapper.deleteIntegrationSystemById(id);
    }
}
