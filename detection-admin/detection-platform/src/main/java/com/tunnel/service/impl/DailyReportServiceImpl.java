package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.DailyReport;
import com.tunnel.mapper.DailyReportMapper;
import com.tunnel.service.DailyReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 日报Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class DailyReportServiceImpl implements DailyReportService
{
    @Autowired
    private DailyReportMapper dailyReportMapper;

    /**
     * 查询日报
     * 
     * @param id 日报主键
     * @return 日报
     */
    @Override
    public DailyReport selectDailyReportById(Long id)
    {
        return dailyReportMapper.selectDailyReportById(id);
    }

    /**
     * 查询日报列表
     * 
     * @param dailyReport 日报
     * @return 日报
     */
    @Override
    public List<DailyReport> selectDailyReportList(DailyReport dailyReport)
    {
        return dailyReportMapper.selectDailyReportList(dailyReport);
    }

    /**
     * 新增日报
     * 
     * @param dailyReport 日报
     * @return 结果
     */
    @Override
    public int insertDailyReport(DailyReport dailyReport)
    {
        dailyReport.setCreateTime(DateUtils.getNowDate());
        return dailyReportMapper.insertDailyReport(dailyReport);
    }

    /**
     * 修改日报
     * 
     * @param dailyReport 日报
     * @return 结果
     */
    @Override
    public int updateDailyReport(DailyReport dailyReport)
    {
        dailyReport.setUpdateTime(DateUtils.getNowDate());
        return dailyReportMapper.updateDailyReport(dailyReport);
    }

    /**
     * 批量删除日报
     * 
     * @param ids 需要删除的日报主键
     * @return 结果
     */
    @Override
    public int deleteDailyReportByIds(Long[] ids)
    {
        return dailyReportMapper.deleteDailyReportByIds(ids);
    }

    /**
     * 删除日报信息
     * 
     * @param id 日报主键
     * @return 结果
     */
    @Override
    public int deleteDailyReportById(Long id)
    {
        return dailyReportMapper.deleteDailyReportById(id);
    }
}
