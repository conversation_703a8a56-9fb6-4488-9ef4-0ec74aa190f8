package com.tunnel.service;

import com.github.pagehelper.PageInfo;
import com.tunnel.domain.CheckDTO;
import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.RequirementReport;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 设备上报数据记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface RequirementReportService
{
    /**
     * 查询设备上报数据记录
     * 
     * @param id 设备上报数据记录主键
     * @return 设备上报数据记录
     */
    public RequirementReport selectRequirementReportById(String id);

    /**
     * 查询设备上报数据记录列表
     * 
     * @param requirementReport 设备上报数据记录
     * @return 设备上报数据记录集合
     */
    public List<RequirementReport> selectRequirementReportList(RequirementReport requirementReport);

    /**
     * 新增设备上报数据记录
     * 
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    public int insertRequirementReport(RequirementReport requirementReport);

    /**
     * 修改设备上报数据记录
     * 
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    public int updateRequirementReport(RequirementReport requirementReport);

    /**
     * 批量删除设备上报数据记录
     * 
     * @param ids 需要删除的设备上报数据记录主键集合
     * @return 结果
     */
    public int deleteRequirementReportByIds(String ids);

    /**
     * 删除设备上报数据记录信息
     * 
     * @param id 设备上报数据记录主键
     * @return 结果
     */
    public int deleteRequirementReportById(String id);

    public List<RequirementReport> listByKey(String key);

    List<Map<String, Object>> getCheckFieldsByType(Integer type);

    PageInfo<Map<String, Object>> selectCheckListByPage(CheckDTO dto);

    Map<String, Object> selectCheckListForChart(CheckDTO dto);

    CheckDTO selectTimeRange(CheckDTO dto);
}
