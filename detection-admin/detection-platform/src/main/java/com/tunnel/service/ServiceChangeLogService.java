package com.tunnel.service;

import com.tunnel.domain.ServiceChangeLog;
import java.util.List;

/**
 * 服务区/收费站档案变更日志Service接口
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
public interface ServiceChangeLogService {
    
    /**
     * 查询服务区/收费站档案变更日志
     *
     * @param id 服务区/收费站档案变更日志主键
     * @return 服务区/收费站档案变更日志
     */
    public ServiceChangeLog selectServiceChangeLogById(Long id);

    /**
     * 查询服务区/收费站档案变更日志列表
     *
     * @param serviceChangeLog 服务区/收费站档案变更日志
     * @return 服务区/收费站档案变更日志集合
     */
    public List<ServiceChangeLog> selectServiceChangeLogList(ServiceChangeLog serviceChangeLog);

    /**
     * 根据表名和记录ID查询变更日志列表
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @return 变更日志集合
     */
    public List<ServiceChangeLog> selectServiceChangeLogByTableAndRecord(String tableName, Long recordId);

    /**
     * 根据设施名称查询变更日志列表
     *
     * @param facilityName 设施名称
     * @return 变更日志集合
     */
    public List<ServiceChangeLog> selectServiceChangeLogByFacilityName(String facilityName);

    /**
     * 新增服务区/收费站档案变更日志
     *
     * @param serviceChangeLog 服务区/收费站档案变更日志
     * @return 结果
     */
    public int insertServiceChangeLog(ServiceChangeLog serviceChangeLog);

    /**
     * 修改服务区/收费站档案变更日志
     *
     * @param serviceChangeLog 服务区/收费站档案变更日志
     * @return 结果
     */
    public int updateServiceChangeLog(ServiceChangeLog serviceChangeLog);

    /**
     * 批量删除服务区/收费站档案变更日志
     *
     * @param ids 需要删除的服务区/收费站档案变更日志主键集合
     * @return 结果
     */
    public int deleteServiceChangeLogByIds(Long[] ids);

    /**
     * 删除服务区/收费站档案变更日志信息
     *
     * @param id 服务区/收费站档案变更日志主键
     * @return 结果
     */
    public int deleteServiceChangeLogById(Long id);

    /**
     * 记录数据变更日志
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @param operationType 操作类型（INSERT/UPDATE/DELETE）
     * @param oldValues 变更前数据（JSON格式）
     * @param newValues 变更后数据（JSON格式）
     * @param remark 备注
     * @return 结果
     */
    public int recordChangeLog(String tableName, Long recordId, String operationType,
                              String oldValues, String newValues, String remark);

    /**
     * 记录数据变更日志（带操作人）
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @param operationType 操作类型（INSERT/UPDATE/DELETE）
     * @param oldValues 变更前数据（JSON格式）
     * @param newValues 变更后数据（JSON格式）
     * @param remark 备注
     * @param operatorId 操作人ID
     * @return 结果
     */
    public int recordChangeLog(String tableName, Long recordId, String operationType,
                              String oldValues, String newValues, String remark, Long operatorId);

    /**
     * 记录数据变更日志（带设施名称）
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @param operationType 操作类型（INSERT/UPDATE/DELETE）
     * @param oldValues 变更前数据（JSON格式）
     * @param newValues 变更后数据（JSON格式）
     * @param remark 备注
     * @param facilityName 设施名称
     * @return 结果
     */
    public int recordChangeLog(String tableName, Long recordId, String operationType,
                              String oldValues, String newValues, String remark, String facilityName);

    /**
     * 记录数据变更日志（带操作人和设施名称）
     *
     * @param tableName 表名
     * @param recordId 记录ID
     * @param operationType 操作类型（INSERT/UPDATE/DELETE）
     * @param oldValues 变更前数据（JSON格式）
     * @param newValues 变更后数据（JSON格式）
     * @param remark 备注
     * @param operatorId 操作人ID
     * @param facilityName 设施名称
     * @return 结果
     */
    public int recordChangeLog(String tableName, Long recordId, String operationType,
                              String oldValues, String newValues, String remark, Long operatorId, String facilityName);
}
