package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorStationUserRelation;
import com.tunnel.mapper.MonitorStationUserRelationMapper;
import com.tunnel.service.MonitorStationUserRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测站点和用户关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorStationUserRelationServiceImpl implements MonitorStationUserRelationService
{
    @Autowired
    private MonitorStationUserRelationMapper monitorStationUserRelationMapper;

    /**
     * 查询监测站点和用户关联
     * 
     * @param id 监测站点和用户关联主键
     * @return 监测站点和用户关联
     */
    @Override
    public MonitorStationUserRelation selectMonitorStationUserRelById(Long id)
    {
        return monitorStationUserRelationMapper.selectMonitorStationUserRelById(id);
    }

    /**
     * 查询监测站点和用户关联列表
     * 
     * @param monitorStationUserRelation 监测站点和用户关联
     * @return 监测站点和用户关联
     */
    @Override
    public List<MonitorStationUserRelation> selectMonitorStationUserRelList(MonitorStationUserRelation monitorStationUserRelation)
    {
        return monitorStationUserRelationMapper.selectMonitorStationUserRelList(monitorStationUserRelation);
    }

    /**
     * 新增监测站点和用户关联
     * 
     * @param monitorStationUserRelation 监测站点和用户关联
     * @return 结果
     */
    @Override
    public int insertMonitorStationUserRel(MonitorStationUserRelation monitorStationUserRelation)
    {
        monitorStationUserRelation.setCreateTime(DateUtils.getNowDate());
        return monitorStationUserRelationMapper.insertMonitorStationUserRel(monitorStationUserRelation);
    }

    /**
     * 修改监测站点和用户关联
     * 
     * @param monitorStationUserRelation 监测站点和用户关联
     * @return 结果
     */
    @Override
    public int updateMonitorStationUserRel(MonitorStationUserRelation monitorStationUserRelation)
    {
        monitorStationUserRelation.setUpdateTime(DateUtils.getNowDate());
        return monitorStationUserRelationMapper.updateMonitorStationUserRel(monitorStationUserRelation);
    }

    /**
     * 批量删除监测站点和用户关联
     * 
     * @param ids 需要删除的监测站点和用户关联主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationUserRelByIds(Long[] ids)
    {
        return monitorStationUserRelationMapper.deleteMonitorStationUserRelByIds(ids);
    }

    /**
     * 删除监测站点和用户关联信息
     * 
     * @param id 监测站点和用户关联主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationUserRelById(Long id)
    {
        return monitorStationUserRelationMapper.deleteMonitorStationUserRelById(id);
    }
}
