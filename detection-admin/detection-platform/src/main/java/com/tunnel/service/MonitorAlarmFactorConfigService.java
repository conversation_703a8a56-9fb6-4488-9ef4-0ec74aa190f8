package com.tunnel.service;

import com.tunnel.domain.MonitorAlarmFactorConfig;

import java.util.List;

/**
 * 监测预警配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorAlarmFactorConfigService
{
    /**
     * 查询监测预警配置
     * 
     * @param id 监测预警配置主键
     * @return 监测预警配置
     */
    public MonitorAlarmFactorConfig selectMonitorAlarmConfigById(Long id);

    /**
     * 查询监测预警配置列表
     * 
     * @param monitorAlarmFactorConfig 监测预警配置
     * @return 监测预警配置集合
     */
    public List<MonitorAlarmFactorConfig> selectMonitorAlarmConfigList(MonitorAlarmFactorConfig monitorAlarmFactorConfig);

    /**
     * 新增监测预警配置
     * 
     * @param monitorAlarmFactorConfig 监测预警配置
     * @return 结果
     */
    public int insertMonitorAlarmConfig(MonitorAlarmFactorConfig monitorAlarmFactorConfig);

    /**
     * 修改监测预警配置
     * 
     * @param monitorAlarmFactorConfig 监测预警配置
     * @return 结果
     */
    public int updateMonitorAlarmConfig(MonitorAlarmFactorConfig monitorAlarmFactorConfig);

    /**
     * 批量删除监测预警配置
     * 
     * @param ids 需要删除的监测预警配置主键集合
     * @return 结果
     */
    public int deleteMonitorAlarmConfigByIds(Long[] ids);

    /**
     * 删除监测预警配置信息
     * 
     * @param id 监测预警配置主键
     * @return 结果
     */
    public int deleteMonitorAlarmConfigById(Long id);
}
