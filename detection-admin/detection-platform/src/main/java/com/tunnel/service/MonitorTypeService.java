package com.tunnel.service;

import com.tunnel.domain.MonitorType;

import java.util.List;

/**
 * 监测类型Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorTypeService
{
    /**
     * 查询监测类型
     * 
     * @param id 监测类型主键
     * @return 监测类型
     */
    public MonitorType selectMonitorTypeById(Long id);

    /**
     * 查询监测类型列表
     * 
     * @param monitorType 监测类型
     * @return 监测类型集合
     */
    public List<MonitorType> selectMonitorTypeList(MonitorType monitorType);

    /**
     * 新增监测类型
     * 
     * @param monitorType 监测类型
     * @return 结果
     */
    public int insertMonitorType(MonitorType monitorType);

    /**
     * 修改监测类型
     * 
     * @param monitorType 监测类型
     * @return 结果
     */
    public int updateMonitorType(MonitorType monitorType);

    /**
     * 批量删除监测类型
     * 
     * @param ids 需要删除的监测类型主键集合
     * @return 结果
     */
    public int deleteMonitorTypeByIds(Long[] ids);

    /**
     * 删除监测类型信息
     * 
     * @param id 监测类型主键
     * @return 结果
     */
    public int deleteMonitorTypeById(Long id);
}
