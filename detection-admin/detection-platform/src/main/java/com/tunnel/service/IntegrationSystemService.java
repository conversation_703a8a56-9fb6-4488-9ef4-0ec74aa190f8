package com.tunnel.service;

import com.tunnel.domain.IntegrationSystem;

import java.util.List;

/**
 * 系统信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface IntegrationSystemService
{
    /**
     * 查询系统信息
     * 
     * @param id 系统信息主键
     * @return 系统信息
     */
    public IntegrationSystem selectIntegrationSystemById(Long id);

    /**
     * 查询系统信息列表
     * 
     * @param integrationSystem 系统信息
     * @return 系统信息集合
     */
    public List<IntegrationSystem> selectIntegrationSystemList(IntegrationSystem integrationSystem);

    /**
     * 新增系统信息
     * 
     * @param integrationSystem 系统信息
     * @return 结果
     */
    public int insertIntegrationSystem(IntegrationSystem integrationSystem);

    /**
     * 修改系统信息
     * 
     * @param integrationSystem 系统信息
     * @return 结果
     */
    public int updateIntegrationSystem(IntegrationSystem integrationSystem);

    /**
     * 批量删除系统信息
     * 
     * @param ids 需要删除的系统信息主键集合
     * @return 结果
     */
    public int deleteIntegrationSystemByIds(Long[] ids);

    /**
     * 删除系统信息信息
     * 
     * @param id 系统信息主键
     * @return 结果
     */
    public int deleteIntegrationSystemById(Long id);
}
