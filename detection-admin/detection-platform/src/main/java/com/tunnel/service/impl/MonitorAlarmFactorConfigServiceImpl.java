package com.tunnel.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorAlarmFactorConfig;
import com.tunnel.mapper.MonitorAlarmFactorConfigMapper;
import com.tunnel.service.MonitorAlarmFactorConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测预警配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorAlarmFactorConfigServiceImpl implements MonitorAlarmFactorConfigService
{
    @Autowired
    private MonitorAlarmFactorConfigMapper monitorAlarmFactorConfigMapper;

    /**
     * 查询监测预警配置
     * 
     * @param id 监测预警配置主键
     * @return 监测预警配置
     */
    @Override
    public MonitorAlarmFactorConfig selectMonitorAlarmConfigById(Long id)
    {
        return monitorAlarmFactorConfigMapper.selectMonitorAlarmConfigById(id);
    }

    /**
     * 查询监测预警配置列表
     * 
     * @param monitorAlarmFactorConfig 监测预警配置
     * @return 监测预警配置
     */
    @Override
    public List<MonitorAlarmFactorConfig> selectMonitorAlarmConfigList(MonitorAlarmFactorConfig monitorAlarmFactorConfig)
    {
        if (monitorAlarmFactorConfig.getPageNum() != null && monitorAlarmFactorConfig.getPageSize() != null) {
            Page page = PageHelper.startPage(monitorAlarmFactorConfig.getPageNum(), monitorAlarmFactorConfig.getPageSize());
        }
        return monitorAlarmFactorConfigMapper.selectMonitorAlarmConfigList(monitorAlarmFactorConfig);
    }

    /**
     * 新增监测预警配置
     * 
     * @param monitorAlarmFactorConfig 监测预警配置
     * @return 结果
     */
    @Override
    public int insertMonitorAlarmConfig(MonitorAlarmFactorConfig monitorAlarmFactorConfig)
    {
        monitorAlarmFactorConfig.setCreateTime(DateUtils.getNowDate());
        return monitorAlarmFactorConfigMapper.insertMonitorAlarmConfig(monitorAlarmFactorConfig);
    }

    /**
     * 修改监测预警配置
     * 
     * @param monitorAlarmFactorConfig 监测预警配置
     * @return 结果
     */
    @Override
    public int updateMonitorAlarmConfig(MonitorAlarmFactorConfig monitorAlarmFactorConfig)
    {
        monitorAlarmFactorConfig.setUpdateTime(DateUtils.getNowDate());
        return monitorAlarmFactorConfigMapper.updateMonitorAlarmConfig(monitorAlarmFactorConfig);
    }

    /**
     * 批量删除监测预警配置
     * 
     * @param ids 需要删除的监测预警配置主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmConfigByIds(Long[] ids)
    {
        return monitorAlarmFactorConfigMapper.deleteMonitorAlarmConfigByIds(ids);
    }

    /**
     * 删除监测预警配置信息
     * 
     * @param id 监测预警配置主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmConfigById(Long id)
    {
        return monitorAlarmFactorConfigMapper.deleteMonitorAlarmConfigById(id);
    }
}
