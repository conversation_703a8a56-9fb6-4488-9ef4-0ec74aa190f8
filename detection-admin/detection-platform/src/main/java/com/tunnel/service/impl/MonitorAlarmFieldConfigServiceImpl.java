package com.tunnel.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorAlarmFieldConfig;
import com.tunnel.mapper.MonitorAlarmFieldConfigMapper;
import com.tunnel.service.MonitorAlarmFieldConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测预警指标配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Service
public class MonitorAlarmFieldConfigServiceImpl implements MonitorAlarmFieldConfigService
{
    @Autowired
    private MonitorAlarmFieldConfigMapper monitorAlarmFieldConfigMapper;

    /**
     * 查询监测预警指标配置
     * 
     * @param id 监测预警指标配置主键
     * @return 监测预警指标配置
     */
    @Override
    public MonitorAlarmFieldConfig selectMonitorAlarmFieldConfigById(Long id)
    {
        return monitorAlarmFieldConfigMapper.selectMonitorAlarmFieldConfigById(id);
    }

    /**
     * 查询监测预警指标配置列表
     * 
     * @param monitorAlarmFieldConfig 监测预警指标配置
     * @return 监测预警指标配置
     */
    @Override
    public List<MonitorAlarmFieldConfig> selectMonitorAlarmFieldConfigList(MonitorAlarmFieldConfig monitorAlarmFieldConfig)
    {
        if (monitorAlarmFieldConfig.getPageNum() != null && monitorAlarmFieldConfig.getPageSize() != null) {
            Page page = PageHelper.startPage(monitorAlarmFieldConfig.getPageNum(), monitorAlarmFieldConfig.getPageSize());
        }
        return monitorAlarmFieldConfigMapper.selectMonitorAlarmFieldConfigList(monitorAlarmFieldConfig);
    }

    /**
     * 新增监测预警指标配置
     * 
     * @param monitorAlarmFieldConfig 监测预警指标配置
     * @return 结果
     */
    @Override
    public int insertMonitorAlarmFieldConfig(MonitorAlarmFieldConfig monitorAlarmFieldConfig)
    {
        monitorAlarmFieldConfig.setCreateTime(DateUtils.getNowDate());
        return monitorAlarmFieldConfigMapper.insertMonitorAlarmFieldConfig(monitorAlarmFieldConfig);
    }

    /**
     * 修改监测预警指标配置
     * 
     * @param monitorAlarmFieldConfig 监测预警指标配置
     * @return 结果
     */
    @Override
    public int updateMonitorAlarmFieldConfig(MonitorAlarmFieldConfig monitorAlarmFieldConfig)
    {
        monitorAlarmFieldConfig.setUpdateTime(DateUtils.getNowDate());
        return monitorAlarmFieldConfigMapper.updateMonitorAlarmFieldConfig(monitorAlarmFieldConfig);
    }

    /**
     * 批量删除监测预警指标配置
     * 
     * @param ids 需要删除的监测预警指标配置主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmFieldConfigByIds(Long[] ids)
    {
        return monitorAlarmFieldConfigMapper.deleteMonitorAlarmFieldConfigByIds(ids);
    }

    /**
     * 删除监测预警指标配置信息
     * 
     * @param id 监测预警指标配置主键
     * @return 结果
     */
    @Override
    public int deleteMonitorAlarmFieldConfigById(Long id)
    {
        return monitorAlarmFieldConfigMapper.deleteMonitorAlarmFieldConfigById(id);
    }
}
