package com.tunnel.service.impl;

import com.tunnel.common.annotation.DataSource;
import com.tunnel.common.enums.DataSourceType;
import com.tunnel.domain.SecomeaRawRow;
import com.tunnel.mapper.SecomeaDynamicRawMapper;
import com.tunnel.service.SecomeaRawAccessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SecomeaRawAccessServiceImpl implements SecomeaRawAccessService {

    @Resource
    private SecomeaDynamicRawMapper secomeaDynamicRawMapper;

    @Override
    @DataSource(DataSourceType.SLAVE2)
    public List<SecomeaRawRow> selectBatch(String tableName, int limitSize) {
        // 不使用 offsetId；直接每次取 status=0 的前 N 条，避免跳过数据
        return secomeaDynamicRawMapper.selectBatchFromRaw(tableName, null, limitSize);
    }

    @Override
    @DataSource(DataSourceType.SLAVE2)
    public void markProcessed(String tableName, List<String> ids) {
        if (ids == null || ids.isEmpty()) return;
        secomeaDynamicRawMapper.markProcessed(tableName, ids);
    }

    @Override
    @DataSource(DataSourceType.SLAVE2)
    public List<String> selectDistinctTimes(String tableName, String lastTime, int limitSize) {
        return secomeaDynamicRawMapper.selectDistinctTimes(tableName, lastTime, limitSize);
    }

    @Override
    @DataSource(DataSourceType.SLAVE2)
    public List<SecomeaRawRow> selectByTimes(String tableName, List<String> times) {
        if (times == null || times.isEmpty()) return java.util.Collections.emptyList();
        return secomeaDynamicRawMapper.selectByTimes(tableName, times);
    }

    @Override
    @DataSource(DataSourceType.SLAVE2)
    public void markProcessedByTimes(String tableName, List<String> times) {
        if (times == null || times.isEmpty()) return;
        secomeaDynamicRawMapper.markProcessedByTimes(tableName, times);
    }
}


