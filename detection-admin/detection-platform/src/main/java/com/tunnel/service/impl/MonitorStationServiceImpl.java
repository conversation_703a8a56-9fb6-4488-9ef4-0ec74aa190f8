package com.tunnel.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.CommonUtil;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.MonitorFactor;
import com.tunnel.domain.MonitorStation;
import com.tunnel.domain.Requirement;
import com.tunnel.mapper.*;
import com.tunnel.service.MonitorStationService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.Calendar;
import java.util.stream.Collectors;

/**
 * 监测站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorStationServiceImpl implements MonitorStationService {
    @Resource
    private MonitorStationMapper monitorStationMapper;
    @Resource
    private MonitorSewageMapper monitorSewageMapper;
    @Resource
    private MonitorAirMapper monitorAirMapper;
    @Resource
    private MonitorNoiseMapper monitorNoiseMapper;
    @Resource
    private MonitorFactorMapper monitorFactorMapper;
    @Resource
    private RequirementMapper requirementMapper;

    /**
     * 查询监测站点
     *
     * @param id 监测站点主键
     * @return 监测站点
     */
    @Override
    public MonitorStation selectMonitorStationById(Long id) {
        return monitorStationMapper.selectMonitorStationById(id);
    }

    /**
     * 查询监测站点列表
     *
     * @param monitorStation 监测站点
     * @return 监测站点
     */
    @Override
    public List<MonitorStation> selectMonitorStationList(MonitorStation monitorStation) {
        if(Objects.nonNull(monitorStation.getPageNum()) &&  Objects.nonNull(monitorStation.getPageSize())){
            Page page = PageHelper.startPage(monitorStation.getPageNum(), monitorStation.getPageSize());
        }
        return monitorStationMapper.selectMonitorStationList(monitorStation);
    }

    /**
     * 新增监测站点
     *
     * @param monitorStation 监测站点
     * @return 结果
     */
    @Override
    public int insertMonitorStation(MonitorStation monitorStation) {
        monitorStation.setCreateTime(DateUtils.getNowDate());
        return monitorStationMapper.insertMonitorStation(monitorStation);
    }

    /**
     * 修改监测站点
     *
     * @param monitorStation 监测站点
     * @return 结果
     */
    @Override
    public int updateMonitorStation(MonitorStation monitorStation) {
        monitorStation.setUpdateTime(DateUtils.getNowDate());
        return monitorStationMapper.updateMonitorStation(monitorStation);
    }

    /**
     * 批量删除监测站点
     *
     * @param ids 需要删除的监测站点主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationByIds(Long[] ids) {
        return monitorStationMapper.deleteMonitorStationByIds(ids);
    }

    /**
     * 删除监测站点信息
     *
     * @param id 监测站点主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationById(Long id) {
        return monitorStationMapper.deleteMonitorStationById(id);
    }

    @Override
    public List<MonitorStation> selectMonitorStationByType(MonitorStation monitorStation) {
        return monitorStationMapper.selectMonitorStationByType(monitorStation);
    }

    @Override
    public PageInfo<Map<String, Object>>  selectMonitorListByPage(MonitorDTO dto) {
        if (Objects.isNull(dto.getStartTime()) || Objects.isNull(dto.getEndTime())) {
            throw new ServiceException("时间参数不能为空");
        }
        List<Map<String, Object>> resultList = Lists.newArrayList();
        Page page= null;
        if(dto.isPage()){
            page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        if (Objects.equals(dto.getType(), 1)) {
            //查询污水
            resultList = monitorSewageMapper.selectByParams(dto);
        }else if (Objects.equals(dto.getType(), 2)) {
            //查询大气
            resultList = monitorAirMapper.selectByParams(dto);
        }else if (Objects.equals(dto.getType(), 3)) {
            //查询噪声
            resultList = monitorNoiseMapper.selectByParams(dto);
        }else if (Objects.equals(dto.getType(), 4)) {
            //查询固废
        }
        // 为resultList中的每条记录添加对应的factor信息
        if(dto.isPage()) {
            //分页直接返回结果
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(resultList);
            if(dto.isPage()){
                pageInfo.setTotal(page.getTotal());
            }
            return pageInfo;
        }
        List<MonitorFactor> factorList = monitorFactorMapper.selectMonitorFactorBySystemCode(dto.getSystemCode(),dto.getType());
        if(CollectionUtils.isEmpty(factorList)){
            throw new ServiceException("没有找到对应的因子信息");
        }
        // 将factorList转换为Map，key为code的小写形式，value为MonitorFactor对象
        Map<String, MonitorFactor> factorMap = factorList.stream().collect(Collectors.toMap(factor -> factor.getCode().toLowerCase().replace(".","_"), factor -> factor,(v1, v2) -> v1));
        //非分页
        for (Map<String, Object> result : resultList) {
            if (result == null) {
                continue;
            }
            // 创建一个新的Map来存储要添加的因子信息，避免并发修改
            Map<String, Object> factorInfo = new HashMap<>();
            // 为每个字段添加对应的factor信息
            for (String fieldName : result.keySet()) {
                if (fieldName == null) {
                    continue;
                }
                String lowerFieldName = fieldName.toLowerCase();
                MonitorFactor factor = factorMap.get(lowerFieldName);
                if (factor != null) {
                    // 为每个字段添加factor的属性信息
                    factorInfo.put(fieldName + "_factor", factor);
                    factorInfo.put(fieldName + "_name", factor.getName());
                    factorInfo.put(fieldName + "_unit", factor.getUnit());
                    factorInfo.put(fieldName + "_maxValue", factor.getMaxValue());
                    factorInfo.put(fieldName + "_minValue", factor.getMinValue());
                    factorInfo.put(fieldName + "_sensorId", factor.getSensorId());
                    factorInfo.put(fieldName + "_factorIndex", factor.getFactorIndex());
                }
            }
            // 将因子信息添加到结果中
            result.putAll(factorInfo);
        }
        //返回结果
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(resultList);
        if(dto.isPage()){
            pageInfo.setTotal(page.getTotal());
        }
        return pageInfo;
    }


    @Override
    public Map<String, Object> selectMonitorListForChart(MonitorDTO dto) {
        dto.setPage(false);
        PageInfo<Map<String, Object>> pageInfo = this.selectMonitorListByPage(dto);
        List<Map<String, Object>> resultList = pageInfo.getList();
        if (CollectionUtils.isEmpty(resultList)) {
            return new HashMap<>();
        }
        // 获取第一条记录的所有字段名（排除因子相关的字段）
        Set<String> fieldNames = new HashSet<>();
        for (Map<String, Object> record : resultList) {
            if (record == null) {
                continue;
            }
            for (String key : record.keySet()) {
                if (key == null) {
                    continue;
                }
                // 排除因子相关的字段（以_factor, _name, _unit等结尾的字段）
                if (!key.endsWith("_factor") && !key.endsWith("_name") && 
                    !key.endsWith("_unit") && !key.endsWith("_maxValue") && 
                    !key.endsWith("_minValue") && !key.endsWith("_sensorId") && 
                    !key.endsWith("_factorIndex")
                        &&!key.equals("id") && !key.equals("monitor_code") &&
                        !key.equals("b_key") && !key.equals("topic") &&
                        !key.equals("remark") && !key.equals("create_time") &&
                        !key.equals("update_time") && !key.equals("creator")
                        && !key.equals("modifier") ) {
                    fieldNames.add(key);
                }
            }
        }
        // 为每个字段创建一个数据集合
        Map<String, Object> chartData = new HashMap<>();
        for (String fieldName : fieldNames) {
            List<Object> fieldValues = new ArrayList<>();
            List<String> timeLabels = new ArrayList<>();
            for (Map<String, Object> record : resultList) {
                if (record == null) {
                    continue;
                }
                // 添加时间标签（假设有create_time字段）
                if (record.containsKey("create_time")) {
                    Object timeValue = record.get("create_time");
                    if (timeValue != null) {
                        String timeStr = CommonUtil.formatTimeValue(timeValue);
                        timeLabels.add(timeStr);
                    }
                }
                // 添加字段值
                Object value = record.get(fieldName);
                fieldValues.add(value);
            }
            // 将字段数据和时间标签一起存储
            Map<String, Object> fieldData = new HashMap<>();
            fieldData.put("values", fieldValues);
            fieldData.put("times", timeLabels);
            // 如果有对应的因子信息，也添加进去
            String factorKey = fieldName + "_factor";
            Map<String, Object> firstRecord = resultList.get(0);
            if (firstRecord != null && firstRecord.containsKey(factorKey)) {
                Object factorObj = firstRecord.get(factorKey);
                if (factorObj instanceof MonitorFactor) {
                    MonitorFactor factor = (MonitorFactor) factorObj;
                    if (factor != null) {
                        fieldData.put("factorName", factor.getName());
                        fieldData.put("unit", factor.getUnit());
                        fieldData.put("maxValue", factor.getMaxValue());
                        fieldData.put("minValue", factor.getMinValue());
                    }
                }
            }
            chartData.put(fieldName, fieldData);
        }
        return chartData;
    }

    @Override
    public MonitorDTO selectTimeRange(MonitorDTO dto) {
        if(Objects.isNull(dto.getType())){
            throw new ServiceException("请选择监测类型");
        }
        MonitorDTO result = new MonitorDTO();
        if(Objects.equals(dto.getType(), 1)){
            //查询污水
            result = monitorSewageMapper.selectTimeRange(dto);
        } else if (Objects.equals(dto.getType(), 2)) {
            //查询大气
            result = monitorAirMapper.selectTimeRange(dto);
        } else if (Objects.equals(dto.getType(), 3)) {
            //查询噪声
            result = monitorNoiseMapper.selectTimeRange(dto);
        }else if (Objects.equals(dto.getType(), 4)) {
            //查询固废
        }
        if(Objects.nonNull(result) && Objects.nonNull(result.getEndTime())){
            Date endTime = result.getEndTime();
            //根据endTime 转换成当天0点的时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endTime);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date dayStartTime = calendar.getTime();
            result.setStartTime(dayStartTime);
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getFieldsByType(Integer type, String systemCode) {
        if (Objects.isNull(type)) {
            throw new ServiceException("监测类型不能为空");
        }
        if (Objects.isNull(systemCode)) {
            throw new ServiceException("系统编码不能为空");
        }
        // 获取该系统的所有监测因子
        List<MonitorFactor> factorList = monitorFactorMapper.selectMonitorFactorBySystemCode(systemCode,type);
        if (CollectionUtils.isEmpty(factorList)) {
            throw new ServiceException("没有找到对应的因子信息");
        }
        // 直接根据监测因子构建字段列表
        List<Map<String, Object>> fieldList = new ArrayList<>();
        for (MonitorFactor factor : factorList) {
            Map<String, Object> fieldInfo = new HashMap<>();
            // 字段编码（转换为小写并替换点号为下划线）
            String fieldCode = factor.getCode().toLowerCase().replace(".", "_");
            fieldInfo.put("fieldCode", fieldCode);
            fieldInfo.put("fieldName", factor.getName());
            // 因子信息
            fieldInfo.put("unit", factor.getUnit());
            fieldInfo.put("maxValue", factor.getMaxValue());
            fieldInfo.put("minValue", factor.getMinValue());
            fieldList.add(fieldInfo);
        }
        // 按factorIndex排序
        fieldList.sort((a, b) -> {
            Integer indexA = (Integer) a.get("factorIndex");
            Integer indexB = (Integer) b.get("factorIndex");
            if (indexA == null) indexA = 0;
            if (indexB == null) indexB = 0;
            return indexA.compareTo(indexB);
        });
        return fieldList;
    }

    @Override
    public List<MonitorStation> selectMonitorStationListWithDeviceCount(MonitorStation monitorStation) {
        // 获取站点列表
        List<MonitorStation> stationList = monitorStationMapper.selectMonitorStationList(monitorStation);

        if (stationList == null || stationList.isEmpty()) {
            return stationList;
        }

        // 为每个站点统计设备数量
        for (MonitorStation station : stationList) {
            // 查询该站点的在线设备数量
            Requirement onlineQuery = new Requirement();
            onlineQuery.setStationId(station.getId());
            onlineQuery.setStatus(1); // 在线状态
            List<Requirement> onlineDevices = requirementMapper.selectScRequirementList(onlineQuery);
            station.setOnlineDeviceCount(onlineDevices != null ? onlineDevices.size() : 0);

            // 查询该站点的离线设备数量
            Requirement offlineQuery = new Requirement();
            offlineQuery.setStationId(station.getId());
            offlineQuery.setStatus(0); // 离线状态
            List<Requirement> offlineDevices = requirementMapper.selectScRequirementList(offlineQuery);
            station.setOfflineDeviceCount(offlineDevices != null ? offlineDevices.size() : 0);
        }

        return stationList;
    }

}
