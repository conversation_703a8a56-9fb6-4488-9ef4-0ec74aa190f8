package com.tunnel.service;

import com.tunnel.domain.MonitorAlarmFieldConfig;

import java.util.List;

/**
 * 监测预警指标配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
public interface MonitorAlarmFieldConfigService
{
    /**
     * 查询监测预警指标配置
     * 
     * @param id 监测预警指标配置主键
     * @return 监测预警指标配置
     */
    public MonitorAlarmFieldConfig selectMonitorAlarmFieldConfigById(Long id);

    /**
     * 查询监测预警指标配置列表
     * 
     * @param monitorAlarmFieldConfig 监测预警指标配置
     * @return 监测预警指标配置集合
     */
    public List<MonitorAlarmFieldConfig> selectMonitorAlarmFieldConfigList(MonitorAlarmFieldConfig monitorAlarmFieldConfig);

    /**
     * 新增监测预警指标配置
     * 
     * @param monitorAlarmFieldConfig 监测预警指标配置
     * @return 结果
     */
    public int insertMonitorAlarmFieldConfig(MonitorAlarmFieldConfig monitorAlarmFieldConfig);

    /**
     * 修改监测预警指标配置
     * 
     * @param monitorAlarmFieldConfig 监测预警指标配置
     * @return 结果
     */
    public int updateMonitorAlarmFieldConfig(MonitorAlarmFieldConfig monitorAlarmFieldConfig);

    /**
     * 批量删除监测预警指标配置
     * 
     * @param ids 需要删除的监测预警指标配置主键集合
     * @return 结果
     */
    public int deleteMonitorAlarmFieldConfigByIds(Long[] ids);

    /**
     * 删除监测预警指标配置信息
     * 
     * @param id 监测预警指标配置主键
     * @return 结果
     */
    public int deleteMonitorAlarmFieldConfigById(Long id);
}
