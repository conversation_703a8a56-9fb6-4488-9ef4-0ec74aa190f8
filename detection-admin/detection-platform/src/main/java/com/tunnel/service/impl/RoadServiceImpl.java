package com.tunnel.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.tunnel.common.annotation.DataScope;
import com.tunnel.common.annotation.DataSource;
import com.tunnel.common.enums.DataSourceType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.Road;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.service.RoadService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 基础项目路线信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class RoadServiceImpl implements RoadService {
    private static final Logger log = LoggerFactory.getLogger(RoadServiceImpl.class);

    @Autowired
    private RoadMapper roadMapper;

    /**
     * 查询基础项目路线信息
     *
     * @param id 基础项目路线信息主键
     * @return 基础项目路线信息
     */
    @Override
    public Road selectRoadById(Long id) {
        return roadMapper.selectRoadById(id);
    }

    /**
     * 查询基础项目路线信息列表
     *
     * @param road 基础项目路线信息
     * @return 基础项目路线信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<Road> selectRoadList(Road road) {
        return roadMapper.selectRoadList(road);
    }

    /**
     * 新增基础项目路线信息
     *
     * @param road 基础项目路线信息
     * @return 结果
     */
    @Override
    
    public int insertRoad(Road road) {
        // 校验路线编号唯一性
        if (StringUtils.isNotEmpty(checkRoadCodeUnique(road))) {
            throw new ServiceException("新增路线'" + road.getRoadName() + "'失败，路线编号已存在");
        }
        
        road.setCreateTime(DateUtils.getNowDate());
        road.setCreator(SecurityUtils.getUserId());
        road.setIsAvailable(1);
        road.setIsDeleted(0);
        road.setVersionNo(0);
        return roadMapper.insertRoad(road);
    }

    /**
     * 修改基础项目路线信息
     *
     * @param road 基础项目路线信息
     * @return 结果
     */
    @Override
    
    public int updateRoad(Road road) {
        // 校验路线编号唯一性
        if (StringUtils.isNotEmpty(checkRoadCodeUnique(road))) {
            throw new ServiceException("修改路线'" + road.getRoadName() + "'失败，路线编号已存在");
        }
        
        road.setModifier(SecurityUtils.getUserId());
        return roadMapper.updateRoad(road);
    }

    /**
     * 批量删除基础项目路线信息
     *
     * @param ids 需要删除的基础项目路线信息主键
     * @return 结果
     */
    @Override
    
    public int deleteRoadByIds(Long[] ids) {
        return roadMapper.deleteRoadByIds(ids);
    }

    /**
     * 删除基础项目路线信息信息
     *
     * @param id 基础项目路线信息主键
     * @return 结果
     */
    @Override
    
    public int deleteRoadById(Long id) {
        return roadMapper.deleteRoadById(id);
    }

    /**
     * 根据路线编号查询路线信息
     *
     * @param roadCode 路线编号
     * @return 路线信息
     */
    @Override
    public Road selectRoadByCode(String roadCode) {
        return roadMapper.selectRoadByCode(roadCode);
    }

    /**
     * 根据用户ID查询路线信息列表
     *
     * @param userId 用户ID
     * @return 路线信息集合
     */
    @Override
    public List<Road> selectRoadListByUserId(Long userId) {
        return roadMapper.selectRoadListByUserId(userId);
    }

    /**
     * 根据部门ID查询路线信息列表
     *
     * @param deptId 部门ID
     * @return 路线信息集合
     */
    @Override
    public List<Road> selectRoadListByDeptId(Long deptId) {
        return roadMapper.selectRoadListByDeptId(deptId);
    }

    /**
     * 根据公司名称和路线编码查询路线信息
     *
     * @param companyName 公司名称
     * @param roadCode 路线编码
     * @return 路线信息
     */
    @Override
    public Road selectRoadByCompanyAndCode(String companyName, String roadCode) {
        return roadMapper.selectRoadByCompanyAndCode(companyName, roadCode);
    }

    /**
     * 校验路线编号是否唯一
     *
     * @param road 路线信息
     * @return 结果
     */
    @Override
    public String checkRoadCodeUnique(Road road) {
        Long roadId = StringUtils.isNull(road.getId()) ? -1L : road.getId();
        Road info = roadMapper.checkRoadCodeUnique(road);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != roadId.longValue()) {
            return "路线编号已存在";
        }
        return "";
    }

    /**
     * 导出基础项目路线信息列表
     *
     * @param response HTTP响应
     * @param road 查询条件
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public void exportRoad(HttpServletResponse response, Road road) {
        List<Road> list = roadMapper.selectRoadList(road);
        ExcelUtil<Road> util = new ExcelUtil<Road>(Road.class);
        util.exportExcel(response, list, "基础项目路线信息数据");
    }


    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    @Override
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<Road> util = new ExcelUtil<Road>(Road.class);
        util.importTemplateExcel(response, "路线数据");
    }


    /**
     * 获取路线信息记录数
     *
     * @param road 查询条件
     * @return 记录数
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public int countRoad(Road road) {
        return roadMapper.countRoad(road);
    }

    /**
     * 获取所有公司列表
     *
     * @return 集合
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<String> selectAllCompany(Road road) {
        return roadMapper.selectAllCompany(road);
    }

    @Override
    public List<Road> selectRoadListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<Road> list = new ArrayList<>();
        for (List<Long> idList : ListUtil.split(ids, 500)) {
            List<Road> roadList = roadMapper.selectRoadListByIds(idList);
            if (CollectionUtils.isNotEmpty(roadList)) {
                list.addAll(roadList);
            }
        }
        return list;
    }
}