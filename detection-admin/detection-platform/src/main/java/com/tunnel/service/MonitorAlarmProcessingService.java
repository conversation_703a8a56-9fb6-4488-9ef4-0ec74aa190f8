package com.tunnel.service;

import com.tunnel.domain.MonitorAlarmProcessing;

import java.util.List;

/**
 * 监测指标报警Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorAlarmProcessingService
{
    /**
     * 查询监测指标报警
     * 
     * @param id 监测指标报警主键
     * @return 监测指标报警
     */
    public MonitorAlarmProcessing selectMonitorAlarmProcessingById(Long id);

    /**
     * 查询监测指标报警列表
     * 
     * @param monitorAlarmProcessingDto 监测指标报警
     * @return 监测指标报警集合
     */
    public List<MonitorAlarmProcessing> selectMonitorAlarmProcessingList(MonitorAlarmProcessing monitorAlarmProcessingDto);

    /**
     * 新增监测指标报警
     * 
     * @param monitorAlarmProcessing 监测指标报警
     * @return 结果
     */
    public int insertMonitorAlarmProcessing(MonitorAlarmProcessing monitorAlarmProcessing);

    /**
     * 修改监测指标报警
     * 
     * @param monitorAlarmProcessing 监测指标报警
     * @return 结果
     */
    public int updateMonitorAlarmProcessing(MonitorAlarmProcessing monitorAlarmProcessing);

    /**
     * 批量删除监测指标报警
     * 
     * @param ids 需要删除的监测指标报警主键集合
     * @return 结果
     */
    public int deleteMonitorAlarmProcessingByIds(Long[] ids);

    /**
     * 删除监测指标报警信息
     * 
     * @param id 监测指标报警主键
     * @return 结果
     */
    public int deleteMonitorAlarmProcessingById(Long id);
}
