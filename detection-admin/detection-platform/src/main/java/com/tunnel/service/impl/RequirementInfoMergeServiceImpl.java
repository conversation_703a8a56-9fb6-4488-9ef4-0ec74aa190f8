package com.tunnel.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.domain.RequirementInfoMerge;
import com.tunnel.domain.RequirementReport;
import com.tunnel.domain.RequirementStatus;
import com.tunnel.domain.RequirementWarning;
import com.tunnel.mapper.RequirementInfoMergeMapper;
import com.tunnel.service.RequirementInfoMergeService;
import com.tunnel.service.RequirementReportService;
import com.tunnel.service.RequirementStatusService;
import com.tunnel.service.RequirementWarningService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/2  14:57
 * @since 1.0.0
 */
@Service
public class RequirementInfoMergeServiceImpl implements RequirementInfoMergeService {

    @Resource
    private RequirementInfoMergeMapper requirementInfoMergeMapper;

    @Resource
    private RequirementStatusService requirementStatusService;
    @Resource
    private RequirementWarningService requirementWarningService;
    @Resource
    private RequirementReportService requirementReportService;

    @Override
    public List<RequirementInfoMerge> selectRequirementReportList(RequirementInfoMerge requirementInfoMerge) {
        return requirementInfoMergeMapper.selectRequirementList(requirementInfoMerge);
    }

    @Override
    public List<Map<String, Object>> selectRequirementReportMap(Date startTime, Date endTime, String field) {
        return requirementInfoMergeMapper.selectRequirementMap(startTime, endTime, field);
    }

    @Override
    public RequirementInfoMerge selectRequirementReportByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("key is empty");
        }
        RequirementInfoMerge query = new RequirementInfoMerge();
        query.setKey(key);
        List<RequirementInfoMerge> list = requirementInfoMergeMapper.selectRequirementList(query);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertRequirement(RequirementInfoMerge requirementInfoMerge) {
        if (StringUtils.isEmpty(requirementInfoMerge.getKey())) {
            throw new RuntimeException("key is empty");
        }
        if (StringUtils.isEmpty(requirementInfoMerge.getTopic())) {
            throw new RuntimeException("topic is empty");
        }
        String jsonStr = JSON.toJSONString(requirementInfoMerge);
        Date now = new Date();
        // todo: 未处理重复数据
        RequirementStatus requirementStatus = JSON.parseObject(jsonStr, RequirementStatus.class);
        requirementStatus.setCreateTime(now);
        requirementStatus.setUpdateTime(now);
        requirementStatusService.insertRequirementStatus(requirementStatus);
        RequirementWarning requirementWarning = JSON.parseObject(jsonStr, RequirementWarning.class);
        requirementWarning.setCreateTime(now);
        requirementWarning.setUpdateTime(now);
        requirementWarningService.insertRequirementWarning(requirementWarning);
        RequirementReport requirementReport = JSON.parseObject(jsonStr, RequirementReport.class);
        requirementReport.setCreateTime(now);
        requirementReport.setUpdateTime(now);
        requirementReportService.insertRequirementReport(requirementReport);
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateRequirement(RequirementInfoMerge requirementInfoMerge) {
        if (StringUtils.isEmpty(requirementInfoMerge.getKey())) {
            throw new RuntimeException("key is empty");
        }

        if (StringUtils.isEmpty(requirementInfoMerge.getTopic())) {
            throw new RuntimeException("topic is empty");
        }
        String key = requirementInfoMerge.getKey();
        String jsonStr = JSON.toJSONString(requirementInfoMerge);
        Date now = new Date();
        List<RequirementStatus> requirementStatusList = requirementStatusService.listByKey(key);
        if (CollectionUtils.isNotEmpty(requirementStatusList)) {
            requirementStatusList.forEach(e -> {
                RequirementStatus requirementStatus = JSON.parseObject(jsonStr, RequirementStatus.class);
                requirementStatus.setId(e.getId());
                requirementStatus.setUpdateTime(now);
                requirementStatusService.updateRequirementStatus(requirementStatus);
            });
        }

        List<RequirementWarning> requirementWarningList = requirementWarningService.listByKey(key);
        if (CollectionUtils.isNotEmpty(requirementWarningList)) {
            requirementWarningList.forEach(e -> {
                RequirementWarning requirementWarning = JSON.parseObject(jsonStr, RequirementWarning.class);
                requirementWarning.setId(e.getId());
                requirementWarning.setUpdateTime(now);
                requirementWarningService.updateRequirementWarning(requirementWarning);
            });
        }
        List<RequirementReport> requirementReportList = requirementReportService.listByKey(key);
        if (CollectionUtils.isNotEmpty(requirementReportList)) {
            requirementReportList.forEach(e -> {
                RequirementReport requirementReport = JSON.parseObject(jsonStr, RequirementReport.class);
                requirementReport.setId(e.getId());
                requirementReport.setUpdateTime(now);
                requirementReportService.updateRequirementReport(requirementReport);
            });
        }
        return 1;
    }

    @Override
    public int deleteRequirementList(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return 0;
        }

        keys.forEach(this::deleteRequirement);
        return keys.size();
    }

    @Override
    public int deleteRequirementList(String keys) {
        if (StringUtils.isEmpty(keys)) {
            return 0;
        }
        return deleteRequirementList(Arrays.asList(keys.split(",")));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRequirement(String key) {
        if (StringUtils.isEmpty(key)) {
            return;
        }

        List<RequirementStatus> requirementStatusList = requirementStatusService.listByKey(key);
        if (CollectionUtils.isNotEmpty(requirementStatusList)) {
            List<String> ids = requirementStatusList.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
            requirementStatusService.deleteRequirementStatusByIds(String.join(",", ids));
        }
        List<RequirementWarning> requirementWarningList = requirementWarningService.listByKey(key);
        if (CollectionUtils.isNotEmpty(requirementWarningList)) {
            List<String> ids = requirementWarningList.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
            requirementWarningService.deleteRequirementWarningByIds(String.join(",", ids));
        }

        List<RequirementReport> requirementReportList = requirementReportService.listByKey(key);
        if (CollectionUtils.isNotEmpty(requirementReportList)) {
            List<String> ids = requirementReportList.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
            requirementReportService.deleteRequirementReportByIds(String.join(",", ids));
        }
    }

    @Override
    public List<JSONObject> getPropertyMapping() {
        List<JSONObject> propertyMappingList = new ArrayList<>();
        propertyMappingList.add(new JSONObject().fluentPut("receive", "key").fluentPut("property", "key").fluentPut("name", "业务唯一键"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "topic").fluentPut("property", "topic").fluentPut("name", "路由"));
        // sc_requirement_report
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I20.0").fluentPut("property", "i200").fluentPut("name", "LS301_H(二级产水池高)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I20.1").fluentPut("property", "i201").fluentPut("name", "LS302_L(二级产水池中)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I20.2").fluentPut("property", "i202").fluentPut("name", "LS303_LL(二级产水池低)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I20.3").fluentPut("property", "i203").fluentPut("name", "LS304(三级产水池低)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I20.4").fluentPut("property", "i204").fluentPut("name", "LS001(原水池高)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I20.5").fluentPut("property", "i205").fluentPut("name", "LS002_L(原水池中)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I20.6").fluentPut("property", "i206").fluentPut("name", "LS003_LL(原水池低)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I21.0").fluentPut("property", "i210").fluentPut("name", "PSA_601(空压机无气压)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I21.1").fluentPut("property", "i211").fluentPut("name", "LS101_H(细格栅高位)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I21.2").fluentPut("property", "i212").fluentPut("name", "LS501_LL(碳源低液位)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I21.3").fluentPut("property", "i213").fluentPut("name", "LS502_LL(次钠低液位)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "I21.4").fluentPut("property", "i214").fluentPut("name", "LS503_LL(消毒剂低液位)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD200").fluentPut("property", "vd200").fluentPut("name", "FIT101(进水流量计)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD230").fluentPut("property", "vd230").fluentPut("name", "FIT201(回流/污泥流量计)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD260").fluentPut("property", "vd260").fluentPut("name", "DPT301(压差计)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD2068").fluentPut("property", "vd2068").fluentPut("name", "TT201(温度计)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD404").fluentPut("property", "vd404").fluentPut("name", "FT101_Total(FIT101总流量)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD408").fluentPut("property", "vd408").fluentPut("name", "FT201_Total(FIT201总流量)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD704").fluentPut("property", "vd704").fluentPut("name", "FIT101PD(FIT101每天数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD712").fluentPut("property", "vd712").fluentPut("name", "FIT101PM(FIT101每月数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD716").fluentPut("property", "vd716").fluentPut("name", "FIT101PY(FIT101每年数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD720").fluentPut("property", "vd720").fluentPut("name", "FIT201RAS_TOTAL(FIT201回流总流量)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD728").fluentPut("property", "vd728").fluentPut("name", "FIT201RASPD(FIT201回流每天数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD736").fluentPut("property", "vd736").fluentPut("name", "FIT201RASPM(FIT201回流每月数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD740").fluentPut("property", "vd740").fluentPut("name", "FIT201RASPY(FIT201回流每年数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD744").fluentPut("property", "vd744").fluentPut("name", "FIT201WAS_TOTAL(FIT201排泥总流量)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD752").fluentPut("property", "vd752").fluentPut("name", "FIT201WASPD(FIT201排泥每天数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD760").fluentPut("property", "vd760").fluentPut("name", "FIT201WASPM(FIT201排泥每月数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD764").fluentPut("property", "vd764").fluentPut("name", "FIT201WASPY(FIT201排泥每年数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD904").fluentPut("property", "vd904").fluentPut("name", "FIT101PD前一(FIT101前一周期每天数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD912").fluentPut("property", "vd912").fluentPut("name", "FIT101PM前一(FIT101前一周期每月数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD916").fluentPut("property", "vd916").fluentPut("name", "FIT101PY前一(FIT101前一周期每年数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD928").fluentPut("property", "vd928").fluentPut("name", "FIT201RASPD前一(FIT201前一周期每天回流数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD936").fluentPut("property", "vd936").fluentPut("name", "FIT201RASPM前一(FIT201前一周期每月回流数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD940").fluentPut("property", "vd940").fluentPut("name", "FIT201RASPY前一(FIT201前一周期每年回流数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD952").fluentPut("property", "vd952").fluentPut("name", "FIT201WASPD前一(FIT201前一周期每天排泥数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD960").fluentPut("property", "vd960").fluentPut("name", "FIT201WASPM前一(FIT201前一周期每月排泥回流数据)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VD964").fluentPut("property", "vd964").fluentPut("name", "FIT201WASPY前一(FIT201前一周期每年排泥回流数据)"));
        // sc_requirement_status
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB100").fluentPut("property", "vb100").fluentPut("name", "F01(细格栅状态)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB101").fluentPut("property", "vb101").fluentPut("name", "P02(砂滤泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB102").fluentPut("property", "vb102").fluentPut("name", "P03(反洗泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB103").fluentPut("property", "vb103").fluentPut("name", "B01(工艺风机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB104").fluentPut("property", "vb104").fluentPut("name", "B03(混合风机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB105").fluentPut("property", "vb105").fluentPut("name", "SC01(刮泥机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB106").fluentPut("property", "vb106").fluentPut("name", "P01(污泥泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB107").fluentPut("property", "vb107").fluentPut("name", "DP01(碳源泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB108").fluentPut("property", "vb108").fluentPut("name", "DP02(混凝剂泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB109").fluentPut("property", "vb109").fluentPut("name", "DP03(次钠泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB110").fluentPut("property", "vb110").fluentPut("name", "C_01(空压机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB111").fluentPut("property", "vb111").fluentPut("name", "P00A(进水泵A)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB112").fluentPut("property", "vb112").fluentPut("name", "P00B(进水泵B)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB113").fluentPut("property", "vb113").fluentPut("name", "B02(曝气风机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB114").fluentPut("property", "vb114").fluentPut("name", "F00(粗格栅)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB115").fluentPut("property", "vb115").fluentPut("name", "XV201A(1#膜池混合阀A)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB116").fluentPut("property", "vb116").fluentPut("name", "XV201B(1#膜池混合阀B)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB117").fluentPut("property", "vb117").fluentPut("name", "XV201C(1#膜池混合阀C)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB118").fluentPut("property", "vb118").fluentPut("name", "XV201D(1#膜池混合阀D)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB119").fluentPut("property", "vb119").fluentPut("name", "XV202A(2#膜池混合阀A)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB120").fluentPut("property", "vb120").fluentPut("name", "XV202B(2#膜池混合阀B)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB121").fluentPut("property", "vb121").fluentPut("name", "XV202C(2#膜池混合阀C)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB122").fluentPut("property", "vb122").fluentPut("name", "XV202D(2#膜池混合阀D)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB123").fluentPut("property", "vb123").fluentPut("name", "XV203(回流阀)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB124").fluentPut("property", "vb124").fluentPut("name", "XV204(排泥阀)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB125").fluentPut("property", "vb125").fluentPut("name", "XV301(砂滤进水阀)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB126").fluentPut("property", "vb126").fluentPut("name", "XV302(砂滤进水阀)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB127").fluentPut("property", "vb127").fluentPut("name", "XV303(砂滤反洗阀)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB128").fluentPut("property", "vb128").fluentPut("name", "XV304(砂滤反洗阀)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB129").fluentPut("property", "vb129").fluentPut("name", "XV101(细格栅冲洗阀)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB130").fluentPut("property", "vb130").fluentPut("name", "XV401(储泥池气动阀)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "VB131").fluentPut("property", "vb131").fluentPut("name", "备用1"));
        // sc_requirement_warning
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V91.0").fluentPut("property", "v910").fluentPut("name", "调节池液位低"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V91.1").fluentPut("property", "v911").fluentPut("name", "F-01 溢流(细格栅高液位)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V91.2").fluentPut("property", "v912").fluentPut("name", "FT201 流量低(污泥流量计)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V91.3").fluentPut("property", "v913").fluentPut("name", "T-02(二级产水池)溢流(二级产水池)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V91.4").fluentPut("property", "v914").fluentPut("name", "T-03液位过低(三级产水池)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V91.5").fluentPut("property", "v915").fluentPut("name", "FIT101流量过低"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V92.0").fluentPut("property", "v920").fluentPut("name", "F01故障(细格栅)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V92.1").fluentPut("property", "v921").fluentPut("name", "P02故障(砂滤进水泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V92.2").fluentPut("property", "v922").fluentPut("name", "P03故障(砂滤反洗泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V92.3").fluentPut("property", "v923").fluentPut("name", "B01故障(工艺风机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V92.4").fluentPut("property", "v924").fluentPut("name", "B03故障(曝气风机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V92.5").fluentPut("property", "v925").fluentPut("name", "SC01故障(刮泥机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V92.6").fluentPut("property", "v926").fluentPut("name", "P01故障(污泥泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V92.7").fluentPut("property", "v927").fluentPut("name", "DP01故障(碳源泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V93.0").fluentPut("property", "v930").fluentPut("name", "DP02故障(混凝剂泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V93.1").fluentPut("property", "v931").fluentPut("name", "DP03故障(次钠泵)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V93.2").fluentPut("property", "v932").fluentPut("name", "C01故障(空压机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V93.3").fluentPut("property", "v933").fluentPut("name", "P00A故障(进水泵A)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V93.4").fluentPut("property", "v934").fluentPut("name", "P00B故障(进水泵B)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V93.5").fluentPut("property", "v935").fluentPut("name", "B02故障(混合风机)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V93.6").fluentPut("property", "v936").fluentPut("name", "F00故障(粗格栅)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V93.7").fluentPut("property", "v937").fluentPut("name", "急停故障"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V95.0").fluentPut("property", "v950").fluentPut("name", "LS 401液位报警(储泥池液位报警)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V95.1").fluentPut("property", "v951").fluentPut("name", "液位开关错误(LS301,302位置错误)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V95.2").fluentPut("property", "v952").fluentPut("name", "液位开关错误(LS301,303位置错误)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V95.3").fluentPut("property", "v953").fluentPut("name", "液位开关错误(LS302,303位置错误)"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V91.6").fluentPut("property", "v916").fluentPut("name", "反洗提示"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V91.7").fluentPut("property", "v917").fluentPut("name", "无气压"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V95.4").fluentPut("property", "v954").fluentPut("name", "碳源药罐液位低"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V95.5").fluentPut("property", "v955").fluentPut("name", "混凝剂药罐液位低"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "V95.6").fluentPut("property", "v956").fluentPut("name", "消毒药罐液位低"));

        propertyMappingList.add(new JSONObject().fluentPut("receive", "remark").fluentPut("property", "remark").fluentPut("name", "备注"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "createTime").fluentPut("property", "createTime").fluentPut("name", "创建时间"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "updateTime").fluentPut("property", "updateTime").fluentPut("name", "更新时间"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "creator").fluentPut("property", "creator").fluentPut("name", "创建人"));
        propertyMappingList.add(new JSONObject().fluentPut("receive", "modifier").fluentPut("property", "modifier").fluentPut("name", "更新人"));
        return propertyMappingList;
    }
}
