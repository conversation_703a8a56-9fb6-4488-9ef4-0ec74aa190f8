package com.tunnel.service;

import com.tunnel.domain.ServiceBaseInfo;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 污水处理设施Service接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface WastewaterFacilityService {
    
    /**
     * 查询服务区/收费站基本信息
     *
     * @param id 服务区/收费站基本信息主键
     * @return 服务区/收费站基本信息
     */
    public ServiceBaseInfo selectServiceBaseInfoById(Long id);

    /**
     * 查询服务区/收费站基本信息列表
     *
     * @param baseInfo 服务区/收费站基本信息
     * @return 服务区/收费站基本信息集合
     */
    public List<ServiceBaseInfo> selectServiceBaseInfoList(ServiceBaseInfo baseInfo);

    /**
     * 新增服务区/收费站基本信息
     *
     * @param baseInfo 服务区/收费站基本信息
     * @return 结果
     */
    public int insertServiceBaseInfo(ServiceBaseInfo baseInfo);

    /**
     * 修改服务区/收费站基本信息
     *
     * @param baseInfo 服务区/收费站基本信息
     * @return 结果
     */
    public int updateServiceBaseInfo(ServiceBaseInfo baseInfo);

    /**
     * 批量删除服务区/收费站基本信息
     *
     * @param ids 需要删除的服务区/收费站基本信息主键集合
     * @return 结果
     */
    public int deleteServiceBaseInfoByIds(Long[] ids);

    /**
     * 根据设施名称获取所有相关数据
     *
     * @param facilityName 设施名称
     * @return 包含所有相关表数据的Map
     */
    public Map<String, Object> selectAllDataByFacilityName(String facilityName);

    /**
     * 导出服务区/收费站基本信息列表
     *
     * @param response HTTP响应
     * @param baseInfo 查询条件
     */
    public void exportServiceBaseInfo(HttpServletResponse response, ServiceBaseInfo baseInfo);

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    public void importTemplate(HttpServletResponse response);

    /**
     * 导入污水处理设施数据
     *
     * @param inputStream Excel文件输入流
     * @param facilityName 设施名称
     * @return 导入结果信息
     */
    public String importWastewaterFacilityData(InputStream inputStream, String facilityName);

    /**
     * 获取服务区/收费站基本信息记录数
     *
     * @param baseInfo 查询条件
     * @return 记录数
     */
    public int countServiceBaseInfo(ServiceBaseInfo baseInfo);

    /**
     * 批量更新档案数据
     *
     * @param archiveData 档案数据
     * @return 更新结果
     */
    public String updateArchiveData(Map<String, Object> archiveData);

    /**
     * 批量更新管网信息
     *
     * @param data 管网数据
     * @return 更新结果
     */
    public String updatePipelineNetworkData(Map<String, Object> data);

    /**
     * 批量更新污水处理设施信息
     *
     * @param data 污水处理设施数据
     * @return 更新结果
     */
    public String updateWastewaterStationData(Map<String, Object> data);

    /**
     * 批量更新设备信息
     *
     * @param data 设备数据
     * @return 更新结果
     */
    public String updateEquipmentData(Map<String, Object> data);
}
