package com.tunnel.service;

import com.tunnel.domain.RequirementStatus;

import java.util.List;

/**
 * 设备状态记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface RequirementStatusService
{
    /**
     * 查询设备状态记录
     * 
     * @param id 设备状态记录主键
     * @return 设备状态记录
     */
    public RequirementStatus selectRequirementStatusById(String id);

    /**
     * 查询设备状态记录列表
     * 
     * @param requirementStatus 设备状态记录
     * @return 设备状态记录集合
     */
    public List<RequirementStatus> selectRequirementStatusList(RequirementStatus requirementStatus);

    /**
     * 新增设备状态记录
     * 
     * @param requirementStatus 设备状态记录
     * @return 结果
     */
    public int insertRequirementStatus(RequirementStatus requirementStatus);

    /**
     * 修改设备状态记录
     * 
     * @param requirementStatus 设备状态记录
     * @return 结果
     */
    public int updateRequirementStatus(RequirementStatus requirementStatus);

    /**
     * 批量删除设备状态记录
     * 
     * @param ids 需要删除的设备状态记录主键集合
     * @return 结果
     */
    public int deleteRequirementStatusByIds(String ids);

    /**
     * 删除设备状态记录信息
     * 
     * @param id 设备状态记录主键
     * @return 结果
     */
    public int deleteRequirementStatusById(String id);

    public List<RequirementStatus> listByKey(String key);
}
