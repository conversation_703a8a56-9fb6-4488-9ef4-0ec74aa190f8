package com.tunnel.service;

import com.tunnel.domain.MonitorStationUserRelation;

import java.util.List;

/**
 * 监测站点和用户关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorStationUserRelationService
{
    /**
     * 查询监测站点和用户关联
     * 
     * @param id 监测站点和用户关联主键
     * @return 监测站点和用户关联
     */
    public MonitorStationUserRelation selectMonitorStationUserRelById(Long id);

    /**
     * 查询监测站点和用户关联列表
     * 
     * @param monitorStationUserRelation 监测站点和用户关联
     * @return 监测站点和用户关联集合
     */
    public List<MonitorStationUserRelation> selectMonitorStationUserRelList(MonitorStationUserRelation monitorStationUserRelation);

    /**
     * 新增监测站点和用户关联
     * 
     * @param monitorStationUserRelation 监测站点和用户关联
     * @return 结果
     */
    public int insertMonitorStationUserRel(MonitorStationUserRelation monitorStationUserRelation);

    /**
     * 修改监测站点和用户关联
     * 
     * @param monitorStationUserRelation 监测站点和用户关联
     * @return 结果
     */
    public int updateMonitorStationUserRel(MonitorStationUserRelation monitorStationUserRelation);

    /**
     * 批量删除监测站点和用户关联
     * 
     * @param ids 需要删除的监测站点和用户关联主键集合
     * @return 结果
     */
    public int deleteMonitorStationUserRelByIds(Long[] ids);

    /**
     * 删除监测站点和用户关联信息
     * 
     * @param id 监测站点和用户关联主键
     * @return 结果
     */
    public int deleteMonitorStationUserRelById(Long id);
}
