package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.SecomeaField;
import com.tunnel.mapper.SecomeaFieldMapper;
import com.tunnel.service.SecomeaFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 控制点位Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class SecomeaFieldServiceImpl implements SecomeaFieldService
{
    @Autowired
    private SecomeaFieldMapper secomeaFieldMapper;

    /**
     * 查询控制点位
     * 
     * @param id 控制点位主键
     * @return 控制点位
     */
    @Override
    public SecomeaField selectSecomeaFieldById(Long id)
    {
        return secomeaFieldMapper.selectSecomeaFieldById(id);
    }

    /**
     * 查询控制点位列表
     * 
     * @param secomeaField 控制点位
     * @return 控制点位
     */
    @Override
    public List<SecomeaField> selectSecomeaFieldList(SecomeaField secomeaField)
    {
        return secomeaFieldMapper.selectSecomeaFieldList(secomeaField);
    }

    /**
     * 新增控制点位
     * 
     * @param secomeaField 控制点位
     * @return 结果
     */
    @Override
    public int insertSecomeaField(SecomeaField secomeaField)
    {
        secomeaField.setCreateTime(DateUtils.getNowDate());
        return secomeaFieldMapper.insertSecomeaField(secomeaField);
    }

    /**
     * 修改控制点位
     * 
     * @param secomeaField 控制点位
     * @return 结果
     */
    @Override
    public int updateSecomeaField(SecomeaField secomeaField)
    {
        secomeaField.setUpdateTime(DateUtils.getNowDate());
        return secomeaFieldMapper.updateSecomeaField(secomeaField);
    }

    /**
     * 批量删除控制点位
     * 
     * @param ids 需要删除的控制点位主键
     * @return 结果
     */
    @Override
    public int deleteSecomeaFieldByIds(Long[] ids)
    {
        return secomeaFieldMapper.deleteSecomeaFieldByIds(ids);
    }

    /**
     * 删除控制点位信息
     * 
     * @param id 控制点位主键
     * @return 结果
     */
    @Override
    public int deleteSecomeaFieldById(Long id)
    {
        return secomeaFieldMapper.deleteSecomeaFieldById(id);
    }
}
