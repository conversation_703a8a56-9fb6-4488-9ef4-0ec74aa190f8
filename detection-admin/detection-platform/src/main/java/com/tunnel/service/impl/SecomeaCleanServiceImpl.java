package com.tunnel.service.impl;

import cn.hutool.core.date.DateUtil;
import com.tunnel.domain.RequirementReport;
import com.tunnel.domain.RequirementStatus;
import com.tunnel.domain.RequirementWarning;
import com.tunnel.domain.SecomeaRawRow;
import com.tunnel.mapper.RequirementReportMapper;
import com.tunnel.mapper.RequirementStatusMapper;
import com.tunnel.mapper.RequirementWarningMapper;
import com.tunnel.mapper.SecomeaDynamicRawMapper;
import com.tunnel.service.SecomeaCleanService;
import com.tunnel.service.SecomeaRawAccessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SecomeaCleanServiceImpl implements SecomeaCleanService {

    private static final int BATCH_SIZE = 1000;

    private static final Logger log = LoggerFactory.getLogger(SecomeaCleanServiceImpl.class);

    @Resource
    private SecomeaDynamicRawMapper secomeaDynamicRawMapper;
    @Resource
    private SecomeaRawAccessService rawAccessService;
    @Resource
    private RequirementReportMapper requirementReportMapper;
    @Resource
    private RequirementStatusMapper requirementStatusMapper;
    @Resource
    private RequirementWarningMapper requirementWarningMapper;

    @Override
    public void cleanRange(String fromYyyyMM, String toYyyyMM) {
        YearMonth start = YearMonth.parse(fromYyyyMM, DateTimeFormatter.ofPattern("yyyyMM"));
        YearMonth end = YearMonth.parse(toYyyyMM, DateTimeFormatter.ofPattern("yyyyMM"));
        YearMonth cur = start;
        while (!cur.isAfter(end)) {
            cleanMonth(cur.format(DateTimeFormatter.ofPattern("yyyyMM")));
            cur = cur.plusMonths(1);
        }
    }

    @Override
    public void cleanMonth(String yyyyMM) {
        String table = "secomea_data_" + yyyyMM;
        log.info("[cleanMonth] start, month={}, table={}, batchSize={}", yyyyMM, table, BATCH_SIZE);
        long startTs = System.currentTimeMillis();
        long totalTimeKeys = 0L;
        long totalRows = 0L;
        int batchIndex = 0;
        String lastTime = null;
        while (true) {
            batchIndex++;
            // 1) 先取一批去重后的时间点，保证同一时间点不会被截断
            log.info("[cleanMonth] batch#{} fetch distinct times, lastTime={}, limit={}", batchIndex, lastTime, BATCH_SIZE);
            List<String> times = rawAccessService.selectDistinctTimes(table, lastTime, BATCH_SIZE);
            if (times == null || times.isEmpty()) {
                log.info("[cleanMonth] batch#{} no more times, exiting loop", batchIndex);
                break;
            }
            totalTimeKeys += times.size();
            String firstTime = times.get(0);
            String lastTimeInBatch = times.get(times.size() - 1);
            log.info("[cleanMonth] batch#{} got {} times, range {} .. {}", batchIndex, times.size(), firstTime, lastTimeInBatch);

            // 2) 基于这些时间点，查询完整明细
            long t2 = System.currentTimeMillis();
            List<SecomeaRawRow> rows = rawAccessService.selectByTimes(table, times);
            if (rows == null || rows.isEmpty()) {
                lastTime = times.get(times.size() - 1);
                log.warn("[cleanMonth] batch#{} no rows returned for {} times, advance lastTime to {}", batchIndex, times.size(), lastTime);
                continue;
            }
            totalRows += rows.size();
            log.info("[cleanMonth] batch#{} fetched {} rows for {} times ({} ms)", batchIndex, rows.size(), times.size(), (System.currentTimeMillis()-t2));

            // 3) 按时间分组并转横
            Map<String, List<SecomeaRawRow>> grouped = rows.stream().collect(Collectors.groupingBy(SecomeaRawRow::getDateTime));
            List<RequirementReport> reportList = new ArrayList<>();
            List<RequirementStatus> statusList = new ArrayList<>();
            List<RequirementWarning> warningList = new ArrayList<>();
            log.info("[cleanMonth] batch#{} grouping done, {} groups", batchIndex, grouped.size());

            for (Map.Entry<String, List<SecomeaRawRow>> entry : grouped.entrySet()) {
                String dateTime = entry.getKey();
                List<SecomeaRawRow> list = entry.getValue();

                String gatewayId = list.stream().map(SecomeaRawRow::getGatewayId).filter(Objects::nonNull).findFirst().orElse(null);

                RequirementReport report = new RequirementReport();
                RequirementStatus status = new RequirementStatus();
                RequirementWarning warning = new RequirementWarning();

                report.setTopic(null);
                report.setKey(dateTime);
                status.setTopic(null);
                status.setKey(dateTime);
                warning.setTopic(null);
                warning.setKey(dateTime);
                Date date = DateUtil.parseDateTime(dateTime);
                report.setCreateTime(date);
                status.setCreateTime(date);
                warning.setCreateTime(date);

                trySetGateway(report, status, warning, gatewayId);

                for (SecomeaRawRow r : list) {
                    String col = normalizePoint(r.getPointAddr());
                    String val = r.getValue() == null ? null : r.getValue().toPlainString();
                    mapToTargets(col, val, report, status, warning);
                }

                reportList.add(report);
                statusList.add(status);
                warningList.add(warning);
            }

            if (!reportList.isEmpty()) requirementReportMapper.insertBatch(reportList);
            if (!statusList.isEmpty()) requirementStatusMapper.insertBatch(statusList);
            if (!warningList.isEmpty()) requirementWarningMapper.insertBatch(warningList);
            log.info("[cleanMonth] batch#{} inserted: report={}, status={}, warning={}", batchIndex, reportList.size(), statusList.size(), warningList.size());

            // 4) 标记已处理：按时间点整体标记，避免跨批遗漏
            rawAccessService.markProcessedByTimes(table, times);
            log.info("[cleanMonth] batch#{} marked processed for {} times ({} .. {})", batchIndex, times.size(), firstTime, lastTimeInBatch);

            // 5) 推进游标
            lastTime = times.get(times.size() - 1);
        }
        log.info("[cleanMonth] done, month={}, table={}, totalTimeKeys={}, totalRows={}, elapsedMs={}", yyyyMM, table, totalTimeKeys, totalRows, (System.currentTimeMillis()-startTs));
    }

    // 写 MASTER（不加事务，逐批调用）
    protected void batchInsertReport(List<RequirementReport> list) {
        if (list == null || list.isEmpty()) return;
        for (RequirementReport r : list) {
            requirementReportMapper.insertRequirementReport(r);
        }
    }

    protected void batchInsertStatus(List<RequirementStatus> list) {
        if (list == null || list.isEmpty()) return;
        for (RequirementStatus r : list) {
            requirementStatusMapper.insertRequirementStatus(r);
        }
    }

    protected void batchInsertWarning(List<RequirementWarning> list) {
        if (list == null || list.isEmpty()) return;
        for (RequirementWarning r : list) {
            requirementWarningMapper.insertRequirementWarning(r);
        }
    }

    private void trySetGateway(RequirementReport report, RequirementStatus status, RequirementWarning warning, String gatewayId) {
        try { report.getClass().getMethod("setGatewayId", String.class).invoke(report, gatewayId); } catch (Exception ignored) {}
        try { status.getClass().getMethod("setGatewayId", String.class).invoke(status, gatewayId); } catch (Exception ignored) {}
        try { warning.getClass().getMethod("setGatewayId", String.class).invoke(warning, gatewayId); } catch (Exception ignored) {}
    }

    private String normalizePoint(String pointAddr) {
        if (pointAddr == null) return null;
        return pointAddr.replace('.', '_').replace('-', '_');
    }

    private void mapToTargets(String columnKey, String value,
                              RequirementReport report,
                              RequirementStatus status,
                              RequirementWarning warning) {
        if (columnKey == null) return;
        // 按前缀分发：i20_*, i21_*, vd*, vb*, v9* 等
        if (columnKey.toLowerCase().startsWith("i") || columnKey.toLowerCase().startsWith("i21_") || columnKey.toLowerCase().startsWith("vd")) {
            // report
            setField(report, columnKey.toLowerCase(), value);
        } else if (columnKey.toLowerCase().startsWith("vb")) {
            // status
            setField(status, columnKey.toLowerCase(), value);
        } else if (columnKey.toLowerCase().startsWith("v9") || columnKey.toLowerCase().startsWith("dcm_alarm")) {
            // warning
            setField(warning, columnKey.toLowerCase(), value);
        }else {
            System.out.println("columnKey="+columnKey+", value="+value);
        }
    }

    private void setField(Object target, String field, String value) {
        try {
            String prop = toCamel(field);
            target.getClass().getMethod("set" + capitalize(prop), String.class).invoke(target, value);
        } catch (Exception ignored) {
        }
    }

    private String toCamel(String s) {
        StringBuilder out = new StringBuilder();
        boolean up = false;
        for (char c : s.toCharArray()) {
            if (c == '_') { up = true; continue; }
            if (up) { out.append(Character.toUpperCase(c)); up = false; }
            else { out.append(c); }
        }
        return out.toString();
    }

    private String capitalize(String s) { return s == null || s.isEmpty() ? s : Character.toUpperCase(s.charAt(0)) + s.substring(1); }
}


