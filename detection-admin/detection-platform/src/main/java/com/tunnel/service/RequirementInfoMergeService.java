package com.tunnel.service;

import com.alibaba.fastjson2.JSONObject;
import com.tunnel.domain.RequirementInfoMerge;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备数据记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface RequirementInfoMergeService
{
    /**
     * 查询设备上报数据记录
     * 
     * @param requirementInfoMerge 设备上报数据记录主键
     * @return 设备上报数据记录
     */
    public List<RequirementInfoMerge> selectRequirementReportList(RequirementInfoMerge requirementInfoMerge);

    /**
     * 查询设备上报数据记录
     *
     * @return 设备上报数据记录
     */
    public List<Map<String, Object>> selectRequirementReportMap(Date startTime, Date endTime, String field);


    /**
     * 查询设备上报数据记录
     *
     * @param key 业务主键
     * @return 设备上报数据记录
     */
    public RequirementInfoMerge selectRequirementReportByKey(String key);


    /**
     * 新增设备上报数据记录
     * 
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    public int insertRequirement(RequirementInfoMerge requirementReport);

    /**
     * 修改设备上报数据记录
     * 
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    public int updateRequirement(RequirementInfoMerge requirementReport);

    /**
     * 批量删除设备上报数据记录
     *
     * @param keys 需要删除的设备上报数据记录主键集合,用逗号拼接
     * @return 结果
     */
    public int deleteRequirementList(String keys);

    /**
     * 批量删除设备上报数据记录
     * 
     * @param keys 需要删除的设备上报数据记录主键集合
     * @return 结果
     */
    public int deleteRequirementList(List<String> keys);

    public void deleteRequirement(String key);

    /**
     * 获取属性映射
     * @return [{"receive": "接收值", "property": "属性值", "name": "描述"}]
     */
    public List<JSONObject> getPropertyMapping();
}
