package com.tunnel.service;

import com.tunnel.domain.MonthlyReport;

import java.util.List;

/**
 * 月报Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonthlyReportService
{
    /**
     * 查询月报
     * 
     * @param id 月报主键
     * @return 月报
     */
    public MonthlyReport selectMonthlyReportById(Long id);

    /**
     * 查询月报列表
     * 
     * @param monthlyReport 月报
     * @return 月报集合
     */
    public List<MonthlyReport> selectMonthlyReportList(MonthlyReport monthlyReport);

    /**
     * 新增月报
     * 
     * @param monthlyReport 月报
     * @return 结果
     */
    public int insertMonthlyReport(MonthlyReport monthlyReport);

    /**
     * 修改月报
     * 
     * @param monthlyReport 月报
     * @return 结果
     */
    public int updateMonthlyReport(MonthlyReport monthlyReport);

    /**
     * 批量删除月报
     * 
     * @param ids 需要删除的月报主键集合
     * @return 结果
     */
    public int deleteMonthlyReportByIds(Long[] ids);

    /**
     * 删除月报信息
     * 
     * @param id 月报主键
     * @return 结果
     */
    public int deleteMonthlyReportById(Long id);
}
