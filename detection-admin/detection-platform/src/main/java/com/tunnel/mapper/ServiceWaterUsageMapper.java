package com.tunnel.mapper;

import com.tunnel.domain.ServiceWaterUsage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用水情况Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceWaterUsageMapper {
    /**
     * 查询用水情况
     *
     * @param id 用水情况主键
     * @return 用水情况
     */
    public ServiceWaterUsage selectServiceWaterUsageById(Long id);

    /**
     * 查询用水情况列表
     *
     * @param serviceWaterUsage 用水情况
     * @return 用水情况集合
     */
    public List<ServiceWaterUsage> selectServiceWaterUsageList(ServiceWaterUsage serviceWaterUsage);

    /**
     * 根据设施ID查询用水情况列表
     *
     * @param serviceAreaId 设施ID
     * @return 用水情况集合
     */
    public List<ServiceWaterUsage> selectServiceWaterUsageByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增用水情况
     *
     * @param serviceWaterUsage 用水情况
     * @return 结果
     */
    public int insertServiceWaterUsage(ServiceWaterUsage serviceWaterUsage);

    /**
     * 修改用水情况
     *
     * @param serviceWaterUsage 用水情况
     * @return 结果
     */
    public int updateServiceWaterUsage(ServiceWaterUsage serviceWaterUsage);

    /**
     * 删除用水情况
     *
     * @param id 用水情况主键
     * @return 结果
     */
    public int deleteServiceWaterUsageById(Long id);

    /**
     * 批量删除用水情况
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceWaterUsageByIds(Long[] ids);

    /**
     * 根据设施ID删除用水情况
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceWaterUsageByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
