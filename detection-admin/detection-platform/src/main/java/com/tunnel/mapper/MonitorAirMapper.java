package com.tunnel.mapper;

import com.tunnel.domain.MonitorAir;
import com.tunnel.domain.MonitorDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 空气监测数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface MonitorAirMapper {
    /**
     * 查询空气监测数据
     * 
     * @param id 空气监测数据主键
     * @return 空气监测数据
     */
    public MonitorAir selectScMonitorAirById(Long id);

    /**
     * 查询空气监测数据列表
     * 
     * @param monitorAir 空气监测数据
     * @return 空气监测数据集合
     */
    public List<MonitorAir> selectScMonitorAirList(MonitorAir monitorAir);

    /**
     * 新增空气监测数据
     * 
     * @param monitorAir 空气监测数据
     * @return 结果
     */
    public int insertScMonitorAir(MonitorAir monitorAir);

    /**
     * 修改空气监测数据
     * 
     * @param monitorAir 空气监测数据
     * @return 结果
     */
    public int updateScMonitorAir(MonitorAir monitorAir);

    /**
     * 删除空气监测数据
     * 
     * @param id 空气监测数据主键
     * @return 结果
     */
    public int deleteScMonitorAirById(Long id);

    /**
     * 批量删除空气监测数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScMonitorAirByIds(Long[] ids);

    /**
     * 批量插入空气监测数据
     * 
     * @param list 数据列表
     * @return 结果
     */
    public int insertBatch(@Param("list") List<MonitorAir> list);

    List<Map<String, Object>> selectByParams(MonitorDTO dto);

    MonitorDTO selectTimeRange(MonitorDTO dto);
}
