package com.tunnel.mapper;

import com.tunnel.domain.ServiceBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务区/收费站基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceBaseInfoMapper {
    /**
     * 查询服务区/收费站基本信息
     *
     * @param id 服务区/收费站基本信息主键
     * @return 服务区/收费站基本信息
     */
    public ServiceBaseInfo selectServiceBaseInfoById(Long id);

    /**
     * 查询服务区/收费站基本信息列表
     *
     * @param serviceBaseInfo 服务区/收费站基本信息
     * @return 服务区/收费站基本信息集合
     */
    public List<ServiceBaseInfo> selectServiceBaseInfoList(ServiceBaseInfo serviceBaseInfo);

    /**
     * 新增服务区/收费站基本信息
     *
     * @param serviceBaseInfo 服务区/收费站基本信息
     * @return 结果
     */
    public int insertServiceBaseInfo(ServiceBaseInfo serviceBaseInfo);

    /**
     * 修改服务区/收费站基本信息
     *
     * @param serviceBaseInfo 服务区/收费站基本信息
     * @return 结果
     */
    public int updateServiceBaseInfo(ServiceBaseInfo serviceBaseInfo);

    /**
     * 删除服务区/收费站基本信息
     *
     * @param id 服务区/收费站基本信息主键
     * @return 结果
     */
    public int deleteServiceBaseInfoById(Long id);

    /**
     * 批量删除服务区/收费站基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceBaseInfoByIds(Long[] ids);

    /**
     * 根据设施名称查询基本信息
     *
     * @param facilityName 设施名称
     * @return 服务区/收费站基本信息
     */
    public ServiceBaseInfo selectServiceBaseInfoByName(@Param("facilityName") String facilityName);

    /**
     * 获取服务区/收费站基本信息记录数
     *
     * @param serviceBaseInfo 查询条件
     * @return 记录数
     */
    public int countServiceBaseInfo(ServiceBaseInfo serviceBaseInfo);
}
