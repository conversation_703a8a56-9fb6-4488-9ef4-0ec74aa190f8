package com.tunnel.mapper;

import com.tunnel.domain.ServiceSecondarySettlingTankInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 二沉池信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceSecondarySettlingTankInfoMapper {
    /**
     * 查询二沉池信息
     *
     * @param id 二沉池信息主键
     * @return 二沉池信息
     */
    public ServiceSecondarySettlingTankInfo selectServiceSecondarySettlingTankInfoById(Long id);

    /**
     * 查询二沉池信息列表
     *
     * @param serviceSecondarySettlingTankInfo 二沉池信息
     * @return 二沉池信息集合
     */
    public List<ServiceSecondarySettlingTankInfo> selectServiceSecondarySettlingTankInfoList(ServiceSecondarySettlingTankInfo serviceSecondarySettlingTankInfo);

    /**
     * 根据设施ID查询二沉池信息列表
     *
     * @param serviceAreaId 设施ID
     * @return 二沉池信息集合
     */
    public List<ServiceSecondarySettlingTankInfo> selectServiceSecondarySettlingTankInfoByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增二沉池信息
     *
     * @param serviceSecondarySettlingTankInfo 二沉池信息
     * @return 结果
     */
    public int insertServiceSecondarySettlingTankInfo(ServiceSecondarySettlingTankInfo serviceSecondarySettlingTankInfo);

    /**
     * 修改二沉池信息
     *
     * @param serviceSecondarySettlingTankInfo 二沉池信息
     * @return 结果
     */
    public int updateServiceSecondarySettlingTankInfo(ServiceSecondarySettlingTankInfo serviceSecondarySettlingTankInfo);

    /**
     * 删除二沉池信息
     *
     * @param id 二沉池信息主键
     * @return 结果
     */
    public int deleteServiceSecondarySettlingTankInfoById(Long id);

    /**
     * 批量删除二沉池信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceSecondarySettlingTankInfoByIds(Long[] ids);

    /**
     * 根据设施ID删除二沉池信息
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceSecondarySettlingTankInfoByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
