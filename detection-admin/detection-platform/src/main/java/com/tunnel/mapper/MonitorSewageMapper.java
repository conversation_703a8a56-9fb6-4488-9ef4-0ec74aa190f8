package com.tunnel.mapper;

import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.MonitorSewage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 水质监测数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface MonitorSewageMapper {
    /**
     * 查询水质监测数据
     * 
     * @param id 水质监测数据主键
     * @return 水质监测数据
     */
    public MonitorSewage selectScMonitorDataById(Long id);

    /**
     * 查询水质监测数据列表
     * 
     * @param monitorSewage 水质监测数据
     * @return 水质监测数据集合
     */
    public List<MonitorSewage> selectScMonitorDataList(MonitorSewage monitorSewage);

    /**
     * 新增水质监测数据
     * 
     * @param monitorSewage 水质监测数据
     * @return 结果
     */
    public int insertScMonitorData(MonitorSewage monitorSewage);

    /**
     * 修改水质监测数据
     * 
     * @param monitorSewage 水质监测数据
     * @return 结果
     */
    public int updateScMonitorData(MonitorSewage monitorSewage);

    /**
     * 删除水质监测数据
     * 
     * @param id 水质监测数据主键
     * @return 结果
     */
    public int deleteScMonitorDataById(Long id);

    /**
     * 批量删除水质监测数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScMonitorDataByIds(Long[] ids);

    /**
     * 批量插入水质监测数据
     * 
     * @param list 数据列表
     * @return 结果
     */
    public int insertBatch(@Param("list") List<MonitorSewage> list);

    List<Map<String, Object>> selectByParams(MonitorDTO dto);

    MonitorDTO selectTimeRange(MonitorDTO dto);
}
