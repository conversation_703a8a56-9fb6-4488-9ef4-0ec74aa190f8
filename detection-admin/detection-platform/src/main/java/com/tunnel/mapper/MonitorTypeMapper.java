package com.tunnel.mapper;

import com.tunnel.domain.MonitorType;

import java.util.List;

/**
 * 监测类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorTypeMapper 
{
    /**
     * 查询监测类型
     * 
     * @param id 监测类型主键
     * @return 监测类型
     */
    public MonitorType selectMonitorTypeById(Long id);

    /**
     * 查询监测类型列表
     * 
     * @param monitorType 监测类型
     * @return 监测类型集合
     */
    public List<MonitorType> selectMonitorTypeList(MonitorType monitorType);

    /**
     * 新增监测类型
     * 
     * @param monitorType 监测类型
     * @return 结果
     */
    public int insertMonitorType(MonitorType monitorType);

    /**
     * 修改监测类型
     * 
     * @param monitorType 监测类型
     * @return 结果
     */
    public int updateMonitorType(MonitorType monitorType);

    /**
     * 删除监测类型
     * 
     * @param id 监测类型主键
     * @return 结果
     */
    public int deleteMonitorTypeById(Long id);

    /**
     * 批量删除监测类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonitorTypeByIds(Long[] ids);
}
