package com.tunnel.mapper;

import com.tunnel.domain.ServiceOtherTreatmentUnits;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 其他处理单元/设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceOtherTreatmentUnitsMapper {
    /**
     * 查询其他处理单元/设备
     *
     * @param id 其他处理单元/设备主键
     * @return 其他处理单元/设备
     */
    public ServiceOtherTreatmentUnits selectServiceOtherTreatmentUnitsById(Long id);

    /**
     * 查询其他处理单元/设备列表
     *
     * @param serviceOtherTreatmentUnits 其他处理单元/设备
     * @return 其他处理单元/设备集合
     */
    public List<ServiceOtherTreatmentUnits> selectServiceOtherTreatmentUnitsList(ServiceOtherTreatmentUnits serviceOtherTreatmentUnits);

    /**
     * 根据设施ID查询其他处理单元/设备列表
     *
     * @param serviceAreaId 设施ID
     * @return 其他处理单元/设备集合
     */
    public List<ServiceOtherTreatmentUnits> selectServiceOtherTreatmentUnitsByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增其他处理单元/设备
     *
     * @param serviceOtherTreatmentUnits 其他处理单元/设备
     * @return 结果
     */
    public int insertServiceOtherTreatmentUnits(ServiceOtherTreatmentUnits serviceOtherTreatmentUnits);

    /**
     * 修改其他处理单元/设备
     *
     * @param serviceOtherTreatmentUnits 其他处理单元/设备
     * @return 结果
     */
    public int updateServiceOtherTreatmentUnits(ServiceOtherTreatmentUnits serviceOtherTreatmentUnits);

    /**
     * 删除其他处理单元/设备
     *
     * @param id 其他处理单元/设备主键
     * @return 结果
     */
    public int deleteServiceOtherTreatmentUnitsById(Long id);

    /**
     * 批量删除其他处理单元/设备
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceOtherTreatmentUnitsByIds(Long[] ids);

    /**
     * 根据设施ID删除其他处理单元/设备
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceOtherTreatmentUnitsByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
