package com.tunnel.mapper;

import com.tunnel.domain.ServiceSystemEvaluation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统整体评价Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceSystemEvaluationMapper {
    /**
     * 查询系统整体评价
     *
     * @param id 系统整体评价主键
     * @return 系统整体评价
     */
    public ServiceSystemEvaluation selectServiceSystemEvaluationById(Long id);

    /**
     * 查询系统整体评价列表
     *
     * @param serviceSystemEvaluation 系统整体评价
     * @return 系统整体评价集合
     */
    public List<ServiceSystemEvaluation> selectServiceSystemEvaluationList(ServiceSystemEvaluation serviceSystemEvaluation);

    /**
     * 根据设施ID查询系统整体评价列表
     *
     * @param serviceAreaId 设施ID
     * @return 系统整体评价集合
     */
    public List<ServiceSystemEvaluation> selectServiceSystemEvaluationByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增系统整体评价
     *
     * @param serviceSystemEvaluation 系统整体评价
     * @return 结果
     */
    public int insertServiceSystemEvaluation(ServiceSystemEvaluation serviceSystemEvaluation);

    /**
     * 修改系统整体评价
     *
     * @param serviceSystemEvaluation 系统整体评价
     * @return 结果
     */
    public int updateServiceSystemEvaluation(ServiceSystemEvaluation serviceSystemEvaluation);

    /**
     * 删除系统整体评价
     *
     * @param id 系统整体评价主键
     * @return 结果
     */
    public int deleteServiceSystemEvaluationById(Long id);

    /**
     * 批量删除系统整体评价
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceSystemEvaluationByIds(Long[] ids);

    /**
     * 根据设施ID删除系统整体评价
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceSystemEvaluationByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
