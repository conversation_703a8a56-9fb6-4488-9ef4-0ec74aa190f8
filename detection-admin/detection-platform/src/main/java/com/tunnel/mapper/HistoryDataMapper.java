package com.tunnel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 历史数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface HistoryDataMapper {
    
    /**
     * 查询未处理的时间点
     * 
     * @param tableName 表名
     * @param lastTime 上次处理的时间
     * @param limitSize 限制数量
     * @return 时间点列表
     */
    List<String> selectDistinctTimes(@Param("tableName") String tableName, 
                                    @Param("lastTime") String lastTime, 
                                    @Param("limitSize") int limitSize);

    /**
     * 根据时间点查询数据
     * 
     * @param tableName 表名
     * @param times 时间点列表
     * @return 数据列表
     */
    List<Map<String, Object>> selectByTimes(@Param("tableName") String tableName, 
                                           @Param("times") List<String> times);

    /**
     * 标记数据为已处理
     * 
     * @param tableName 表名
     * @param times 时间点列表
     * @return 结果
     */
    int markProcessedByTimes(@Param("tableName") String tableName, 
                           @Param("times") List<String> times);
}
