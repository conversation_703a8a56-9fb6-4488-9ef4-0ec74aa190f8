package com.tunnel.mapper;

import com.tunnel.domain.RequirementInfoMerge;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备数据记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface RequirementInfoMergeMapper
{
    /**
     * 查询设备上报数据记录
     * 
     * @param requirementInfoMerge 设备数据查询条件
     * @return 设备上报数据记录
     */
    public List<RequirementInfoMerge> selectRequirementList(RequirementInfoMerge requirementInfoMerge);

    /**
     * 查询设备上报数据记录
     *
     * @return 设备上报数据记录
     */
    public List<Map<String, Object>> selectRequirementMap(@Param("startTime") Date startTime,
                                                          @Param("endTime") Date endTime,
                                                          @Param("field") String field);
}
