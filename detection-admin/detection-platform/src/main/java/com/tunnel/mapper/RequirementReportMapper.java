package com.tunnel.mapper;

import com.tunnel.domain.CheckDTO;
import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.RequirementReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备上报数据记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface RequirementReportMapper 
{
    /**
     * 查询设备上报数据记录
     * 
     * @param id 设备上报数据记录主键
     * @return 设备上报数据记录
     */
    public RequirementReport selectRequirementReportById(String id);

    /**
     * 查询设备上报数据记录列表
     * 
     * @param requirementReport 设备上报数据记录
     * @return 设备上报数据记录集合
     */
    public List<RequirementReport> selectRequirementReportList(RequirementReport requirementReport);

    /**
     * 新增设备上报数据记录
     * 
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    public int insertRequirementReport(RequirementReport requirementReport);

    /**
     * 批量新增设备上报数据记录
     */
    public int insertBatch(@Param("list") List<RequirementReport> list);

    /**
     * 修改设备上报数据记录
     * 
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    public int updateRequirementReport(RequirementReport requirementReport);

    /**
     * 删除设备上报数据记录
     * 
     * @param id 设备上报数据记录主键
     * @return 结果
     */
    public int deleteRequirementReportById(String id);

    /**
     * 批量删除设备上报数据记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRequirementReportByIds(String[] ids);

    /**
     * 根据key查询设备上报数据记录
     *
     * @param key 对应的业务标识key(b_key)
     * @return
     */
    public List<RequirementReport> listByKey(@Param(value = "key") String key);

    /**
     * 获取上报数据表的所有字段信息
     *
     * @return 字段信息列表，包含字段名和注释
     */
    public List<Map<String, Object>> getAllFields();

    CheckDTO selectTimeRange(CheckDTO dto);

    List<Map<String, Object>> selectByParams(CheckDTO dto);
}
