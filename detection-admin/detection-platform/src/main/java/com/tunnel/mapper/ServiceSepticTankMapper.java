package com.tunnel.mapper;

import com.tunnel.domain.ServiceSepticTank;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 化粪池信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceSepticTankMapper {
    /**
     * 查询化粪池信息
     *
     * @param id 化粪池信息主键
     * @return 化粪池信息
     */
    public ServiceSepticTank selectServiceSepticTankById(Long id);

    /**
     * 查询化粪池信息列表
     *
     * @param serviceSepticTank 化粪池信息
     * @return 化粪池信息集合
     */
    public List<ServiceSepticTank> selectServiceSepticTankList(ServiceSepticTank serviceSepticTank);

    /**
     * 根据设施ID查询化粪池信息列表
     *
     * @param serviceAreaId 设施ID
     * @return 化粪池信息集合
     */
    public List<ServiceSepticTank> selectServiceSepticTankByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 根据设施ID和类型查询化粪池/隔油池信息列表
     *
     * @param serviceAreaId 设施ID
     * @param type 类型(化粪池/隔油池)
     * @return 化粪池信息集合
     */
    public List<ServiceSepticTank> selectServiceSepticTankByServiceAreaIdAndType(@Param("serviceAreaId") Long serviceAreaId, @Param("type") String type);

    /**
     * 新增化粪池信息
     *
     * @param serviceSepticTank 化粪池信息
     * @return 结果
     */
    public int insertServiceSepticTank(ServiceSepticTank serviceSepticTank);

    /**
     * 修改化粪池信息
     *
     * @param serviceSepticTank 化粪池信息
     * @return 结果
     */
    public int updateServiceSepticTank(ServiceSepticTank serviceSepticTank);

    /**
     * 删除化粪池信息
     *
     * @param id 化粪池信息主键
     * @return 结果
     */
    public int deleteServiceSepticTankById(Long id);

    /**
     * 批量删除化粪池信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceSepticTankByIds(Long[] ids);

    /**
     * 根据设施ID删除化粪池信息
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceSepticTankByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 根据设施ID和类型删除化粪池/隔油池信息
     *
     * @param serviceAreaId 设施ID
     * @param type 类型(化粪池/隔油池)
     * @return 结果
     */
    public int deleteServiceSepticTankByServiceAreaIdAndType(@Param("serviceAreaId") Long serviceAreaId, @Param("type") String type);
}
