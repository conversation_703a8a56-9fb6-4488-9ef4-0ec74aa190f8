package com.tunnel.mapper;

import com.tunnel.domain.ServiceBiochemicalSystemInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生化系统信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceBiochemicalSystemInfoMapper {
    /**
     * 查询生化系统信息
     *
     * @param id 生化系统信息主键
     * @return 生化系统信息
     */
    public ServiceBiochemicalSystemInfo selectServiceBiochemicalSystemInfoById(Long id);

    /**
     * 查询生化系统信息列表
     *
     * @param serviceBiochemicalSystemInfo 生化系统信息
     * @return 生化系统信息集合
     */
    public List<ServiceBiochemicalSystemInfo> selectServiceBiochemicalSystemInfoList(ServiceBiochemicalSystemInfo serviceBiochemicalSystemInfo);

    /**
     * 根据设施ID查询生化系统信息列表
     *
     * @param serviceAreaId 设施ID
     * @return 生化系统信息集合
     */
    public List<ServiceBiochemicalSystemInfo> selectServiceBiochemicalSystemInfoByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增生化系统信息
     *
     * @param serviceBiochemicalSystemInfo 生化系统信息
     * @return 结果
     */
    public int insertServiceBiochemicalSystemInfo(ServiceBiochemicalSystemInfo serviceBiochemicalSystemInfo);

    /**
     * 修改生化系统信息
     *
     * @param serviceBiochemicalSystemInfo 生化系统信息
     * @return 结果
     */
    public int updateServiceBiochemicalSystemInfo(ServiceBiochemicalSystemInfo serviceBiochemicalSystemInfo);

    /**
     * 删除生化系统信息
     *
     * @param id 生化系统信息主键
     * @return 结果
     */
    public int deleteServiceBiochemicalSystemInfoById(Long id);

    /**
     * 批量删除生化系统信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceBiochemicalSystemInfoByIds(Long[] ids);

    /**
     * 根据设施ID删除生化系统信息
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceBiochemicalSystemInfoByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
