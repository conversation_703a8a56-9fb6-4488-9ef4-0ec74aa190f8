package com.tunnel.mapper;

import com.tunnel.domain.CheckDTO;
import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.RequirementStatus;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备状态记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface RequirementStatusMapper 
{
    /**
     * 查询设备状态记录
     * 
     * @param id 设备状态记录主键
     * @return 设备状态记录
     */
    public RequirementStatus selectRequirementStatusById(String id);

    /**
     * 查询设备状态记录列表
     * 
     * @param requirementStatus 设备状态记录
     * @return 设备状态记录集合
     */
    public List<RequirementStatus> selectRequirementStatusList(RequirementStatus requirementStatus);

    /**
     * 新增设备状态记录
     * 
     * @param requirementStatus 设备状态记录
     * @return 结果
     */
    public int insertRequirementStatus(RequirementStatus requirementStatus);

    /**
     * 批量新增设备状态记录
     */
    public int insertBatch(@Param("list") List<RequirementStatus> list);

    /**
     * 修改设备状态记录
     * 
     * @param requirementStatus 设备状态记录
     * @return 结果
     */
    public int updateRequirementStatus(RequirementStatus requirementStatus);

    /**
     * 删除设备状态记录
     * 
     * @param id 设备状态记录主键
     * @return 结果
     */
    public int deleteRequirementStatusById(String id);

    /**
     * 批量删除设备状态记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRequirementStatusByIds(String[] ids);

    /**
     * 根据key查询设备状态记录
     *
     * @param key 对应的业务标识key(b_key)
     * @return
     */
    public List<RequirementStatus> listByKey(@Param(value = "key") String key);

    /**
     * 获取状态数据表的所有字段信息
     *
     * @return 字段信息列表，包含字段名和注释
     */
    public List<Map<String, Object>> getAllFields();

    CheckDTO selectTimeRange(CheckDTO dto);

    List<Map<String, Object>> selectByParams(CheckDTO dto);
}
