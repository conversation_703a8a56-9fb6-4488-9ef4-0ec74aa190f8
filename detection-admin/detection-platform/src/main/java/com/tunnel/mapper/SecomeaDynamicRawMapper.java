package com.tunnel.mapper;

import com.tunnel.domain.SecomeaRawRow;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 动态竖表读取（从 SLAVE2）
 */
public interface SecomeaDynamicRawMapper {

    List<SecomeaRawRow> selectBatchFromRaw(
            @Param("tableName") String tableName,
            @Param("offsetId") String offsetId,
            @Param("limitSize") int limitSize);

    int markProcessed(@Param("tableName") String tableName,
                      @Param("ids") List<String> ids);

    List<String> selectDistinctTimes(
            @Param("tableName") String tableName,
            @Param("lastTime") String lastTime,
            @Param("limitSize") int limitSize);

    List<SecomeaRawRow> selectByTimes(
            @Param("tableName") String tableName,
            @Param("times") List<String> times);

    int markProcessedByTimes(
            @Param("tableName") String tableName,
            @Param("times") List<String> times);
}


