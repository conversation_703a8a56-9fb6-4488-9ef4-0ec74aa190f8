package com.tunnel.mapper;

import com.tunnel.domain.MonitorAlarmProcessing;

import java.util.List;

/**
 * 监测指标报警Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorAlarmProcessingMapper 
{
    /**
     * 查询监测指标报警
     * 
     * @param id 监测指标报警主键
     * @return 监测指标报警
     */
    public MonitorAlarmProcessing selectMonitorAlarmProcessingById(Long id);

    /**
     * 查询监测指标报警列表
     * 
     * @param monitorAlarmProcessing 监测指标报警
     * @return 监测指标报警集合
     */
    public List<MonitorAlarmProcessing> selectMonitorAlarmProcessingList(MonitorAlarmProcessing monitorAlarmProcessing);

    /**
     * 新增监测指标报警
     * 
     * @param monitorAlarmProcessing 监测指标报警
     * @return 结果
     */
    public int insertMonitorAlarmProcessing(MonitorAlarmProcessing monitorAlarmProcessing);

    /**
     * 修改监测指标报警
     * 
     * @param monitorAlarmProcessing 监测指标报警
     * @return 结果
     */
    public int updateMonitorAlarmProcessing(MonitorAlarmProcessing monitorAlarmProcessing);

    /**
     * 删除监测指标报警
     * 
     * @param id 监测指标报警主键
     * @return 结果
     */
    public int deleteMonitorAlarmProcessingById(Long id);

    /**
     * 批量删除监测指标报警
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonitorAlarmProcessingByIds(Long[] ids);
}
