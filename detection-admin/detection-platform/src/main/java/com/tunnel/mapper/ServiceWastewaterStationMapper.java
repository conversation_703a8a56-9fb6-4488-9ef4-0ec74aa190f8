package com.tunnel.mapper;

import com.tunnel.domain.ServiceWastewaterStation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 污水站基本情况Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceWastewaterStationMapper {
    /**
     * 查询污水站基本情况
     *
     * @param id 污水站基本情况主键
     * @return 污水站基本情况
     */
    public ServiceWastewaterStation selectServiceWastewaterStationById(Long id);

    /**
     * 查询污水站基本情况列表
     *
     * @param serviceWastewaterStation 污水站基本情况
     * @return 污水站基本情况集合
     */
    public List<ServiceWastewaterStation> selectServiceWastewaterStationList(ServiceWastewaterStation serviceWastewaterStation);

    /**
     * 根据设施ID查询污水站基本情况列表
     *
     * @param serviceAreaId 设施ID
     * @return 污水站基本情况集合
     */
    public List<ServiceWastewaterStation> selectServiceWastewaterStationByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增污水站基本情况
     *
     * @param serviceWastewaterStation 污水站基本情况
     * @return 结果
     */
    public int insertServiceWastewaterStation(ServiceWastewaterStation serviceWastewaterStation);

    /**
     * 修改污水站基本情况
     *
     * @param serviceWastewaterStation 污水站基本情况
     * @return 结果
     */
    public int updateServiceWastewaterStation(ServiceWastewaterStation serviceWastewaterStation);

    /**
     * 删除污水站基本情况
     *
     * @param id 污水站基本情况主键
     * @return 结果
     */
    public int deleteServiceWastewaterStationById(Long id);

    /**
     * 批量删除污水站基本情况
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceWastewaterStationByIds(Long[] ids);

    /**
     * 根据设施ID删除污水站基本情况
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceWastewaterStationByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
