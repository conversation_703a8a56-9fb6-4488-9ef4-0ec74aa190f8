package com.tunnel.mapper;

import com.tunnel.domain.ServiceEquipmentInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceEquipmentInfoMapper {
    /**
     * 查询设备信息
     *
     * @param id 设备信息主键
     * @return 设备信息
     */
    public ServiceEquipmentInfo selectServiceEquipmentInfoById(Long id);

    /**
     * 查询设备信息列表
     *
     * @param serviceEquipmentInfo 设备信息
     * @return 设备信息集合
     */
    public List<ServiceEquipmentInfo> selectServiceEquipmentInfoList(ServiceEquipmentInfo serviceEquipmentInfo);

    /**
     * 根据设施ID查询设备信息列表
     *
     * @param serviceAreaId 设施ID
     * @return 设备信息集合
     */
    public List<ServiceEquipmentInfo> selectServiceEquipmentInfoByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增设备信息
     *
     * @param serviceEquipmentInfo 设备信息
     * @return 结果
     */
    public int insertServiceEquipmentInfo(ServiceEquipmentInfo serviceEquipmentInfo);

    /**
     * 修改设备信息
     *
     * @param serviceEquipmentInfo 设备信息
     * @return 结果
     */
    public int updateServiceEquipmentInfo(ServiceEquipmentInfo serviceEquipmentInfo);

    /**
     * 删除设备信息
     *
     * @param id 设备信息主键
     * @return 结果
     */
    public int deleteServiceEquipmentInfoById(Long id);

    /**
     * 批量删除设备信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceEquipmentInfoByIds(Long[] ids);

    /**
     * 根据设施ID删除设备信息
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceEquipmentInfoByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
