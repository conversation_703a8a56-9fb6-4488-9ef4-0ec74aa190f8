package com.tunnel.mapper;

import java.util.List;
import com.tunnel.domain.ServiceControlSystem;

/**
 * 控制系统Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
public interface ServiceControlSystemMapper 
{
    /**
     * 查询控制系统
     * 
     * @param id 控制系统主键
     * @return 控制系统
     */
    public ServiceControlSystem selectServiceControlSystemById(Long id);

    /**
     * 查询控制系统列表
     * 
     * @param serviceControlSystem 控制系统
     * @return 控制系统集合
     */
    public List<ServiceControlSystem> selectServiceControlSystemList(ServiceControlSystem serviceControlSystem);

    /**
     * 根据服务区域ID查询控制系统列表
     * 
     * @param serviceAreaId 服务区域ID
     * @return 控制系统集合
     */
    public List<ServiceControlSystem> selectServiceControlSystemByServiceAreaId(Long serviceAreaId);

    /**
     * 新增控制系统
     * 
     * @param serviceControlSystem 控制系统
     * @return 结果
     */
    public int insertServiceControlSystem(ServiceControlSystem serviceControlSystem);

    /**
     * 修改控制系统
     * 
     * @param serviceControlSystem 控制系统
     * @return 结果
     */
    public int updateServiceControlSystem(ServiceControlSystem serviceControlSystem);

    /**
     * 删除控制系统
     * 
     * @param id 控制系统主键
     * @return 结果
     */
    public int deleteServiceControlSystemById(Long id);

    /**
     * 批量删除控制系统
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceControlSystemByIds(Long[] ids);

    /**
     * 根据服务区域ID删除控制系统
     * 
     * @param serviceAreaId 服务区域ID
     * @return 结果
     */
    public int deleteServiceControlSystemByServiceAreaId(Long serviceAreaId);
}
