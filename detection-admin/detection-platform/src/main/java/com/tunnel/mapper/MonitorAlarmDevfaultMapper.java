package com.tunnel.mapper;

import com.tunnel.domain.MonitorAlarmDevfault;

import java.util.List;

/**
 * 设备故障报警Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorAlarmDevfaultMapper 
{
    /**
     * 查询设备故障报警
     * 
     * @param id 设备故障报警主键
     * @return 设备故障报警
     */
    public MonitorAlarmDevfault selectMonitorAlarmDevfaultById(Long id);

    /**
     * 查询设备故障报警列表
     * 
     * @param monitorAlarmDevfault 设备故障报警
     * @return 设备故障报警集合
     */
    public List<MonitorAlarmDevfault> selectMonitorAlarmDevfaultList(MonitorAlarmDevfault monitorAlarmDevfault);

    /**
     * 新增设备故障报警
     * 
     * @param monitorAlarmDevfault 设备故障报警
     * @return 结果
     */
    public int insertMonitorAlarmDevfault(MonitorAlarmDevfault monitorAlarmDevfault);

    /**
     * 修改设备故障报警
     * 
     * @param monitorAlarmDevfault 设备故障报警
     * @return 结果
     */
    public int updateMonitorAlarmDevfault(MonitorAlarmDevfault monitorAlarmDevfault);

    /**
     * 删除设备故障报警
     * 
     * @param id 设备故障报警主键
     * @return 结果
     */
    public int deleteMonitorAlarmDevfaultById(Long id);

    /**
     * 批量删除设备故障报警
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonitorAlarmDevfaultByIds(Long[] ids);
}
