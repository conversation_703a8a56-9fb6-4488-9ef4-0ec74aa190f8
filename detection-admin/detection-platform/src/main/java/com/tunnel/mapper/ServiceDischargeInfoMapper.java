package com.tunnel.mapper;

import com.tunnel.domain.ServiceDischargeInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 排放情况Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServiceDischargeInfoMapper {
    /**
     * 查询排放情况
     *
     * @param id 排放情况主键
     * @return 排放情况
     */
    public ServiceDischargeInfo selectServiceDischargeInfoById(Long id);

    /**
     * 查询排放情况列表
     *
     * @param serviceDischargeInfo 排放情况
     * @return 排放情况集合
     */
    public List<ServiceDischargeInfo> selectServiceDischargeInfoList(ServiceDischargeInfo serviceDischargeInfo);

    /**
     * 根据设施ID查询排放情况列表
     *
     * @param serviceAreaId 设施ID
     * @return 排放情况集合
     */
    public List<ServiceDischargeInfo> selectServiceDischargeInfoByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增排放情况
     *
     * @param serviceDischargeInfo 排放情况
     * @return 结果
     */
    public int insertServiceDischargeInfo(ServiceDischargeInfo serviceDischargeInfo);

    /**
     * 修改排放情况
     *
     * @param serviceDischargeInfo 排放情况
     * @return 结果
     */
    public int updateServiceDischargeInfo(ServiceDischargeInfo serviceDischargeInfo);

    /**
     * 删除排放情况
     *
     * @param id 排放情况主键
     * @return 结果
     */
    public int deleteServiceDischargeInfoById(Long id);

    /**
     * 批量删除排放情况
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceDischargeInfoByIds(Long[] ids);

    /**
     * 根据设施ID删除排放情况
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServiceDischargeInfoByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
