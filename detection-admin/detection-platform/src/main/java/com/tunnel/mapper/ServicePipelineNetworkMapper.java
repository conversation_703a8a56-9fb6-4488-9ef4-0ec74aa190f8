package com.tunnel.mapper;

import com.tunnel.domain.ServicePipelineNetwork;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 管网信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ServicePipelineNetworkMapper {
    /**
     * 查询管网信息
     *
     * @param id 管网信息主键
     * @return 管网信息
     */
    public ServicePipelineNetwork selectServicePipelineNetworkById(Long id);

    /**
     * 查询管网信息列表
     *
     * @param servicePipelineNetwork 管网信息
     * @return 管网信息集合
     */
    public List<ServicePipelineNetwork> selectServicePipelineNetworkList(ServicePipelineNetwork servicePipelineNetwork);

    /**
     * 根据设施ID查询管网信息列表
     *
     * @param serviceAreaId 设施ID
     * @return 管网信息集合
     */
    public List<ServicePipelineNetwork> selectServicePipelineNetworkByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);

    /**
     * 新增管网信息
     *
     * @param servicePipelineNetwork 管网信息
     * @return 结果
     */
    public int insertServicePipelineNetwork(ServicePipelineNetwork servicePipelineNetwork);

    /**
     * 修改管网信息
     *
     * @param servicePipelineNetwork 管网信息
     * @return 结果
     */
    public int updateServicePipelineNetwork(ServicePipelineNetwork servicePipelineNetwork);

    /**
     * 删除管网信息
     *
     * @param id 管网信息主键
     * @return 结果
     */
    public int deleteServicePipelineNetworkById(Long id);

    /**
     * 批量删除管网信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServicePipelineNetworkByIds(Long[] ids);

    /**
     * 根据设施ID删除管网信息
     *
     * @param serviceAreaId 设施ID
     * @return 结果
     */
    public int deleteServicePipelineNetworkByServiceAreaId(@Param("serviceAreaId") Long serviceAreaId);
}
