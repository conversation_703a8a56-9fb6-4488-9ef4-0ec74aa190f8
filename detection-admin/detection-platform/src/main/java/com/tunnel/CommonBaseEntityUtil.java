package com.tunnel;

import com.tunnel.common.core.domain.BaseEntity;
import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.core.domain.model.LoginUser;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.spring.SpringUtils;
import com.tunnel.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j

public class CommonBaseEntityUtil {

    public static void initUserNameByList(List list) {
        if(CollectionUtils.isEmpty(list)){
            return ;
        }
        Set<Long> userIdList = new HashSet<>();
        for (Object temp : list) {
            BaseEntity baseEntity = (BaseEntity) temp;
            userIdList.add(baseEntity.getCreator());
            userIdList.add(baseEntity.getModifier());
        }
        SysUserMapper sysUserMapper = SpringUtils.getBean(SysUserMapper.class);
        List<SysUser> userList = sysUserMapper.selectByUserIdList(Lists.newArrayList(userIdList.iterator()));
        Map<Long, String> userMap = userList.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getNickName, (key1, key2) -> key1));
        for (Object temp : list) {
            BaseEntity baseEntity = (BaseEntity) temp;
            baseEntity.setCreateBy(userMap.get(baseEntity.getCreator()));
            baseEntity.setUpdateBy(userMap.get(baseEntity.getModifier()));
        }
    }

    public static void setBaseEntityInfo(BaseEntity baseEntity) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        baseEntity.setCreator(loginUser.getUserId());
    }
}
