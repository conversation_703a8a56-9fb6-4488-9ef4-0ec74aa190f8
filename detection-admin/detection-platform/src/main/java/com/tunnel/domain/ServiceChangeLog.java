package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 服务区/收费站档案变更日志对象 sc_service_change_log
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceChangeLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 变更的表名 */
    @Excel(name = "变更表名", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "变更表名不能为空")
    @Size(max = 100, message = "变更表名长度不能超过100个字符")
    private String tableName;

    /** 变更记录的主键ID */
    @Excel(name = "记录ID", cellType = Excel.ColumnType.STRING)
    @NotNull(message = "记录ID不能为空")
    @Size(max = 50, message = "记录ID长度不能超过50个字符")
    private Long recordId;

    /** 操作类型：新增/修改/删除 */
    @Excel(name = "操作类型", readConverterExp = "INSERT=新增,UPDATE=修改,DELETE=删除")
    @NotBlank(message = "操作类型不能为空")
    private String operationType;

    /** 变更前的完整记录（JSON格式） */
    @Excel(name = "变更前数据", cellType = Excel.ColumnType.STRING)
    private String oldValues;

    /** 变更后的完整记录（JSON格式） */
    @Excel(name = "变更后数据", cellType = Excel.ColumnType.STRING)
    private String newValues;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;

    // 扩展字段，用于查询
    /** 设施名称（用于关联查询） */
    @Excel(name = "设施名称", cellType = Excel.ColumnType.STRING)
    private String facilityName;

    /** 操作人姓名（用于显示） */
    @Excel(name = "操作人", cellType = Excel.ColumnType.STRING)
    private String operatorName;

    /** 查询开始时间 */
    private String beginTime;

    /** 查询结束时间 */
    private String endTime;
}
