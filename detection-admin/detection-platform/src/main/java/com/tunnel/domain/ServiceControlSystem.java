package com.tunnel.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;

/**
 * 控制系统对象 service_control_system
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
public class ServiceControlSystem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 服务区域ID */
    @Excel(name = "服务区域ID")
    private Long serviceAreaId;

    /** 区域 */
    @Excel(name = "区域")
    private String area;

    /** 有无PLC */
    @Excel(name = "有无PLC")
    private String hasPlc;

    /** PLC运行状态 */
    @Excel(name = "PLC运行状态")
    private String plcStatus;

    /** 主要设备控制方式 */
    @Excel(name = "主要设备控制方式")
    private String controlMethod;

    /** 主要电气元件 */
    @Excel(name = "主要电气元件")
    private String electricalComponents;

    /** 其他情况 */
    @Excel(name = "其他情况")
    private String otherSituation;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setServiceAreaId(Long serviceAreaId) 
    {
        this.serviceAreaId = serviceAreaId;
    }

    public Long getServiceAreaId() 
    {
        return serviceAreaId;
    }

    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }

    public void setHasPlc(String hasPlc) 
    {
        this.hasPlc = hasPlc;
    }

    public String getHasPlc() 
    {
        return hasPlc;
    }

    public void setPlcStatus(String plcStatus) 
    {
        this.plcStatus = plcStatus;
    }

    public String getPlcStatus() 
    {
        return plcStatus;
    }

    public void setControlMethod(String controlMethod) 
    {
        this.controlMethod = controlMethod;
    }

    public String getControlMethod() 
    {
        return controlMethod;
    }

    public void setElectricalComponents(String electricalComponents) 
    {
        this.electricalComponents = electricalComponents;
    }

    public String getElectricalComponents() 
    {
        return electricalComponents;
    }

    public void setOtherSituation(String otherSituation) 
    {
        this.otherSituation = otherSituation;
    }

    public String getOtherSituation() 
    {
        return otherSituation;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("serviceAreaId", getServiceAreaId())
            .append("area", getArea())
            .append("hasPlc", getHasPlc())
            .append("plcStatus", getPlcStatus())
            .append("controlMethod", getControlMethod())
            .append("electricalComponents", getElectricalComponents())
            .append("otherSituation", getOtherSituation())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
