package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 化粪池信息对象 sc_service_septic_tank
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceSepticTank extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施ID(服务区或收费站) */
    @Excel(name = "设施ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "设施ID不能为空")
    private Long serviceAreaId;

    /** 区域类型(东区/西区) */
    @Excel(name = "区域类型", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "区域类型不能为空")
    @Size(max = 20, message = "区域类型长度不能超过20个字符")
    private String areaType;

    /** 类型(化粪池/隔油池) */
    @Excel(name = "类型", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "类型不能为空")
    @Size(max = 20, message = "类型长度不能超过20个字符")
    private String type;

    /** 尺寸容积(m*m*m) */
    @Excel(name = "尺寸容积", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "尺寸容积长度不能超过100个字符")
    private String sizeVolume;

    /** 材质 */
    @Excel(name = "材质", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "材质长度不能超过50个字符")
    private String material;

    /** 淤积情况 */
    @Excel(name = "淤积情况", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "淤积情况长度不能超过50个字符")
    private String siltationStatus;

    /** 废水接入情况 */
    @Excel(name = "废水接入情况", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "废水接入情况长度不能超过100个字符")
    private String wastewaterAccess;

    /** 其他情况 */
    @Excel(name = "其他情况", cellType = Excel.ColumnType.STRING)
    private String otherConditions;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
