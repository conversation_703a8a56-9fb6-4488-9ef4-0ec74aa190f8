package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 污水站基本情况对象 sc_service_wastewater_station
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceWastewaterStation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施ID(服务区或收费站) */
    @NotNull(message = "设施ID不能为空")
    private Long serviceAreaId;

    /** 区域类型(东区/西区) */
    @Excel(name = "区域类型", cellType = Excel.ColumnType.STRING)
    @Size(max = 20, message = "区域类型长度不能超过20个字符")
    private String areaType;

    /** 处理工艺 */
    @Excel(name = "处理工艺", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "处理工艺长度不能超过50个字符")
    private String treatmentProcess;

    /** 设计处理规模(t/d) */
    @Excel(name = "设计处理规模(t/d)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal designCapacity;

    /** 结构材质 */
    @Excel(name = "结构材质", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "结构材质长度不能超过50个字符")
    private String structureMaterial;

    /** 建成/改造时间 */
    @Excel(name = "建成/改造时间")
    private String constructionDate;

    /** 运行状态 */
    @Excel(name = "运行状态", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "运行状态长度不能超过50个字符")
    private String operationStatus;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
