package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 月报对象 monthly_report
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ToString
public class MonthlyReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 监测类型 */
    @Excel(name = "监测类型")
    private String monitorType;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 日期 */
    @Excel(name = "日期")
    private String dateTime;

    /** 地区 */
    @Excel(name = "地区")
    private String district;

    /** 监测指标 */
    @Excel(name = "监测指标")
    private String factor;

    /** 监测指标编码 */
    @Excel(name = "监测指标编码")
    private String factorCode;

    /** 监测系统 */
    @Excel(name = "监测系统")
    private String integrationSystem;

    /** 最大值 */
    @Excel(name = "最大值")
    private BigDecimal maxValue;

    /** 最小值 */
    @Excel(name = "最小值")
    private BigDecimal minValue;

    /** 服务区 */
    @Excel(name = "服务区")
    private String serviceArea;

    /** 传输率 */
    @Excel(name = "传输率")
    private String transmissionRate;

    /** 上传率 */
    @Excel(name = "上传率")
    private String uploadRate;

    /** 系统编码 */
    @Excel(name = "系统编码")
    private String systemCode;

    /** 服务区编码 */
    @Excel(name = "服务区编码")
    private String serviceCode;

    /** 监测指标 */
    @Excel(name = "监测指标")
    private String dischargeAmount;

    /** 平均值 */
    @Excel(name = "平均值")
    private BigDecimal averageValue;
}
