package com.tunnel.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 监测站点DTO
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@ApiModel("监测站点DTO")
public class MonitorStationDTO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty(value = "站点编码", required = true)
    @NotBlank(message = "站点编码不能为空")
    @Size(max = 255, message = "站点编码长度不能超过255个字符")
    private String code;

    @ApiModelProperty("站点名称")
    @Size(max = 255, message = "站点名称长度不能超过255个字符")
    private String name;

    @ApiModelProperty(value = "省份", required = true)
    @NotBlank(message = "省份不能为空")
    @Size(max = 255, message = "省份长度不能超过255个字符")
    private String province;

    @ApiModelProperty(value = "城市", required = true)
    @NotBlank(message = "城市不能为空")
    @Size(max = 255, message = "城市长度不能超过255个字符")
    private String city;

    @ApiModelProperty(value = "地区", required = true)
    @NotBlank(message = "地区不能为空")
    @Size(max = 255, message = "地区长度不能超过255个字符")
    private String district;

    @ApiModelProperty("分公司")
    @Size(max = 255, message = "分公司名称长度不能超过255个字符")
    private String companyName;

    @ApiModelProperty("经度")
    @DecimalMin(value = "-180.0", message = "经度范围为-180到180")
    @DecimalMax(value = "180.0", message = "经度范围为-180到180")
    private BigDecimal lat;

    @ApiModelProperty("纬度")
    @DecimalMin(value = "-90.0", message = "纬度范围为-90到90")
    @DecimalMax(value = "90.0", message = "纬度范围为-90到90")
    private BigDecimal lon;

    @ApiModelProperty("开通情况")
    @Size(max = 255, message = "开通情况长度不能超过255个字符")
    private String openingStatus;

    @ApiModelProperty("运维分类")
    @Size(max = 255, message = "运维分类长度不能超过255个字符")
    private String maintenanceCategory;

    @ApiModelProperty("污水处理工艺")
    @Size(max = 255, message = "污水处理工艺长度不能超过255个字符")
    private String sewageProcess;

    @ApiModelProperty("设备型号")
    @Size(max = 255, message = "设备型号长度不能超过255个字符")
    private String equipmentModel;

    @ApiModelProperty("设备数量")
    @Min(value = 0, message = "设备数量不能小于0")
    private Integer equipmentQuantity;

    @ApiModelProperty("处理规模（t/d）")
    @DecimalMin(value = "0.0", message = "处理规模不能小于0")
    private BigDecimal processingScale;

    @ApiModelProperty("运维类型")
    @Size(max = 255, message = "运维类型长度不能超过255个字符")
    private String maintenanceType;

    @ApiModelProperty("污染类型")
    @Size(max = 255, message = "污染类型长度不能超过255个字符")
    private String pollutionType;

    @ApiModelProperty("状态：0-正常，1-异常")
    @Min(value = 0, message = "状态值只能为0或1")
    @Max(value = 1, message = "状态值只能为0或1")
    private Integer state;

    @ApiModelProperty("故障状态：0-正常，1-故障")
    @Min(value = 0, message = "故障状态值只能为0或1")
    @Max(value = 1, message = "故障状态值只能为0或1")
    private Integer faulty;

    @ApiModelProperty("站点顺序")
    @Min(value = 0, message = "站点顺序不能小于0")
    private Integer stationIndex;

    @ApiModelProperty("备注")
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private Long creator;

    @ApiModelProperty("更新人")
    private Long modifier;

    // ========== 查询条件字段 ==========

    @ApiModelProperty("系统编码（查询条件）")
    private String systemCode;

    @ApiModelProperty("在线设备数量")
    private Integer onlineDeviceCount;

    @ApiModelProperty("离线设备数量")
    private Integer offlineDeviceCount;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("页面大小")
    private Integer pageSize = 10;
}
