package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * 控制网关和用户关联对象 processing_gateway_user_rel
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Builder
@ToString
public class ProcessingGatewayUserRel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 远程网关编码 */
    @Excel(name = "远程网关编码")
    private String code;

    /** 网关mac地址 */
    @Excel(name = "网关mac地址")
    private String mac;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String userName;
}
