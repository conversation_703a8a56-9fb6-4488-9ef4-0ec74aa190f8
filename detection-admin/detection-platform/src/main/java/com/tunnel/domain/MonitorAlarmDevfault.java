package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 设备故障报警对象 monitor_alarm_devfault
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ToString
public class MonitorAlarmDevfault extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 监测指标id */
    @Excel(name = "监测指标id")
    private String factorId;

    /** 监测指标 */
    @Excel(name = "监测指标")
    private String factorName;

    /** 是否已处理 */
    @Excel(name = "是否已处理")
    private Integer handled;

    /** 服务区名称 */
    @Excel(name = "服务区名称")
    private String serviceArea;

    /** 服务区编码 */
    @Excel(name = "服务区编码")
    private String serviceId;

    /** 系统编码 */
    @Excel(name = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 报警时间 */
    @Excel(name = "报警时间")
    private String time;

    /** 设备ID */
    @Excel(name = "设备ID")
    private String sensorId;

    /** 监测指标编码 */
//    @Excel(name = "监测指标编码")
//    private String factorCode;

    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "页面大小")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
