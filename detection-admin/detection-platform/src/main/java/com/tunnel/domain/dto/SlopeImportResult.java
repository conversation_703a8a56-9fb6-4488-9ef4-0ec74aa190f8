package com.tunnel.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 边坡导入结果DTO
 * <AUTHOR>
 */
@Data
public class SlopeImportResult {
    /** 成功数量 */
    private int successCount;
    
    /** 失败数量 */
    private int failCount;
    
    /** 成功消息列表 */
    private List<String> successMessages;
    
    /** 失败消息列表 */
    private List<String> failMessages;
    
    /** 总计处理数量 */
    private int totalCount;
    
    public SlopeImportResult() {
        this.successCount = 0;
        this.failCount = 0;
        this.totalCount = 0;
    }
    
    public void addSuccess(String message) {
        this.successCount++;
        this.totalCount++;
        if (this.successMessages != null) {
            this.successMessages.add(message);
        }
    }
    
    public void addFail(String message) {
        this.failCount++;
        this.totalCount++;
        if (this.failMessages != null) {
            this.failMessages.add(message);
        }
    }
    
    public boolean hasFailures() {
        return failCount > 0;
    }
    
    public String getResultMessage() {
        if (hasFailures()) {
            return String.format("导入完成！成功 %d 条，失败 %d 条", successCount, failCount);
        } else {
            return String.format("导入成功！共导入 %d 条数据", successCount);
        }
    }
} 