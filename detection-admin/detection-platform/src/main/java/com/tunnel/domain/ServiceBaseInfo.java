package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 服务区/收费站基本信息对象 sc_service_base_info
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceBaseInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施类型(1:服务区, 2:收费站) */
    @Excel(name = "设施类型", readConverterExp = "1=服务区,2=收费站")
    private Integer facilityType;

    /** 设施名称(服务区名称或收费站名称) */
    @Excel(name = "设施名称", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "设施名称不能为空")
    @Size(max = 100, message = "设施名称长度不能超过100个字符")
    private String facilityName;

    /** 档案编号 */
    @Excel(name = "档案编号", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "档案编号长度不能超过50个字符")
    private String fileNumber;

    /** 分公司 */
    @Excel(name = "分公司", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "分公司长度不能超过100个字符")
    private String branchCompany;

    /** 所在高速 */
    @Excel(name = "所在高速", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "所在高速长度不能超过100个字符")
    private String highwayName;

    /** 建成年份 */
    @Excel(name = "建成年份", cellType = Excel.ColumnType.NUMERIC)
    private Integer constructionYear;

    /** 是否有互通 */
    @Excel(name = "是否有互通", readConverterExp = "0=否,1=是")
    private Integer hasInterchange;

    /** 建筑物构成 */
    @Excel(name = "建筑物构成", cellType = Excel.ColumnType.STRING)
    private String buildingComposition;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
