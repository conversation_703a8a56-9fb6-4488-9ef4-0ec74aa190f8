package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 管网信息对象 sc_service_pipeline_network
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServicePipelineNetwork extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施ID(服务区或收费站) */
    @Excel(name = "设施ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "设施ID不能为空")
    private Long serviceAreaId;

    /** 区域类型(东区/西区) */
    @Excel(name = "区域类型", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "区域类型不能为空")
    @Size(max = 20, message = "区域类型长度不能超过20个字符")
    private String areaType;

    /** 雨污分流情况 */
    @Excel(name = "雨污分流情况", cellType = Excel.ColumnType.STRING)
    @Size(max = 20, message = "雨污分流情况长度不能超过20个字符")
    private String rainSewageSeparation;

    /** 管径(mm) */
    @Excel(name = "管径(mm)", cellType = Excel.ColumnType.NUMERIC)
    private Integer pipeDiameter;

    /** 管道材质 */
    @Excel(name = "管道材质", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "管道材质长度不能超过100个字符")
    private String pipeMaterial;

    /** 运行情况 */
    @Excel(name = "运行情况", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "运行情况长度不能超过50个字符")
    private String operationStatus;

    /** 其他情况 */
    @Excel(name = "其他情况", cellType = Excel.ColumnType.STRING)
    private String otherConditions;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
