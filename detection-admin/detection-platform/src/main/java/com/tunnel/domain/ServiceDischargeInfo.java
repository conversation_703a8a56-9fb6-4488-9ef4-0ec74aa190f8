package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 排放情况对象 sc_service_discharge_info
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceDischargeInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施ID(服务区或收费站) */
    @Excel(name = "设施ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "设施ID不能为空")
    private Long serviceAreaId;

    /** 区域类型(东区/西区) */
    @Excel(name = "区域类型", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "区域类型不能为空")
    @Size(max = 20, message = "区域类型长度不能超过20个字符")
    private String areaType;

    /** 排口位置 */
    @Excel(name = "排口位置", cellType = Excel.ColumnType.STRING)
    @Size(max = 200, message = "排口位置长度不能超过200个字符")
    private String outletLocation;

    /** 排放去向 */
    @Excel(name = "排放去向", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "排放去向长度不能超过100个字符")
    private String dischargeDestination;

    /** 外部环境 */
    @Excel(name = "外部环境", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "外部环境长度不能超过100个字符")
    private String externalEnvironment;

    /** 其他情况 */
    @Excel(name = "其他情况", cellType = Excel.ColumnType.STRING)
    private String otherConditions;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
