package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 监测指标报警对象 monitor_alarm_processing
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ToString
public class MonitorAlarmProcessing extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 报警临界值 */
    @Excel(name = "报警临界值")
    private BigDecimal alarmLimit;

    /** 监测指标编码 */
    @Excel(name = "监测指标编码")
    private String factorId;

    /** 监测指标code */
//    @Excel(name = "监测指标code")
//    private String factorCode;

    /** 监测指标名称 */
    @Excel(name = "监测指标名称")
    private String factorName;

    /** 是否已处理 */
    @Excel(name = "是否已处理")
    private Integer handled;

    /** 报警级别 */
    @Excel(name = "报警级别")
    private String level;

    /** 监测指标 */
    @Excel(name = "监测指标")
    private BigDecimal max;

    /** 监测指标 */
    @Excel(name = "监测指标")
    private BigDecimal min;

    /** 服务区名称 */
    @Excel(name = "服务区名称")
    private String serviceArea;

    /** 服务区编码 */
    @Excel(name = "服务区编码")
    private String serviceId;

    /** 监测系统编码 */
    @Excel(name = "监测系统编码")
    private String systemCode;

    /** 监测系统名称 */
    @Excel(name = "监测系统名称")
    private String systemName;

    /** 报警时间 */
    @Excel(name = "报警时间")
    private String time;

//    /** 监测指标 */
//    @Excel(name = "监测指标")
//    private BigDecimal realValue;
//
//    /** 报警级别 */
//    @Excel(name = "报警级别")
//    private String alarmLevel;

    @Excel(name = "指标值")
    private BigDecimal value;

    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "页面大小")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
