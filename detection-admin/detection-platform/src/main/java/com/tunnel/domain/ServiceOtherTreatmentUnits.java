package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 其他处理单元/设备对象 sc_service_other_treatment_units
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceOtherTreatmentUnits extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施ID(服务区或收费站) */
    @Excel(name = "设施ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "设施ID不能为空")
    private Long serviceAreaId;

    /** 区域类型(东区/西区) */
    @Excel(name = "区域类型", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "区域类型不能为空")
    @Size(max = 20, message = "区域类型长度不能超过20个字符")
    private String areaType;

    /** 处理单元名称 */
    @Excel(name = "处理单元名称", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "处理单元名称不能为空")
    @Size(max = 100, message = "处理单元名称长度不能超过100个字符")
    private String unitName;

    /** 运行状态 */
    @Excel(name = "运行状态", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "运行状态长度不能超过50个字符")
    private String operationStatus;

    /** 对应设备名称 */
    @Excel(name = "对应设备名称", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "对应设备名称长度不能超过100个字符")
    private String equipmentName;

    /** 厂家型号 */
    @Excel(name = "厂家型号", cellType = Excel.ColumnType.STRING)
    @Size(max = 200, message = "厂家型号长度不能超过200个字符")
    private String manufacturerModel;

    /** 设备参数 */
    @Excel(name = "设备参数", cellType = Excel.ColumnType.STRING)
    private String equipmentParameters;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
