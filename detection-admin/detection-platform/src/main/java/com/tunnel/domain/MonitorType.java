package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/8/12  18:36
 * @since 1.0.0
 */
@Data
@ToString
public class MonitorType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 监测类型编码 */
    @Excel(name = "监测类型编码")
    private String code;

    /** 监测类型名称 */
    @Excel(name = "监测类型名称")
    private String name;

    /** 创建人 */
    @Excel(name = "创建人")
    private Long creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private Long modifier;

}

