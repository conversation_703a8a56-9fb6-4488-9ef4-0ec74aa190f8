package com.tunnel.domain.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 隧道数据导入结果DTO
 */
@Data
public class TunnelImportResult {
    /**
     * 成功导入的数量
     */
    private int successCount = 0;
    
    /**
     * 失败导入的数量
     */
    private int failureCount = 0;
    
    /**
     * 成功导入的记录信息
     */
    private List<String> successMessages = new ArrayList<>();
    
    /**
     * 失败导入的记录信息
     */
    private List<String> failureMessages = new ArrayList<>();
    
    /**
     * 添加成功记录
     */
    public void addSuccessMessage(String message) {
        successCount++;
        successMessages.add(message);
    }
    
    /**
     * 添加失败记录
     */
    public void addFailureMessage(String message) {
        failureCount++;
        failureMessages.add(message);
    }
    
    /**
     * 获取总处理数量
     */
    public int getTotalCount() {
        return successCount + failureCount;
    }
    
    /**
     * 是否有失败记录
     */
    public boolean hasFailures() {
        return failureCount > 0;
    }
    
    /**
     * 获取结果摘要
     */
    public String getSummary() {
        if (hasFailures()) {
            return String.format("导入完成，成功%d条，失败%d条", successCount, failureCount);
        } else {
            return String.format("导入成功，共处理%d条记录", successCount);
        }
    }
} 