package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用水情况对象 sc_service_water_usage
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceWaterUsage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施ID(服务区或收费站) */
    @NotNull(message = "设施ID不能为空")
    private Long serviceAreaId;

    /** 水源 */
    @Excel(name = "水源", cellType = Excel.ColumnType.STRING)
    @Size(max = 50, message = "水源长度不能超过50个字符")
    private String waterSource;

    /** 日常用水量(吨) */
    @Excel(name = "日常用水量(吨)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal dailyUsage;

    /** 高峰期用水量(吨) */
    @Excel(name = "高峰期用水量(吨)", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal peakUsage;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
