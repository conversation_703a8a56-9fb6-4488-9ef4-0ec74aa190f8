package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

/**
 * 监测预警指标配置对象 sc_monitor_alarm_field_config
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Data
@ToString
public class MonitorAlarmFieldConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Integer alarmEnabled;

    /** 设备指标 */
    @Excel(name = "设备指标")
    private String field;

    /** 服务区编码 */
    @Excel(name = "服务区编码")
    private String serviceId;

    /** mac地址 */
    @Excel(name = "mac地址")
    private String mac;

    /** 设备指标名称 */
    @Excel(name = "设备指标名称")
    private String fieldName;

    /** 服务区名称 */
    @Excel(name = "服务区名称")
    private String serviceArea;

    /** 预警名称 */
    @Excel(name = "预警名称")
    private String name;

    /** 电子邮箱 */
    @Excel(name = "电子邮箱")
    private String emailAddress;

    /** 是否启用电子邮箱 */
    @Excel(name = "是否启用电子邮箱")
    private Integer emailEnabled;

    /** 上次邮件通知时间 */
    @Excel(name = "上次邮件通知时间")
    private String lastTime;

    /** 是否短信提醒 */
    @Excel(name = "是否短信提醒")
    private Integer messageEnabled;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phoneNum;

    /** 邮件通知时间间隔 */
    @Excel(name = "邮件通知时间间隔")
    private Long timeInterval;

    /** 上次短信通知时间 */
    @Excel(name = "上次短信通知时间")
    private String lastMessageTime;

    /** 短信通知时间间隔 */
    @Excel(name = "短信通知时间间隔")
    private Long messageTimeInterval;

    private Integer pageNum;

    private Integer pageSize;
}
