package com.tunnel.domain.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 桥梁导入结果DTO
 * <AUTHOR>
 */
@Data
public class BridgeImportResult {
    
    /** 成功导入的数量 */
    private int successCount = 0;
    
    /** 失败导入的数量 */
    private int failureCount = 0;
    
    /** 成功导入的记录信息 */
    private List<String> successMessages = new ArrayList<>();
    
    /** 失败导入的记录信息 */
    private List<String> failureMessages = new ArrayList<>();
    
    /** 是否全部成功 */
    public boolean isAllSuccess() {
        return failureCount == 0;
    }
    
    /** 添加成功记录 */
    public void addSuccess(String message) {
        successCount++;
        successMessages.add(message);
    }
    
    /** 添加失败记录 */
    public void addFailure(String message) {
        failureCount++;
        failureMessages.add(message);
    }
    
    /** 获取结果摘要 */
    public String getSummary() {
        if (isAllSuccess()) {
            return "恭喜您，数据已全部导入成功！共 " + successCount + " 条";
        } else {
            return "导入完成！成功 " + successCount + " 条，失败 " + failureCount + " 条";
        }
    }
    
    /** 获取详细结果信息 */
    public String getDetailMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(getSummary());
        
        if (!successMessages.isEmpty()) {
            sb.append("<br/><br/>成功记录：");
            for (int i = 0; i < successMessages.size(); i++) {
                sb.append("<br/>").append(i + 1).append("、").append(successMessages.get(i));
            }
        }
        
        if (!failureMessages.isEmpty()) {
            sb.append("<br/><br/>失败记录：");
            for (int i = 0; i < failureMessages.size(); i++) {
                sb.append("<br/>").append(i + 1).append("、").append(failureMessages.get(i));
            }
        }
        
        return sb.toString();
    }
} 