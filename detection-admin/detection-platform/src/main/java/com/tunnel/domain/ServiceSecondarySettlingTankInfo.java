package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 二沉池信息对象 sc_service_secondary_settling_tank_info
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceSecondarySettlingTankInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施ID(服务区或收费站) */
    @Excel(name = "设施ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "设施ID不能为空")
    private Long serviceAreaId;

    /** 区域类型(东区/西区) */
    @Excel(name = "区域类型", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "区域类型不能为空")
    @Size(max = 20, message = "区域类型长度不能超过20个字符")
    private String areaType;

    /** 填料形式 */
    @Excel(name = "填料形式", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "填料形式长度不能超过100个字符")
    private String fillerType;

    /** 填料状态 */
    @Excel(name = "填料状态", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "填料状态长度不能超过100个字符")
    private String fillerStatus;

    /** 工艺参数(有效池容、有效池深) */
    @Excel(name = "工艺参数", cellType = Excel.ColumnType.STRING)
    private String processParameters;

    /** 污泥回流及运行状态 */
    @Excel(name = "污泥回流及运行状态", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "污泥回流及运行状态长度不能超过100个字符")
    private String sludgeRefluxStatus;

    /** 浮泥及泥水分离情况 */
    @Excel(name = "浮泥及泥水分离情况", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "浮泥及泥水分离情况长度不能超过100个字符")
    private String floatingSludgeStatus;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
