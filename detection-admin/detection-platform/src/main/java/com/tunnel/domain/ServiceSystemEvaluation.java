package com.tunnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 系统整体评价对象 sc_service_system_evaluation
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceSystemEvaluation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设施ID(服务区或收费站) */
    @Excel(name = "设施ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "设施ID不能为空")
    private Long serviceAreaId;

    /** 评价内容 */
    @Excel(name = "评价内容", cellType = Excel.ColumnType.STRING)
    private String evaluationContent;

    /** 评价日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "评价日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date evaluationDate;

    /** 评价人 */
    @Excel(name = "评价人", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "评价人长度不能超过100个字符")
    private String evaluator;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;
}
