package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 监测站点和类型关联对象 monitor_station_relation
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Builder
@ToString
public class MonitorStationRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 站点编码 */
    @Excel(name = "站点编码")
    private String code;

    /** 监测类型编码 */
    @Excel(name = "监测类型编码")
    private String monitorType;

    /** 监测类型名称 */
    @Excel(name = "监测类型名称")
    private String monitorTypeName;

    /** 创建人 */
    @Excel(name = "创建人")
    private Long creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private Long modifier;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setModifier(Long modifier) 
    {
        this.modifier = modifier;
    }

    public Long getModifier() 
    {
        return modifier;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("code", getCode())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("creator", getCreator())
            .append("modifier", getModifier())
            .toString();
    }
}
