package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 监测站点对象 sc_monitor_station
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@ToString
public class MonitorStation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 站点编码
     */
    @Excel(name = "站点编码")
    @NotBlank(message = "站点编码不能为空")
    @Size(max = 255, message = "站点编码长度不能超过255个字符")
    private String code;

    /**
     * 站点名称
     */
    @Excel(name = "站点名称")
    @Size(max = 255, message = "站点名称长度不能超过255个字符")
    private String name;

    /**
     * 省份
     */
    @Excel(name = "省份")
    @NotBlank(message = "省份不能为空")
    @Size(max = 255, message = "省份长度不能超过255个字符")
    private String province;

    /**
     * 城市
     */
    @Excel(name = "城市")
    @NotBlank(message = "城市不能为空")
    @Size(max = 255, message = "城市长度不能超过255个字符")
    private String city;

    /**
     * 地区
     */
    @Excel(name = "地区")
    @NotBlank(message = "地区不能为空")
    @Size(max = 255, message = "地区长度不能超过255个字符")
    private String district;

    /**
     * 分公司
     */
    @Excel(name = "分公司")
    private String companyName;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private BigDecimal lat;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private BigDecimal lon;

    /**
     * 开通情况
     */
    @Excel(name = "开通情况")
    private String openingStatus;

    /**
     * 运维分类
     */
    @Excel(name = "运维分类")
    private String maintenanceCategory;

    /**
     * 污水处理工艺
     */
    @Excel(name = "污水处理工艺")
    private String sewageProcess;

    /**
     * 设备型号
     */
    @Excel(name = "设备型号")
    private String equipmentModel;

    /**
     * 设备数量
     */
    @Excel(name = "设备数量")
    @Min(value = 0, message = "设备数量不能小于0")
    private Integer equipmentQuantity;

    /**
     * 处理规模（t/d）
     */
    @Excel(name = "处理规模")
    @DecimalMin(value = "0.0", message = "处理规模不能小于0")
    private BigDecimal processingScale;

    /**
     * 运维类型
     */
    @Excel(name = "运维类型")
    private String maintenanceType;

    /**
     * 污染类型
     */
    @Excel(name = "污染类型")
    private String pollutionType;

    /**
     * 状态
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    private Integer state;

    /**
     * 故障状态
     */
    @Excel(name = "故障状态", readConverterExp = "0=正常,1=故障")
    private Integer faulty;

    /**
     * 站点顺序
     */
    @Excel(name = "站点顺序")
    private Integer stationIndex;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 更新人
     */
    private Long modifier;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    @ApiModelProperty(value = "在线设备数量")
    private Integer onlineDeviceCount = 0;

    @ApiModelProperty(value = "离线设备数量")
    private Integer offlineDeviceCount = 0;

    @Excel(name = "页码")
    private Integer pageNum = 1;

    @Excel(name = "页面大小")
    private Integer pageSize = 10;
}
