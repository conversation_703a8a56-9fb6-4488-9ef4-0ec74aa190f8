package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

/**
 * 设备数据记录对象 (sc_requirement_report, sc_requirement_status, sc_requirement_warning)
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@ToString
@Data
public class RequirementInfoMerge extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 业务标识key */
    @Excel(name = "业务标识key")
    private String key;

    /** 路由 */
    @Excel(name = "路由")
    private String topic;

    /* sc_requirement_status*/


    /** F01(细格栅状态) */
    @Excel(name = "F01(细格栅状态)")
    private String vb100;

    /** P02(砂滤泵) */
    @Excel(name = "P02(砂滤泵)")
    private String vb101;

    /** P03(反洗泵) */
    @Excel(name = "P03(反洗泵)")
    private String vb102;

    /** B01(工艺风机) */
    @Excel(name = "B01(工艺风机)")
    private String vb103;

    /** B03(混合风机) */
    @Excel(name = "B03(混合风机)")
    private String vb104;

    /** SC01(刮泥机) */
    @Excel(name = "SC01(刮泥机)")
    private String vb105;

    /** P01(污泥泵) */
    @Excel(name = "P01(污泥泵)")
    private String vb106;

    /** DP01(碳源泵) */
    @Excel(name = "DP01(碳源泵)")
    private String vb107;

    /** DP02(混凝剂泵) */
    @Excel(name = "DP02(混凝剂泵)")
    private String vb108;

    /** DP03(次钠泵) */
    @Excel(name = "DP03(次钠泵)")
    private String vb109;

    /** C_01(空压机) */
    @Excel(name = "C_01(空压机)")
    private String vb110;

    /** P00A(进水泵A) */
    @Excel(name = "P00A(进水泵A)")
    private String vb111;

    /** P00B(进水泵B) */
    @Excel(name = "P00B(进水泵B)")
    private String vb112;

    /** B02(曝气风机) */
    @Excel(name = "B02(曝气风机)")
    private String vb113;

    /** F00(粗格栅) */
    @Excel(name = "F00(粗格栅)")
    private String vb114;

    /** XV201A(1#膜池混合阀A) */
    @Excel(name = "XV201A(1#膜池混合阀A)")
    private String vb115;

    /** XV201B(1#膜池混合阀B) */
    @Excel(name = "XV201B(1#膜池混合阀B)")
    private String vb116;

    /** XV201C(1#膜池混合阀C) */
    @Excel(name = "XV201C(1#膜池混合阀C)")
    private String vb117;

    /** XV201D(1#膜池混合阀D) */
    @Excel(name = "XV201D(1#膜池混合阀D)")
    private String vb118;

    /** XV202A(2#膜池混合阀A) */
    @Excel(name = "XV202A(2#膜池混合阀A)")
    private String vb119;

    /** XV202B(2#膜池混合阀B) */
    @Excel(name = "XV202B(2#膜池混合阀B)")
    private String vb120;

    /** XV202C(2#膜池混合阀C) */
    @Excel(name = "XV202C(2#膜池混合阀C)")
    private String vb121;

    /** XV202D(2#膜池混合阀D) */
    @Excel(name = "XV202D(2#膜池混合阀D)")
    private String vb122;

    /** XV203(回流阀) */
    @Excel(name = "XV203(回流阀)")
    private String vb123;

    /** XV204(排泥阀) */
    @Excel(name = "XV204(排泥阀)")
    private String vb124;

    /** XV301(砂滤进水阀) */
    @Excel(name = "XV301(砂滤进水阀)")
    private String vb125;

    /** XV302(砂滤进水阀) */
    @Excel(name = "XV302(砂滤进水阀)")
    private String vb126;

    /** XV303(砂滤反洗阀) */
    @Excel(name = "XV303(砂滤反洗阀)")
    private String vb127;

    /** XV304(砂滤反洗阀) */
    @Excel(name = "XV304(砂滤反洗阀)")
    private String vb128;

    /** XV101(细格栅冲洗阀) */
    @Excel(name = "XV101(细格栅冲洗阀)")
    private String vb129;

    /** XV401(储泥池气动阀) */
    @Excel(name = "XV401(储泥池气动阀)")
    private String vb130;

    /** 备用1 */
    @Excel(name = "备用1")
    private String vb131;

    /* sc_requirement_warning*/


    /** 调节池液位低 */
    @Excel(name = "调节池液位低")
    private String v910;

    /** F-01 溢流(细格栅高液位) */
    @Excel(name = "F-01 溢流(细格栅高液位)")
    private String v911;

    /** FT201 流量低(污泥流量计) */
    @Excel(name = "FT201 流量低(污泥流量计)")
    private String v912;

    /** T-02(二级产水池)溢流(二级产水池) */
    @Excel(name = "T-02(二级产水池)溢流(二级产水池)")
    private String v913;

    /** T-03液位过低(三级产水池) */
    @Excel(name = "T-03液位过低(三级产水池)")
    private String v914;

    /** FIT101流量过低 */
    @Excel(name = "FIT101流量过低")
    private String v915;

    /** F01故障(细格栅) */
    @Excel(name = "F01故障(细格栅)")
    private String v920;

    /** P02故障(砂滤进水泵) */
    @Excel(name = "P02故障(砂滤进水泵)")
    private String v921;

    /** P03故障(砂滤反洗泵) */
    @Excel(name = "P03故障(砂滤反洗泵)")
    private String v922;

    /** B01故障(工艺风机) */
    @Excel(name = "B01故障(工艺风机)")
    private String v923;

    /** B03故障(曝气风机) */
    @Excel(name = "B03故障(曝气风机)")
    private String v924;

    /** SC01故障(刮泥机) */
    @Excel(name = "SC01故障(刮泥机)")
    private String v925;

    /** P01故障(污泥泵) */
    @Excel(name = "P01故障(污泥泵)")
    private String v926;

    /** DP01故障(碳源泵) */
    @Excel(name = "DP01故障(碳源泵)")
    private String v927;

    /** DP02故障(混凝剂泵) */
    @Excel(name = "DP02故障(混凝剂泵)")
    private String v930;

    /** DP03故障(次钠泵) */
    @Excel(name = "DP03故障(次钠泵)")
    private String v931;

    /** C01故障(空压机) */
    @Excel(name = "C01故障(空压机)")
    private String v932;

    /** P00A故障(进水泵A) */
    @Excel(name = "P00A故障(进水泵A)")
    private String v933;

    /** P00B故障(进水泵B) */
    @Excel(name = "P00B故障(进水泵B)")
    private String v934;

    /** B02故障(混合风机) */
    @Excel(name = "B02故障(混合风机)")
    private String v935;

    /** F00故障(粗格栅) */
    @Excel(name = "F00故障(粗格栅)")
    private String v936;

    /** 急停故障 */
    @Excel(name = "急停故障")
    private String v937;

    /** LS 401液位报警(储泥池液位报警) */
    @Excel(name = "LS 401液位报警(储泥池液位报警)")
    private String v950;

    /** 液位开关错误(LS301,302位置错误) */
    @Excel(name = "液位开关错误(LS301,302位置错误)")
    private String v951;

    /** 液位开关错误(LS301,303位置错误) */
    @Excel(name = "液位开关错误(LS301,303位置错误)")
    private String v952;

    /** 液位开关错误(LS302,303位置错误) */
    @Excel(name = "液位开关错误(LS302,303位置错误)")
    private String v953;

    /** 反洗提示 */
    @Excel(name = "反洗提示")
    private String v916;

    /** 无气压 */
    @Excel(name = "无气压")
    private String v917;

    /** 碳源药罐液位低 */
    @Excel(name = "碳源药罐液位低")
    private String v954;

    /** 混凝剂药罐液位低 */
    @Excel(name = "混凝剂药罐液位低")
    private String v955;

    /** 消毒药罐液位低 */
    @Excel(name = "消毒药罐液位低")
    private String v956;

    /* sc_requirement_report*/

    /** LS301_H(二级产水池高) */
    @Excel(name = "LS301_H(二级产水池高)")
    private String i200;

    /** LS302_L(二级产水池中) */
    @Excel(name = "LS302_L(二级产水池中)")
    private String i201;

    /** LS303_LL(二级产水池低) */
    @Excel(name = "LS303_LL(二级产水池低)")
    private String i202;

    /** LS304(三级产水池低) */
    @Excel(name = "LS304(三级产水池低)")
    private String i203;

    /** LS001(原水池高) */
    @Excel(name = "LS001(原水池高)")
    private String i204;

    /** LS002_L(原水池中) */
    @Excel(name = "LS002_L(原水池中)")
    private String i205;

    /** LS003_LL(原水池低) */
    @Excel(name = "LS003_LL(原水池低)")
    private String i206;

    /** PSA_601(空压机无气压) */
    @Excel(name = "PSA_601(空压机无气压)")
    private String i210;

    /** LS101_H(细格栅高位) */
    @Excel(name = "LS101_H(细格栅高位)")
    private String i211;

    /** LS501_LL(碳源低液位) */
    @Excel(name = "LS501_LL(碳源低液位)")
    private String i212;

    /** LS502_LL(次钠低液位) */
    @Excel(name = "LS502_LL(次钠低液位)")
    private String i213;

    /** LS503_LL(消毒剂低液位) */
    @Excel(name = "LS503_LL(消毒剂低液位)")
    private String i214;

    /** FIT101(进水流量计) */
    @Excel(name = "FIT101(进水流量计)")
    private String vd200;

    /** FIT201(回流/污泥流量计) */
    @Excel(name = "FIT201(回流/污泥流量计)")
    private String vd230;

    /** DPT301(压差计) */
    @Excel(name = "DPT301(压差计)")
    private String vd260;

    /** TT201(温度计) */
    @Excel(name = "TT201(温度计)")
    private String vd2068;

    /** FT101_Total(FIT101总流量) */
    @Excel(name = "FT101_Total(FIT101总流量)")
    private String vd404;

    /** FT201_Total(FIT201总流量) */
    @Excel(name = "FT201_Total(FIT201总流量)")
    private String vd408;

    /** FIT101PD(FIT101每天数据) */
    @Excel(name = "FIT101PD(FIT101每天数据)")
    private String vd704;

    /** FIT101PM(FIT101每月数据) */
    @Excel(name = "FIT101PM(FIT101每月数据)")
    private String vd712;

    /** FIT101PY(FIT101每年数据) */
    @Excel(name = "FIT101PY(FIT101每年数据)")
    private String vd716;

    /** FIT201RAS_TOTAL(FIT201回流总流量) */
    @Excel(name = "FIT201RAS_TOTAL(FIT201回流总流量)")
    private String vd720;

    /** FIT201RASPD(FIT201回流每天数据) */
    @Excel(name = "FIT201RASPD(FIT201回流每天数据)")
    private String vd728;

    /** FIT201RASPM(FIT201回流每月数据) */
    @Excel(name = "FIT201RASPM(FIT201回流每月数据)")
    private String vd736;

    /** FIT201RASPY(FIT201回流每年数据) */
    @Excel(name = "FIT201RASPY(FIT201回流每年数据)")
    private String vd740;

    /** FIT201WAS_TOTAL(FIT201排泥总流量) */
    @Excel(name = "FIT201WAS_TOTAL(FIT201排泥总流量)")
    private String vd744;

    /** FIT201WASPD(FIT201排泥每天数据) */
    @Excel(name = "FIT201WASPD(FIT201排泥每天数据)")
    private String vd752;

    /** FIT201WASPM(FIT201排泥每月数据) */
    @Excel(name = "FIT201WASPM(FIT201排泥每月数据)")
    private String vd760;

    /** FIT201WASPY(FIT201排泥每年数据) */
    @Excel(name = "FIT201WASPY(FIT201排泥每年数据)")
    private String vd764;

    /** FIT101PD前一(FIT101前一周期每天数据) */
    @Excel(name = "FIT101PD前一(FIT101前一周期每天数据)")
    private String vd904;

    /** FIT101PM前一(FIT101前一周期每月数据) */
    @Excel(name = "FIT101PM前一(FIT101前一周期每月数据)")
    private String vd912;

    /** FIT101PY前一(FIT101前一周期每年数据) */
    @Excel(name = "FIT101PY前一(FIT101前一周期每年数据)")
    private String vd916;

    /** FIT201RASPD前一(FIT201前一周期每天回流数据) */
    @Excel(name = "FIT201RASPD前一(FIT201前一周期每天回流数据)")
    private String vd928;

    /** FIT201RASPM前一(FIT201前一周期每月回流数据) */
    @Excel(name = "FIT201RASPM前一(FIT201前一周期每月回流数据)")
    private String vd936;

    /** FIT201RASPY前一(FIT201前一周期每年回流数据) */
    @Excel(name = "FIT201RASPY前一(FIT201前一周期每年回流数据)")
    private String vd940;

    /** FIT201WASPD前一(FIT201前一周期每天排泥数据) */
    @Excel(name = "FIT201WASPD前一(FIT201前一周期每天排泥数据)")
    private String vd952;

    /** FIT201WASPM前一(FIT201前一周期每月排泥回流数据) */
    @Excel(name = "FIT201WASPM前一(FIT201前一周期每月排泥回流数据)")
    private String vd960;

    /** FIT201WASPY前一(FIT201前一周期每年排泥回流数据) */
    @Excel(name = "FIT201WASPY前一(FIT201前一周期每年排泥回流数据)")
    private String vd964;
}
