package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 基础项目路线信息对象 sc_road
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Road extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 序号 */
    @Excel(name = "序号", cellType = Excel.ColumnType.NUMERIC)
    private Integer code;

    /** 年份 */
    @Excel(name = "年份", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "年份不能为空")
    private Integer year;

    /** 委托单位 */
    @Excel(name = "管理单位", cellType = Excel.ColumnType.STRING)
    @Size(max = 100, message = "委托单位长度不能超过100个字符")
    private String companyName;

    /** 路线编号 */
    @Excel(name = "路线编号", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "路线编号不能为空")
    @Size(max = 60, message = "路线编号长度不能超过60个字符")
    private String roadCode;

    /** 路线名称 */
    @Excel(name = "路线名称", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "路线名称不能为空")
    @Size(max = 60, message = "路线名称长度不能超过60个字符")
    private String roadName;

    /** 起点桩号 */
    @Excel(name = "起点桩号", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "起点桩号不能为空")
    @Size(max = 50, message = "起点桩号长度不能超过50个字符")
    private String startCode;

    /** 迄点桩号 */
    @Excel(name = "止点桩号", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "迄点桩号不能为空")
    @Size(max = 50, message = "迄点桩号长度不能超过50个字符")
    private String endCode;

    /** 里程(km) */
    @Excel(name = "里程", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "里程不能为空")
    private BigDecimal mileage;

    /** 负责人 */
    @Excel(name = "联系人", cellType = Excel.ColumnType.STRING)
    @Size(max = 255, message = "负责人长度不能超过255个字符")
    private String manager;

    /** 负责人电话 */
    @Excel(name = "联系人电话", cellType = Excel.ColumnType.STRING)
    @Size(max = 255, message = "负责人电话长度不能超过255个字符")
    private String managerPhone;

    /** 备注信息 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;

    /** 是否可用：0-否，1-是 */
    private Integer isAvailable;

    /** 是否逻辑删除：0-否，1-是 */
    private Integer isDeleted;

    /** 版本号:默认0,每次更新+1 */
    private Integer versionNo;

    /** 用户id */
    private Long userId;

    /** 部门id */
    private Long deptId;

    /** 路线类型:1.高速,2.国省道 */
    @Excel(name = "路线类型", readConverterExp = "1=高速,2=国省道")
    private Integer type;

    //搜索参数
    private String roadCodeOrName;
} 