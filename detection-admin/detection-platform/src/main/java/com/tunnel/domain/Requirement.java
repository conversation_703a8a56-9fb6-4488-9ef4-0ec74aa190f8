package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

/**
 * 设备状态信息对象 sc_requirement
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@ToString
public class Requirement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String code;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String name;

    /** 在线状态:0.离线,1.在线 */
    @Excel(name = "在线状态", readConverterExp = "0=离线,1=在线")
    private Integer status;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private String type;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private String typeName;

    /** mac地址 */
    @Excel(name = "mac地址")
    private String macAddress;

    /** ip */
    @Excel(name = "ip")
    private String ip;

    /** 路线id */
    @Excel(name = "路线id")
    private Long roadId;

    /** 监测站点ID */
    @Excel(name = "监测站点ID")
    private Long stationId;

    /** 数据来源，mqtt， */
    @Excel(name = "数据来源，mqtt，")
    private String dataFrom;

    /** 消息方式的topic */
    @Excel(name = "消息方式的topic")
    private String topic;

    @Excel(name = "路线")
    private String roadName;
}
