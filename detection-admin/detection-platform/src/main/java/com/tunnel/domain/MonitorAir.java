package com.tunnel.domain;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 空气监测数据对象 sc_monitor_air
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class MonitorAir extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 监测系统编码 */
    private String monitorCode;

    /** 业务标识key */
    private String bKey;

    /** 路由 */
    private String topic;

    /** 温度 */
    private BigDecimal t;

    /** 湿度 */
    private BigDecimal h;

    /** 大气压 */
    private BigDecimal bp;

    /** 风速 */
    private BigDecimal ws;

    /** 风向 */
    private String wd;

    /** 一氧化氮 */
    private BigDecimal no;

    /** 二氧化氮 */
    private BigDecimal no2;

    /** 二氧化硫 */
    private BigDecimal so2;

    /** PM10 */
    private BigDecimal pm10_0;

    /** 一氧化碳 */
    private BigDecimal co;

    /** 氮氧化物 */
    private BigDecimal nox;

    /** 备注 */
    private String remark;
}
