package com.tunnel.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 监测站点VO
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@ApiModel("监测站点VO")
public class MonitorStationVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("站点编码")
    private String code;

    @ApiModelProperty("站点名称")
    private String name;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("地区")
    private String district;

    @ApiModelProperty("分公司")
    private String companyName;

    @ApiModelProperty("经度")
    private BigDecimal lat;

    @ApiModelProperty("纬度")
    private BigDecimal lon;

    @ApiModelProperty("开通情况")
    private String openingStatus;

    @ApiModelProperty("运维分类")
    private String maintenanceCategory;

    @ApiModelProperty("污水处理工艺")
    private String sewageProcess;

    @ApiModelProperty("设备型号")
    private String equipmentModel;

    @ApiModelProperty("设备数量")
    private Integer equipmentQuantity;

    @ApiModelProperty("处理规模（t/d）")
    private BigDecimal processingScale;

    @ApiModelProperty("运维类型")
    private String maintenanceType;

    @ApiModelProperty("污染类型")
    private String pollutionType;

    @ApiModelProperty("状态：0-正常，1-异常")
    private Integer state;

    @ApiModelProperty("状态描述")
    private String stateDesc;

    @ApiModelProperty("故障状态：0-正常，1-故障")
    private Integer faulty;

    @ApiModelProperty("故障状态描述")
    private String faultyDesc;

    @ApiModelProperty("站点顺序")
    private Integer stationIndex;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private Long creator;

    @ApiModelProperty("更新人")
    private Long modifier;

    @ApiModelProperty("系统编码")
    private String systemCode;

    @ApiModelProperty("在线设备数量")
    private Integer onlineDeviceCount;

    @ApiModelProperty("离线设备数量")
    private Integer offlineDeviceCount;

    @ApiModelProperty("设备总数")
    private Integer totalDeviceCount;

    @ApiModelProperty("在线率")
    private String onlineRate;

    /**
     * 获取状态描述
     */
    public String getStateDesc() {
        if (state == null) {
            return "未知";
        }
        return state == 0 ? "正常" : "异常";
    }

    /**
     * 获取故障状态描述
     */
    public String getFaultyDesc() {
        if (faulty == null) {
            return "未知";
        }
        return faulty == 0 ? "正常" : "故障";
    }

    /**
     * 获取设备总数
     */
    public Integer getTotalDeviceCount() {
        int online = onlineDeviceCount != null ? onlineDeviceCount : 0;
        int offline = offlineDeviceCount != null ? offlineDeviceCount : 0;
        return online + offline;
    }

    /**
     * 获取在线率
     */
    public String getOnlineRate() {
        int total = getTotalDeviceCount();
        if (total == 0) {
            return "0%";
        }
        int online = onlineDeviceCount != null ? onlineDeviceCount : 0;
        double rate = (double) online / total * 100;
        return String.format("%.1f%%", rate);
    }
}
