package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

/**
 * 监测站点详细信息对象 monitor_station_relation_info
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ToString
public class MonitorStationRelationInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 站点编码 */
    @Excel(name = "站点编码")
    private String code;

    /** 监测类型 */
    @Excel(name = "监测类型")
    private String monitorType;

    /** 监测类型 */
    @Excel(name = "监测类型名称")
    private String monitorTypeName;

    /** 设计标准,多个之间以‘;’分割 */
    @Excel(name = "设计标准,多个之间以‘;’分割")
    private String designStands;

    /** 环评标准,多个之间以‘;’分割 */
    @Excel(name = "环评标准,多个之间以‘;’分割")
    private String eiaStands;

    /** 图片 */
    @Excel(name = "图片")
    private String pic;

    /** 污染源类型,多个之间以‘;’分割 */
    @Excel(name = "污染源类型,多个之间以‘;’分割")
    private String pollutions;
}
