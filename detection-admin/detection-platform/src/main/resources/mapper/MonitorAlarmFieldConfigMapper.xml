<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorAlarmFieldConfigMapper">
    
    <resultMap type="com.tunnel.domain.MonitorAlarmFieldConfig" id="MonitorAlarmFieldConfigResult">
        <result property="id"    column="id"    />
        <result property="alarmEnabled"    column="alarm_enabled"    />
        <result property="field"    column="field"    />
        <result property="serviceId"    column="service_id"    />
        <result property="mac"    column="mac"    />
        <result property="fieldName"    column="field_name"    />
        <result property="serviceArea"    column="service_area"    />
        <result property="name"    column="name"    />
        <result property="emailAddress"    column="email_address"    />
        <result property="emailEnabled"    column="email_enabled"    />
        <result property="lastTime"    column="last_time"    />
        <result property="messageEnabled"    column="message_enabled"    />
        <result property="phoneNum"    column="phone_num"    />
        <result property="timeInterval"    column="time_interval"    />
        <result property="lastMessageTime"    column="last_message_time"    />
        <result property="messageTimeInterval"    column="message_time_interval"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, alarm_enabled, field, service_id, mac, field_name, service_area, name, email_address, email_enabled, last_time, message_enabled, phone_num, time_interval, last_message_time, message_time_interval, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorAlarmFieldConfigList" parameterType="com.tunnel.domain.MonitorAlarmFieldConfig" resultMap="MonitorAlarmFieldConfigResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        <where>  
            <if test="alarmEnabled != null "> and alarm_enabled = #{alarmEnabled}</if>
            <if test="field != null  and field != ''"> and field = #{field}</if>
            <if test="serviceId != null  and serviceId != ''"> and service_id = #{serviceId}</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="fieldName != null  and fieldName != ''"> and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="serviceArea != null  and serviceArea != ''"> and service_area = #{serviceArea}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="emailAddress != null  and emailAddress != ''"> and email_address = #{emailAddress}</if>
            <if test="emailEnabled != null "> and email_enabled = #{emailEnabled}</if>
            <if test="lastTime != null  and lastTime != ''"> and last_time = #{lastTime}</if>
            <if test="messageEnabled != null "> and message_enabled = #{messageEnabled}</if>
            <if test="phoneNum != null  and phoneNum != ''"> and phone_num = #{phoneNum}</if>
            <if test="timeInterval != null "> and time_interval = #{timeInterval}</if>
            <if test="lastMessageTime != null  and lastMessageTime != ''"> and last_message_time = #{lastMessageTime}</if>
            <if test="messageTimeInterval != null "> and message_time_interval = #{messageTimeInterval}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectMonitorAlarmFieldConfigById" parameterType="Long" resultMap="MonitorAlarmFieldConfigResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertMonitorAlarmFieldConfig" parameterType="com.tunnel.domain.MonitorAlarmFieldConfig" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmEnabled != null">alarm_enabled,</if>
            <if test="field != null">field,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="mac != null">mac,</if>
            <if test="fieldName != null">field_name,</if>
            <if test="serviceArea != null">service_area,</if>
            <if test="name != null">name,</if>
            <if test="emailAddress != null">email_address,</if>
            <if test="emailEnabled != null">email_enabled,</if>
            <if test="lastTime != null">last_time,</if>
            <if test="messageEnabled != null">message_enabled,</if>
            <if test="phoneNum != null">phone_num,</if>
            <if test="timeInterval != null">time_interval,</if>
            <if test="lastMessageTime != null">last_message_time,</if>
            <if test="messageTimeInterval != null">message_time_interval,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmEnabled != null">#{alarmEnabled},</if>
            <if test="field != null">#{field},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="mac != null">#{mac},</if>
            <if test="fieldName != null">#{fieldName},</if>
            <if test="serviceArea != null">#{serviceArea},</if>
            <if test="name != null">#{name},</if>
            <if test="emailAddress != null">#{emailAddress},</if>
            <if test="emailEnabled != null">#{emailEnabled},</if>
            <if test="lastTime != null">#{lastTime},</if>
            <if test="messageEnabled != null">#{messageEnabled},</if>
            <if test="phoneNum != null">#{phoneNum},</if>
            <if test="timeInterval != null">#{timeInterval},</if>
            <if test="lastMessageTime != null">#{lastMessageTime},</if>
            <if test="messageTimeInterval != null">#{messageTimeInterval},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateMonitorAlarmFieldConfig" parameterType="MonitorAlarmFieldConfig">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmEnabled != null">alarm_enabled = #{alarmEnabled},</if>
            <if test="field != null">field = #{field},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="fieldName != null">field_name = #{fieldName},</if>
            <if test="serviceArea != null">service_area = #{serviceArea},</if>
            <if test="name != null">name = #{name},</if>
            <if test="emailAddress != null">email_address = #{emailAddress},</if>
            <if test="emailEnabled != null">email_enabled = #{emailEnabled},</if>
            <if test="lastTime != null">last_time = #{lastTime},</if>
            <if test="messageEnabled != null">message_enabled = #{messageEnabled},</if>
            <if test="phoneNum != null">phone_num = #{phoneNum},</if>
            <if test="timeInterval != null">time_interval = #{timeInterval},</if>
            <if test="lastMessageTime != null">last_message_time = #{lastMessageTime},</if>
            <if test="messageTimeInterval != null">message_time_interval = #{messageTimeInterval},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorAlarmFieldConfigById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteMonitorAlarmFieldConfigByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_alarm_field_config
    </sql>
</mapper>