<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.IntegrationSystemMapper">
    
    <resultMap type="com.tunnel.domain.IntegrationSystem" id="IntegrationSystemResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="systemCode"    column="system_code"    />
<!--        <result property="monitorType"    column="monitor_type"    />-->
<!--        <result property="monitorTypeName"    column="monitor_type_name"    />-->
        <result property="name"    column="name"    />
        <result property="monitorStationCode"    column="monitor_station_code"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, type, system_code, name, monitor_station_code, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectIntegrationSystemList" parameterType="com.tunnel.domain.IntegrationSystem" resultMap="IntegrationSystemResult">
        select
        <include refid="allColumn"/>
        from <include refid="tableName"/>
        <where>  
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
<!--            <if test="monitorType != null  and monitorType != ''"> and monitor_type = #{monitorType}</if>-->
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="monitorStationCode != null  and monitorStationCode != ''"> and monitor_station_code = #{monitorStationCode}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectIntegrationSystemById" parameterType="Long" resultMap="IntegrationSystemResult">
        select <include refid="allColumn"/>
        from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertIntegrationSystem" parameterType="com.tunnel.domain.IntegrationSystem" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">type,</if>
            <if test="systemCode != null and systemCode != ''">system_code,</if>
<!--            <if test="monitorType != null and monitorType != ''">monitor_type,</if>-->
<!--            <if test="monitorTypeName != null and monitorTypeName != ''">monitor_type_name,</if>-->
            <if test="name != null">name,</if>
            <if test="monitorStationCode != null and monitorStationCode != ''">monitor_station_code,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">#{type},</if>
            <if test="systemCode != null and systemCode != ''">#{systemCode},</if>
<!--            <if test="monitorType != null and monitorType != ''">#{monitorType},</if>-->
<!--            <if test="monitorTypeName != null and monitorTypeName != ''">#{monitorTypeName},</if>-->
            <if test="name != null">#{name},</if>
            <if test="monitorStationCode != null and monitorStationCode != ''">#{monitorStationCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateIntegrationSystem" parameterType="com.tunnel.domain.IntegrationSystem">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="systemCode != null and systemCode != ''">system_code = #{systemCode},</if>
<!--            <if test="monitorType != null and monitorType != ''">monitor_type = #{monitorType},</if>-->
            <if test="name != null">name = #{name},</if>
            <if test="monitorStationCode != null and monitorStationCode != ''">monitor_station_code = #{monitorStationCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIntegrationSystemById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteIntegrationSystemByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_integration_system
    </sql>
</mapper>