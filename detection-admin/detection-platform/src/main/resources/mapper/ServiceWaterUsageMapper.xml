<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceWaterUsageMapper">
    
    <resultMap type="ServiceWaterUsage" id="ServiceWaterUsageResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="waterSource"    column="water_source"    />
        <result property="dailyUsage"    column="daily_usage"    />
        <result property="peakUsage"    column="peak_usage"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceWaterUsageVo">
        select id, service_area_id, water_source, daily_usage, peak_usage, remark, create_time, update_time, creator, modifier from sc_service_water_usage
    </sql>

    <select id="selectServiceWaterUsageList" parameterType="ServiceWaterUsage" resultMap="ServiceWaterUsageResult">
        <include refid="selectServiceWaterUsageVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="waterSource != null  and waterSource != ''"> and water_source like concat('%', #{waterSource}, '%')</if>
        </where>
    </select>
    
    <select id="selectServiceWaterUsageById" parameterType="Long" resultMap="ServiceWaterUsageResult">
        <include refid="selectServiceWaterUsageVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceWaterUsageByServiceAreaId" parameterType="Long" resultMap="ServiceWaterUsageResult">
        <include refid="selectServiceWaterUsageVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceWaterUsage" parameterType="ServiceWaterUsage" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_water_usage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="waterSource != null">water_source,</if>
            <if test="dailyUsage != null">daily_usage,</if>
            <if test="peakUsage != null">peak_usage,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="waterSource != null">#{waterSource},</if>
            <if test="dailyUsage != null">#{dailyUsage},</if>
            <if test="peakUsage != null">#{peakUsage},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceWaterUsage" parameterType="ServiceWaterUsage">
        update sc_service_water_usage
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="waterSource != null">water_source = #{waterSource},</if>
            <if test="dailyUsage != null">daily_usage = #{dailyUsage},</if>
            <if test="peakUsage != null">peak_usage = #{peakUsage},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceWaterUsageById" parameterType="Long">
        delete from sc_service_water_usage where id = #{id}
    </delete>

    <delete id="deleteServiceWaterUsageByIds" parameterType="String">
        delete from sc_service_water_usage where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceWaterUsageByServiceAreaId" parameterType="Long">
        delete from sc_service_water_usage where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
