<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceBiochemicalSystemInfoMapper">
    
    <resultMap type="ServiceBiochemicalSystemInfo" id="ServiceBiochemicalSystemInfoResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="areaType"    column="area_type"    />
        <result property="fillerType"    column="filler_type"    />
        <result property="fillerStatus"    column="filler_status"    />
        <result property="processParameters"    column="process_parameters"    />
        <result property="sludgeConcentration"    column="sludge_concentration"    />
        <result property="nitrificationReflux"    column="nitrification_reflux"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceBiochemicalSystemInfoVo">
        select id, service_area_id, area_type, filler_type, filler_status, process_parameters, sludge_concentration, nitrification_reflux, remark, create_time, update_time, creator, modifier from sc_service_biochemical_system_info
    </sql>

    <select id="selectServiceBiochemicalSystemInfoList" parameterType="ServiceBiochemicalSystemInfo" resultMap="ServiceBiochemicalSystemInfoResult">
        <include refid="selectServiceBiochemicalSystemInfoVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="fillerType != null  and fillerType != ''"> and filler_type like concat('%', #{fillerType}, '%')</if>
            <if test="fillerStatus != null  and fillerStatus != ''"> and filler_status like concat('%', #{fillerStatus}, '%')</if>
        </where>
    </select>
    
    <select id="selectServiceBiochemicalSystemInfoById" parameterType="Long" resultMap="ServiceBiochemicalSystemInfoResult">
        <include refid="selectServiceBiochemicalSystemInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceBiochemicalSystemInfoByServiceAreaId" parameterType="Long" resultMap="ServiceBiochemicalSystemInfoResult">
        <include refid="selectServiceBiochemicalSystemInfoVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceBiochemicalSystemInfo" parameterType="ServiceBiochemicalSystemInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_biochemical_system_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="fillerType != null">filler_type,</if>
            <if test="fillerStatus != null">filler_status,</if>
            <if test="processParameters != null">process_parameters,</if>
            <if test="sludgeConcentration != null">sludge_concentration,</if>
            <if test="nitrificationReflux != null">nitrification_reflux,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="fillerType != null">#{fillerType},</if>
            <if test="fillerStatus != null">#{fillerStatus},</if>
            <if test="processParameters != null">#{processParameters},</if>
            <if test="sludgeConcentration != null">#{sludgeConcentration},</if>
            <if test="nitrificationReflux != null">#{nitrificationReflux},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceBiochemicalSystemInfo" parameterType="ServiceBiochemicalSystemInfo">
        update sc_service_biochemical_system_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="fillerType != null">filler_type = #{fillerType},</if>
            <if test="fillerStatus != null">filler_status = #{fillerStatus},</if>
            <if test="processParameters != null">process_parameters = #{processParameters},</if>
            <if test="sludgeConcentration != null">sludge_concentration = #{sludgeConcentration},</if>
            <if test="nitrificationReflux != null">nitrification_reflux = #{nitrificationReflux},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceBiochemicalSystemInfoById" parameterType="Long">
        delete from sc_service_biochemical_system_info where id = #{id}
    </delete>

    <delete id="deleteServiceBiochemicalSystemInfoByIds" parameterType="String">
        delete from sc_service_biochemical_system_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceBiochemicalSystemInfoByServiceAreaId" parameterType="Long">
        delete from sc_service_biochemical_system_info where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
