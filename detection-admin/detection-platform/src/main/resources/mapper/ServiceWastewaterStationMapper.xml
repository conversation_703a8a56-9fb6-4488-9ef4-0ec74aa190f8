<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceWastewaterStationMapper">
    
    <resultMap type="ServiceWastewaterStation" id="ServiceWastewaterStationResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="areaType"    column="area_type"    />
        <result property="treatmentProcess"    column="treatment_process"    />
        <result property="designCapacity"    column="design_capacity"    />
        <result property="structureMaterial"    column="structure_material"    />
        <result property="constructionDate"    column="construction_date"    />
        <result property="operationStatus"    column="operation_status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceWastewaterStationVo">
        select id, service_area_id, area_type, treatment_process, design_capacity, structure_material, construction_date, operation_status, remark, create_time, update_time, creator, modifier from sc_service_wastewater_station
    </sql>

    <select id="selectServiceWastewaterStationList" parameterType="ServiceWastewaterStation" resultMap="ServiceWastewaterStationResult">
        <include refid="selectServiceWastewaterStationVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="treatmentProcess != null  and treatmentProcess != ''"> and treatment_process like concat('%', #{treatmentProcess}, '%')</if>
            <if test="structureMaterial != null  and structureMaterial != ''"> and structure_material like concat('%', #{structureMaterial}, '%')</if>
            <if test="operationStatus != null  and operationStatus != ''"> and operation_status = #{operationStatus}</if>
        </where>
    </select>
    
    <select id="selectServiceWastewaterStationById" parameterType="Long" resultMap="ServiceWastewaterStationResult">
        <include refid="selectServiceWastewaterStationVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceWastewaterStationByServiceAreaId" parameterType="Long" resultMap="ServiceWastewaterStationResult">
        <include refid="selectServiceWastewaterStationVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceWastewaterStation" parameterType="ServiceWastewaterStation" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_wastewater_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="treatmentProcess != null">treatment_process,</if>
            <if test="designCapacity != null">design_capacity,</if>
            <if test="structureMaterial != null">structure_material,</if>
            <if test="constructionDate != null">construction_date,</if>
            <if test="operationStatus != null">operation_status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="treatmentProcess != null">#{treatmentProcess},</if>
            <if test="designCapacity != null">#{designCapacity},</if>
            <if test="structureMaterial != null">#{structureMaterial},</if>
            <if test="constructionDate != null">#{constructionDate},</if>
            <if test="operationStatus != null">#{operationStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceWastewaterStation" parameterType="ServiceWastewaterStation">
        update sc_service_wastewater_station
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="treatmentProcess != null">treatment_process = #{treatmentProcess},</if>
            <if test="designCapacity != null">design_capacity = #{designCapacity},</if>
            <if test="structureMaterial != null">structure_material = #{structureMaterial},</if>
            <if test="constructionDate != null">construction_date = #{constructionDate},</if>
            <if test="operationStatus != null">operation_status = #{operationStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceWastewaterStationById" parameterType="Long">
        delete from sc_service_wastewater_station where id = #{id}
    </delete>

    <delete id="deleteServiceWastewaterStationByIds" parameterType="String">
        delete from sc_service_wastewater_station where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceWastewaterStationByServiceAreaId" parameterType="Long">
        delete from sc_service_wastewater_station where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
