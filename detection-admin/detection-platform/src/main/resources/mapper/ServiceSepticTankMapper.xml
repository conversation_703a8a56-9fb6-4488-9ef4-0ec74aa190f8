<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceSepticTankMapper">
    
    <resultMap type="ServiceSepticTank" id="ServiceSepticTankResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="areaType"    column="area_type"    />
        <result property="type"    column="type"    />
        <result property="sizeVolume"    column="size_volume"    />
        <result property="material"    column="material"    />
        <result property="siltationStatus"    column="siltation_status"    />
        <result property="wastewaterAccess"    column="wastewater_access"    />
        <result property="otherConditions"    column="other_conditions"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceSepticTankVo">
        select id, service_area_id, area_type, type, size_volume, material, siltation_status, wastewater_access, other_conditions, remark, create_time, update_time, creator, modifier from sc_service_septic_tank
    </sql>

    <select id="selectServiceSepticTankList" parameterType="ServiceSepticTank" resultMap="ServiceSepticTankResult">
        <include refid="selectServiceSepticTankVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="material != null  and material != ''"> and material like concat('%', #{material}, '%')</if>
            <if test="siltationStatus != null  and siltationStatus != ''"> and siltation_status like concat('%', #{siltationStatus}, '%')</if>
        </where>
    </select>
    
    <select id="selectServiceSepticTankById" parameterType="Long" resultMap="ServiceSepticTankResult">
        <include refid="selectServiceSepticTankVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceSepticTankByServiceAreaId" parameterType="Long" resultMap="ServiceSepticTankResult">
        <include refid="selectServiceSepticTankVo"/>
        where service_area_id = #{serviceAreaId}
    </select>

    <select id="selectServiceSepticTankByServiceAreaIdAndType" resultMap="ServiceSepticTankResult">
        <include refid="selectServiceSepticTankVo"/>
        where service_area_id = #{serviceAreaId} and type = #{type}
    </select>
        
    <insert id="insertServiceSepticTank" parameterType="ServiceSepticTank" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_septic_tank
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="sizeVolume != null">size_volume,</if>
            <if test="material != null">material,</if>
            <if test="siltationStatus != null">siltation_status,</if>
            <if test="wastewaterAccess != null">wastewater_access,</if>
            <if test="otherConditions != null">other_conditions,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="sizeVolume != null">#{sizeVolume},</if>
            <if test="material != null">#{material},</if>
            <if test="siltationStatus != null">#{siltationStatus},</if>
            <if test="wastewaterAccess != null">#{wastewaterAccess},</if>
            <if test="otherConditions != null">#{otherConditions},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceSepticTank" parameterType="ServiceSepticTank">
        update sc_service_septic_tank
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="sizeVolume != null">size_volume = #{sizeVolume},</if>
            <if test="material != null">material = #{material},</if>
            <if test="siltationStatus != null">siltation_status = #{siltationStatus},</if>
            <if test="wastewaterAccess != null">wastewater_access = #{wastewaterAccess},</if>
            <if test="otherConditions != null">other_conditions = #{otherConditions},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceSepticTankById" parameterType="Long">
        delete from sc_service_septic_tank where id = #{id}
    </delete>

    <delete id="deleteServiceSepticTankByIds" parameterType="String">
        delete from sc_service_septic_tank where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceSepticTankByServiceAreaId" parameterType="Long">
        delete from sc_service_septic_tank where service_area_id = #{serviceAreaId}
    </delete>

    <delete id="deleteServiceSepticTankByServiceAreaIdAndType">
        delete from sc_service_septic_tank where service_area_id = #{serviceAreaId} and type = #{type}
    </delete>
</mapper>
