<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RequirementInfoMergeMapper">
    
    <resultMap type="com.tunnel.domain.RequirementInfoMerge" id="RequirementInfoMergeResult">
        <result property="key"    column="b_key"    />
        <result property="topic"  column="topic"    />
        <result property="i200"    column="i20_0"    />
        <result property="i201"    column="i20_1"    />
        <result property="i202"    column="i20_2"    />
        <result property="i203"    column="i20_3"    />
        <result property="i204"    column="i20_4"    />
        <result property="i205"    column="i20_5"    />
        <result property="i206"    column="i20_6"    />
        <result property="i210"    column="i21_0"    />
        <result property="i211"    column="i21_1"    />
        <result property="i212"    column="i21_2"    />
        <result property="i213"    column="i21_3"    />
        <result property="i214"    column="i21_4"    />
        <result property="vd200"    column="vd200"    />
        <result property="vd230"    column="vd230"    />
        <result property="vd260"    column="vd260"    />
        <result property="vd2068"    column="vd2068"    />
        <result property="vd404"    column="vd404"    />
        <result property="vd408"    column="vd408"    />
        <result property="vd704"    column="vd704"    />
        <result property="vd712"    column="vd712"    />
        <result property="vd716"    column="vd716"    />
        <result property="vd720"    column="vd720"    />
        <result property="vd728"    column="vd728"    />
        <result property="vd736"    column="vd736"    />
        <result property="vd740"    column="vd740"    />
        <result property="vd744"    column="vd744"    />
        <result property="vd752"    column="vd752"    />
        <result property="vd760"    column="vd760"    />
        <result property="vd764"    column="vd764"    />
        <result property="vd904"    column="vd904"    />
        <result property="vd912"    column="vd912"    />
        <result property="vd916"    column="vd916"    />
        <result property="vd928"    column="vd928"    />
        <result property="vd936"    column="vd936"    />
        <result property="vd940"    column="vd940"    />
        <result property="vd952"    column="vd952"    />
        <result property="vd960"    column="vd960"    />
        <result property="vd964"    column="vd964"    />

        <result property="vb100"    column="vb100"    />
        <result property="vb101"    column="vb101"    />
        <result property="vb102"    column="vb102"    />
        <result property="vb103"    column="vb103"    />
        <result property="vb104"    column="vb104"    />
        <result property="vb105"    column="vb105"    />
        <result property="vb106"    column="vb106"    />
        <result property="vb107"    column="vb107"    />
        <result property="vb108"    column="vb108"    />
        <result property="vb109"    column="vb109"    />
        <result property="vb110"    column="vb110"    />
        <result property="vb111"    column="vb111"    />
        <result property="vb112"    column="vb112"    />
        <result property="vb113"    column="vb113"    />
        <result property="vb114"    column="vb114"    />
        <result property="vb115"    column="vb115"    />
        <result property="vb116"    column="vb116"    />
        <result property="vb117"    column="vb117"    />
        <result property="vb118"    column="vb118"    />
        <result property="vb119"    column="vb119"    />
        <result property="vb120"    column="vb120"    />
        <result property="vb121"    column="vb121"    />
        <result property="vb122"    column="vb122"    />
        <result property="vb123"    column="vb123"    />
        <result property="vb124"    column="vb124"    />
        <result property="vb125"    column="vb125"    />
        <result property="vb126"    column="vb126"    />
        <result property="vb127"    column="vb127"    />
        <result property="vb128"    column="vb128"    />
        <result property="vb129"    column="vb129"    />
        <result property="vb130"    column="vb130"    />
        <result property="vb131"    column="vb131"    />

        <result property="v910"    column="v91_0"    />
        <result property="v911"    column="v91_1"    />
        <result property="v912"    column="v91_2"    />
        <result property="v913"    column="v91_3"    />
        <result property="v914"    column="v91_4"    />
        <result property="v915"    column="v91_5"    />
        <result property="v920"    column="v92_0"    />
        <result property="v921"    column="v92_1"    />
        <result property="v922"    column="v92_2"    />
        <result property="v923"    column="v92_3"    />
        <result property="v924"    column="v92_4"    />
        <result property="v925"    column="v92_5"    />
        <result property="v926"    column="v92_6"    />
        <result property="v927"    column="v92_7"    />
        <result property="v930"    column="v93_0"    />
        <result property="v931"    column="v93_1"    />
        <result property="v932"    column="v93_2"    />
        <result property="v933"    column="v93_3"    />
        <result property="v934"    column="v93_4"    />
        <result property="v935"    column="v93_5"    />
        <result property="v936"    column="v93_6"    />
        <result property="v937"    column="v93_7"    />
        <result property="v950"    column="v95_0"    />
        <result property="v951"    column="v95_1"    />
        <result property="v952"    column="v95_2"    />
        <result property="v953"    column="v95_3"    />
        <result property="v916"    column="v91_6"    />
        <result property="v917"    column="v91_7"    />
        <result property="v954"    column="v95_4"    />
        <result property="v955"    column="v95_5"    />
        <result property="v956"    column="v95_6"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectRequirementVo">
        srr.b_key, srr.topic, srr.i20_0, srr.i20_1, srr.i20_2, srr.i20_3, srr.i20_4, srr.i20_5, srr.i20_6, srr.i21_0, srr.i21_1, srr.i21_2, srr.i21_3, srr.i21_4, srr.vd200, srr.vd230, srr.vd260, srr.vd2068, srr.vd404, srr.vd408, srr.vd704, srr.vd712, srr.vd716, srr.vd720, srr.vd728, srr.vd736, srr.vd740, srr.vd744, srr.vd752, srr.vd760, srr.vd764, srr.vd904, srr.vd912, srr.vd916, srr.vd928, srr.vd936, srr.vd940, srr.vd952, srr.vd960, srr.vd964,
        srs.vb100, srs.vb101, srs.vb102, srs.vb103, srs.vb104, srs.vb105, srs.vb106, srs.vb107, srs.vb108, srs.vb109, srs.vb110, srs.vb111, srs.vb112, srs.vb113, srs.vb114, srs.vb115, srs.vb116, srs.vb117, srs.vb118, srs.vb119, srs.vb120, srs.vb121, srs.vb122, srs.vb123, srs.vb124, srs.vb125, srs.vb126, srs.vb127, srs.vb128, srs.vb129, srs.vb130, srs.vb131,
        srw.v91_0, srw.v91_1, srw.v91_2, srw.v91_3, srw.v91_4, srw.v91_5, srw.v92_0, srw.v92_1, srw.v92_2, srw.v92_3, srw.v92_4, srw.v92_5, srw.v92_6, srw.v92_7, srw.v93_0, srw.v93_1, srw.v93_2, srw.v93_3, srw.v93_4, srw.v93_5, srw.v93_6, srw.v93_7, srw.v95_0, srw.v95_1, srw.v95_2, srw.v95_3, srw.v91_6, srw.v91_7, srw.v95_4, srw.v95_5, srw.v95_6,
        srr.remark, srr.create_time, srr.update_time, srr.creator, srr.modifier
    </sql>

    <select id="selectRequirementList" parameterType="com.tunnel.domain.RequirementInfoMerge" resultMap="RequirementInfoMergeResult">
        select <include refid="selectRequirementVo"/>
            from sc_requirement_report srr
            inner join sc_requirement_status srs on srr.b_key = srs.b_key
            inner join sc_requirement_warning srw on srr.b_key = srw.b_key
        <where>
            <if test="key != null  and key != ''"> and srr.b_key = #{key}</if>
            <if test="i200 != null  and i200 != ''"> and srr.i20_0 = #{i200}</if>
            <if test="i201 != null  and i201 != ''"> and srr.i20_1 = #{i201}</if>
            <if test="i202 != null  and i202 != ''"> and srr.i20_2 = #{i202}</if>
            <if test="i203 != null  and i203 != ''"> and srr.i20_3 = #{i203}</if>
            <if test="i204 != null  and i204 != ''"> and srr.i20_4 = #{i204}</if>
            <if test="i205 != null  and i205 != ''"> and srr.i20_5 = #{i205}</if>
            <if test="i206 != null  and i206 != ''"> and srr.i20_6 = #{i206}</if>
            <if test="i210 != null  and i210 != ''"> and srr.i21_0 = #{i210}</if>
            <if test="i211 != null  and i211 != ''"> and srr.i21_1 = #{i211}</if>
            <if test="i212 != null  and i212 != ''"> and srr.i21_2 = #{i212}</if>
            <if test="i213 != null  and i213 != ''"> and srr.i21_3 = #{i213}</if>
            <if test="i214 != null  and i214 != ''"> and srr.i21_4 = #{i214}</if>
            <if test="vd200 != null  and vd200 != ''"> and srr.vd200 = #{vd200}</if>
            <if test="vd230 != null  and vd230 != ''"> and srr.vd230 = #{vd230}</if>
            <if test="vd260 != null  and vd260 != ''"> and srr.vd260 = #{vd260}</if>
            <if test="vd2068 != null  and vd2068 != ''"> and srr.vd2068 = #{vd2068}</if>
            <if test="vd404 != null  and vd404 != ''"> and srr.vd404 = #{vd404}</if>
            <if test="vd408 != null  and vd408 != ''"> and srr.vd408 = #{vd408}</if>
            <if test="vd704 != null  and vd704 != ''"> and srr.vd704 = #{vd704}</if>
            <if test="vd712 != null  and vd712 != ''"> and srr.vd712 = #{vd712}</if>
            <if test="vd716 != null  and vd716 != ''"> and srr.vd716 = #{vd716}</if>
            <if test="vd720 != null  and vd720 != ''"> and srr.vd720 = #{vd720}</if>
            <if test="vd728 != null  and vd728 != ''"> and srr.vd728 = #{vd728}</if>
            <if test="vd736 != null  and vd736 != ''"> and srr.vd736 = #{vd736}</if>
            <if test="vd740 != null  and vd740 != ''"> and srr.vd740 = #{vd740}</if>
            <if test="vd744 != null  and vd744 != ''"> and srr.vd744 = #{vd744}</if>
            <if test="vd752 != null  and vd752 != ''"> and srr.vd752 = #{vd752}</if>
            <if test="vd760 != null  and vd760 != ''"> and srr.vd760 = #{vd760}</if>
            <if test="vd764 != null  and vd764 != ''"> and srr.vd764 = #{vd764}</if>
            <if test="vd904 != null  and vd904 != ''"> and srr.vd904 = #{vd904}</if>
            <if test="vd912 != null  and vd912 != ''"> and srr.vd912 = #{vd912}</if>
            <if test="vd916 != null  and vd916 != ''"> and srr.vd916 = #{vd916}</if>
            <if test="vd928 != null  and vd928 != ''"> and srr.vd928 = #{vd928}</if>
            <if test="vd936 != null  and vd936 != ''"> and srr.vd936 = #{vd936}</if>
            <if test="vd940 != null  and vd940 != ''"> and srr.vd940 = #{vd940}</if>
            <if test="vd952 != null  and vd952 != ''"> and srr.vd952 = #{vd952}</if>
            <if test="vd960 != null  and vd960 != ''"> and srr.vd960 = #{vd960}</if>
            <if test="vd964 != null  and vd964 != ''"> and srr.vd964 = #{vd964}</if>
            <if test="vb100 != null  and vb100 != ''"> and srs.vb100 = #{vb100}</if>
            <if test="vb101 != null  and vb101 != ''"> and srs.vb101 = #{vb101}</if>
            <if test="vb102 != null  and vb102 != ''"> and srs.vb102 = #{vb102}</if>
            <if test="vb103 != null  and vb103 != ''"> and srs.vb103 = #{vb103}</if>
            <if test="vb104 != null  and vb104 != ''"> and srs.vb104 = #{vb104}</if>
            <if test="vb105 != null  and vb105 != ''"> and srs.vb105 = #{vb105}</if>
            <if test="vb106 != null  and vb106 != ''"> and srs.vb106 = #{vb106}</if>
            <if test="vb107 != null  and vb107 != ''"> and srs.vb107 = #{vb107}</if>
            <if test="vb108 != null  and vb108 != ''"> and srs.vb108 = #{vb108}</if>
            <if test="vb109 != null  and vb109 != ''"> and srs.vb109 = #{vb109}</if>
            <if test="vb110 != null  and vb110 != ''"> and srs.vb110 = #{vb110}</if>
            <if test="vb111 != null  and vb111 != ''"> and srs.vb111 = #{vb111}</if>
            <if test="vb112 != null  and vb112 != ''"> and srs.vb112 = #{vb112}</if>
            <if test="vb113 != null  and vb113 != ''"> and srs.vb113 = #{vb113}</if>
            <if test="vb114 != null  and vb114 != ''"> and srs.vb114 = #{vb114}</if>
            <if test="vb115 != null  and vb115 != ''"> and srs.vb115 = #{vb115}</if>
            <if test="vb116 != null  and vb116 != ''"> and srs.vb116 = #{vb116}</if>
            <if test="vb117 != null  and vb117 != ''"> and srs.vb117 = #{vb117}</if>
            <if test="vb118 != null  and vb118 != ''"> and srs.vb118 = #{vb118}</if>
            <if test="vb119 != null  and vb119 != ''"> and srs.vb119 = #{vb119}</if>
            <if test="vb120 != null  and vb120 != ''"> and srs.vb120 = #{vb120}</if>
            <if test="vb121 != null  and vb121 != ''"> and srs.vb121 = #{vb121}</if>
            <if test="vb122 != null  and vb122 != ''"> and srs.vb122 = #{vb122}</if>
            <if test="vb123 != null  and vb123 != ''"> and srs.vb123 = #{vb123}</if>
            <if test="vb124 != null  and vb124 != ''"> and srs.vb124 = #{vb124}</if>
            <if test="vb125 != null  and vb125 != ''"> and srs.vb125 = #{vb125}</if>
            <if test="vb126 != null  and vb126 != ''"> and srs.vb126 = #{vb126}</if>
            <if test="vb127 != null  and vb127 != ''"> and srs.vb127 = #{vb127}</if>
            <if test="vb128 != null  and vb128 != ''"> and srs.vb128 = #{vb128}</if>
            <if test="vb129 != null  and vb129 != ''"> and srs.vb129 = #{vb129}</if>
            <if test="vb130 != null  and vb130 != ''"> and srs.vb130 = #{vb130}</if>
            <if test="vb131 != null  and vb131 != ''"> and srs.vb131 = #{vb131}</if>
            <if test="v910 != null  and v910 != ''"> and srw.v91_0 = #{v910}</if>
            <if test="v911 != null  and v911 != ''"> and srw.v91_1 = #{v911}</if>
            <if test="v912 != null  and v912 != ''"> and srw.v91_2 = #{v912}</if>
            <if test="v913 != null  and v913 != ''"> and srw.v91_3 = #{v913}</if>
            <if test="v914 != null  and v914 != ''"> and srw.v91_4 = #{v914}</if>
            <if test="v915 != null  and v915 != ''"> and srw.v91_5 = #{v915}</if>
            <if test="v920 != null  and v920 != ''"> and srw.v92_0 = #{v920}</if>
            <if test="v921 != null  and v921 != ''"> and srw.v92_1 = #{v921}</if>
            <if test="v922 != null  and v922 != ''"> and srw.v92_2 = #{v922}</if>
            <if test="v923 != null  and v923 != ''"> and srw.v92_3 = #{v923}</if>
            <if test="v924 != null  and v924 != ''"> and srw.v92_4 = #{v924}</if>
            <if test="v925 != null  and v925 != ''"> and srw.v92_5 = #{v925}</if>
            <if test="v926 != null  and v926 != ''"> and srw.v92_6 = #{v926}</if>
            <if test="v927 != null  and v927 != ''"> and srw.v92_7 = #{v927}</if>
            <if test="v930 != null  and v930 != ''"> and srw.v93_0 = #{v930}</if>
            <if test="v931 != null  and v931 != ''"> and srw.v93_1 = #{v931}</if>
            <if test="v932 != null  and v932 != ''"> and srw.v93_2 = #{v932}</if>
            <if test="v933 != null  and v933 != ''"> and srw.v93_3 = #{v933}</if>
            <if test="v934 != null  and v934 != ''"> and srw.v93_4 = #{v934}</if>
            <if test="v935 != null  and v935 != ''"> and srw.v93_5 = #{v935}</if>
            <if test="v936 != null  and v936 != ''"> and srw.v93_6 = #{v936}</if>
            <if test="v937 != null  and v937 != ''"> and srw.v93_7 = #{v937}</if>
            <if test="v950 != null  and v950 != ''"> and srw.v95_0 = #{v950}</if>
            <if test="v951 != null  and v951 != ''"> and srw.v95_1 = #{v951}</if>
            <if test="v952 != null  and v952 != ''"> and srw.v95_2 = #{v952}</if>
            <if test="v953 != null  and v953 != ''"> and srw.v95_3 = #{v953}</if>
            <if test="v916 != null  and v916 != ''"> and srw.v91_6 = #{v916}</if>
            <if test="v917 != null  and v917 != ''"> and srw.v91_7 = #{v917}</if>
            <if test="v954 != null  and v954 != ''"> and srw.v95_4 = #{v954}</if>
            <if test="v955 != null  and v955 != ''"> and srw.v95_5 = #{v955}</if>
            <if test="v956 != null  and v956 != ''"> and srw.v95_6 = #{v956}</if>
        </where>
        order by srs.id desc
    </select>

    <select id="selectRequirementMap" resultType="java.util.Map">
        select
            <choose>
                <when test="field != null and field != ''">
                    ${field}
                </when>
                <otherwise>*</otherwise>
            </choose>
        from sc_requirement_report srr
        inner join sc_requirement_status srs on srr.b_key = srs.b_key
        inner join sc_requirement_warning srw on srr.b_key = srw.b_key
        <where>
            <if test="startTime != null"> and srr.create_time &gt;= #{startTime}</if>
            <if test="endTime != null"> and srr.create_time &lt;= #{endTime}</if>
        </where>
    </select>
</mapper>