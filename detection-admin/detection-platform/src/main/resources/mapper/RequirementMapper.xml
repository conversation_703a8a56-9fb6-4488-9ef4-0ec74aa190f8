<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RequirementMapper">
    
    <resultMap type="com.tunnel.domain.Requirement" id="ScRequirementResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="typeName"    column="type_name"    />
        <result property="macAddress"    column="mac_address"    />
        <result property="ip"    column="ip"    />
        <result property="roadId"    column="road_id"    />
        <result property="stationId"    column="station_id"    />
        <result property="dataFrom"    column="data_from"    />
        <result property="topic"    column="topic"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectScRequirementVo">
        select id, code, name, status, type, type_name, mac_address, ip, road_id, station_id, data_from, topic, remark, create_time, update_time, creator, modifier from sc_requirement
    </sql>

    <select id="selectScRequirementList" parameterType="com.tunnel.domain.Requirement" resultMap="ScRequirementResult">
        <include refid="selectScRequirementVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="macAddress != null  and macAddress != ''"> and mac_address = #{macAddress}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="roadId != null "> and road_id = #{roadId}</if>
            <if test="stationId != null "> and station_id = #{stationId}</if>
            <if test="dataFrom != null  and dataFrom != ''"> and data_from = #{dataFrom}</if>
            <if test="topic != null  and topic != ''"> and topic = #{topic}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectScRequirementById" parameterType="Long" resultMap="ScRequirementResult">
        <include refid="selectScRequirementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertScRequirement" parameterType="com.tunnel.domain.Requirement" useGeneratedKeys="true" keyProperty="id">
        insert into sc_requirement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="typeName != null">type_name,</if>
            <if test="macAddress != null">mac_address,</if>
            <if test="ip != null">ip,</if>
            <if test="roadId != null">road_id,</if>
            <if test="stationId != null">station_id,</if>
            <if test="dataFrom != null">data_from,</if>
            <if test="topic != null">topic,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="typeName != null">#{typeName},</if>
            <if test="macAddress != null">#{macAddress},</if>
            <if test="ip != null">#{ip},</if>
            <if test="roadId != null">#{roadId},</if>
            <if test="stationId != null">#{stationId},</if>
            <if test="dataFrom != null">#{dataFrom},</if>
            <if test="topic != null">#{topic},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateScRequirement" parameterType="com.tunnel.domain.Requirement">
        update sc_requirement
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="typeName != null">type_name = #{typeName},</if>
            <if test="macAddress != null">mac_address = #{macAddress},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="roadId != null">road_id = #{roadId},</if>
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="dataFrom != null">data_from = #{dataFrom},</if>
            <if test="topic != null">topic = #{topic},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScRequirementById" parameterType="String">
        delete from sc_requirement where id = #{id}
    </delete>

    <delete id="deleteScRequirementByIds" parameterType="String">
        delete from sc_requirement where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>