<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorStationMapper">
    
    <resultMap type="com.tunnel.domain.MonitorStation" id="MonitorStationResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="companyName"    column="company_name"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="openingStatus"    column="opening_status"    />
        <result property="maintenanceCategory"    column="maintenance_category"    />
        <result property="sewageProcess"    column="sewage_process"    />
        <result property="equipmentModel"    column="equipment_model"    />
        <result property="equipmentQuantity"    column="equipment_quantity"    />
        <result property="processingScale"    column="processing_scale"    />
        <result property="maintenanceType"    column="maintenance_type"    />
        <result property="pollutionType"    column="pollution_type"    />
        <result property="state"    column="state"    />
        <result property="faulty"    column="faulty"    />
        <result property="stationIndex"    column="stationIndex"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, code, name, province, city, district, company_name, lat, lon, opening_status,
        maintenance_category, sewage_process, equipment_model, equipment_quantity, processing_scale,
        maintenance_type, pollution_type, state, faulty, stationIndex, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorStationList" parameterType="com.tunnel.domain.MonitorStation" resultMap="MonitorStationResult">
        SELECT m.*, s.system_code from sc_monitor_station m
        LEFT JOIN sc_integration_system s on m.code = s.monitor_station_code
        <where>
            <if test="systemCode != null and systemCode != ''"> and s.system_code = #{systemCode}</if>
            <if test="name != null and name != ''"> and m.name like concat('%', #{name}, '%')</if>
            <if test="code != null and code != ''"> and m.code = #{code}</if>
            <if test="city != null and city != ''"> and m.city = #{city}</if>
            <if test="district != null and district != ''"> and m.district = #{district}</if>
            <if test="province != null and province != ''"> and m.province = #{province}</if>
            <if test="companyName != null and companyName != ''"> and m.company_name like concat('%', #{companyName}, '%')</if>
            <if test="openingStatus != null and openingStatus != ''"> and m.opening_status = #{openingStatus}</if>
            <if test="maintenanceCategory != null and maintenanceCategory != ''"> and m.maintenance_category = #{maintenanceCategory}</if>
            <if test="maintenanceType != null and maintenanceType != ''"> and m.maintenance_type = #{maintenanceType}</if>
            <if test="state != null"> and m.state = #{state}</if>
            <if test="faulty != null"> and m.faulty = #{faulty}</if>
            <if test="pollutionType != null and pollutionType != ''"> and m.pollution_type like concat('%', #{pollutionType}, '%')</if>
        </where>
        ORDER BY m.stationIndex ASC, m.create_time DESC
    </select>
    
    <select id="selectMonitorStationById" parameterType="Long" resultMap="MonitorStationResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
    <select id="selectMonitorStationByType" resultMap="MonitorStationResult">
        SELECT m.*,s.system_code from sc_monitor_station m
        INNER JOIN sc_integration_system s on m.code=s.monitor_station_code
        WHERE s.type=#{type}
    </select>

    <insert id="insertMonitorStation" parameterType="com.tunnel.domain.MonitorStation" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="district != null and district != ''">district,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="lat != null">lat,</if>
            <if test="lon != null">lon,</if>
            <if test="openingStatus != null and openingStatus != ''">opening_status,</if>
            <if test="maintenanceCategory != null and maintenanceCategory != ''">maintenance_category,</if>
            <if test="sewageProcess != null and sewageProcess != ''">sewage_process,</if>
            <if test="equipmentModel != null and equipmentModel != ''">equipment_model,</if>
            <if test="equipmentQuantity != null">equipment_quantity,</if>
            <if test="processingScale != null">processing_scale,</if>
            <if test="maintenanceType != null and maintenanceType != ''">maintenance_type,</if>
            <if test="pollutionType != null and pollutionType != ''">pollution_type,</if>
            <if test="state != null">state,</if>
            <if test="faulty != null">faulty,</if>
            <if test="stationIndex != null">stationIndex,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            create_time, update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="district != null and district != ''">#{district},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lon != null">#{lon},</if>
            <if test="openingStatus != null and openingStatus != ''">#{openingStatus},</if>
            <if test="maintenanceCategory != null and maintenanceCategory != ''">#{maintenanceCategory},</if>
            <if test="sewageProcess != null and sewageProcess != ''">#{sewageProcess},</if>
            <if test="equipmentModel != null and equipmentModel != ''">#{equipmentModel},</if>
            <if test="equipmentQuantity != null">#{equipmentQuantity},</if>
            <if test="processingScale != null">#{processingScale},</if>
            <if test="maintenanceType != null and maintenanceType != ''">#{maintenanceType},</if>
            <if test="pollutionType != null and pollutionType != ''">#{pollutionType},</if>
            <if test="state != null">#{state},</if>
            <if test="faulty != null">#{faulty},</if>
            <if test="stationIndex != null">#{stationIndex},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            now(), now()
         </trim>
    </insert>

    <update id="updateMonitorStation" parameterType="com.tunnel.domain.MonitorStation">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="openingStatus != null">opening_status = #{openingStatus},</if>
            <if test="maintenanceCategory != null">maintenance_category = #{maintenanceCategory},</if>
            <if test="sewageProcess != null">sewage_process = #{sewageProcess},</if>
            <if test="equipmentModel != null">equipment_model = #{equipmentModel},</if>
            <if test="equipmentQuantity != null">equipment_quantity = #{equipmentQuantity},</if>
            <if test="processingScale != null">processing_scale = #{processingScale},</if>
            <if test="maintenanceType != null">maintenance_type = #{maintenanceType},</if>
            <if test="pollutionType != null">pollution_type = #{pollutionType},</if>
            <if test="state != null">state = #{state},</if>
            <if test="faulty != null">faulty = #{faulty},</if>
            <if test="stationIndex != null">stationIndex = #{stationIndex},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorStationById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteMonitorStationByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_station
    </sql>
</mapper>