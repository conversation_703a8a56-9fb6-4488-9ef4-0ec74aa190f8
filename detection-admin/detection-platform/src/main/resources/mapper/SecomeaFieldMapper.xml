<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.SecomeaFieldMapper">
    
    <resultMap type="com.tunnel.domain.SecomeaField" id="SecomeaFieldResult">
        <result property="id"    column="id"    />
        <result property="checked"    column="checked"    />
        <result property="pointAddr"    column="point_addr"    />
        <result property="monitorType" column="monitor_type" />
        <result property="monitorTypeName" column="monitor_type_name" />
        <result property="name"    column="name"    />
        <result property="processingGatewayCode"    column="processing_gateway_code"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, checked, point_addr, monitor_type, monitor_type_name, name, processing_gateway_code, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectSecomeaFieldList" parameterType="com.tunnel.domain.SecomeaField" resultMap="SecomeaFieldResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        <where>  
            <if test="checked != null "> and checked = #{checked}</if>
            <if test="pointAddr != null  and pointAddr != ''"> and point_addr = #{pointAddr}</if>
                <if test="monitorType != null  and monitorType != ''"> and monitor_type = #{monitorType}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="processingGatewayCode != null  and processingGatewayCode != ''"> and processing_gateway_code = #{processingGatewayCode}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectSecomeaFieldById" parameterType="Long" resultMap="SecomeaFieldResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertSecomeaField" parameterType="com.tunnel.domain.SecomeaField" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checked != null">checked,</if>
            <if test="pointAddr != null and pointAddr != ''">point_addr,</if>
            <if test="monitorType != null and monitorType != ''">monitor_type,</if>
            <if test="monitorTypeName != null and monitorTypeName != ''">monitor_type_name,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="processingGatewayCode != null and processingGatewayCode != ''">processing_gateway_code,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checked != null">#{checked},</if>
            <if test="pointAddr != null and pointAddr != ''">#{pointAddr},</if>
            <if test="monitorType != null and monitorType != ''">#{monitorType},</if>
            <if test="monitorTypeName != null and monitorTypeName != ''">#{monitorTypeName},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="processingGatewayCode != null and processingGatewayCode != ''">#{processingGatewayCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateSecomeaField" parameterType="com.tunnel.domain.SecomeaField">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="checked != null">checked = #{checked},</if>
            <if test="pointAddr != null and pointAddr != ''">point_addr = #{pointAddr},</if>
            <if test="monitorType != null and monitorType != ''">monitor_type = #{monitorType},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="processingGatewayCode != null and processingGatewayCode != ''">processing_gateway_code = #{processingGatewayCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecomeaFieldById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteSecomeaFieldByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_secomea_field
    </sql>
</mapper>