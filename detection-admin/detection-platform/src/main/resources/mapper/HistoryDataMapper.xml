<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.HistoryDataMapper">

    <select id="selectDistinctTimes" resultType="java.lang.String">
        select distinct time
        from ${tableName}
        where status = 0
        <if test="lastTime != null and lastTime != ''">
            and time > #{lastTime}
        </if>
        order by time
        limit #{limitSize}
    </select>

    <select id="selectByTimes" resultType="java.util.Map">
        select h.id, h.flag, h.time, h.value, h.factor_id, h.status,
               f.code as factor_code, f.name as factor_name, f.monitoringSystem_systemCode
        from ${tableName} h
        inner join monitor_factor f on h.factor_id = f.id
        where h.status = 0
          and h.time in
        <foreach collection="times" item="t" open="(" close=")" separator=",">#{t}</foreach>
        order by h.time, h.id
    </select>

    <update id="markProcessedByTimes">
        update ${tableName}
        set status = 1
        where status = 0
          and time in
        <foreach collection="times" item="t" open="(" close=")" separator=",">#{t}</foreach>
    </update>

</mapper>
