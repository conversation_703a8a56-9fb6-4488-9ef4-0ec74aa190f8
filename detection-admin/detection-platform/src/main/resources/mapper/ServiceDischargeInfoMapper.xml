<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceDischargeInfoMapper">
    
    <resultMap type="ServiceDischargeInfo" id="ServiceDischargeInfoResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="areaType"    column="area_type"    />
        <result property="outletLocation"    column="outlet_location"    />
        <result property="dischargeDestination"    column="discharge_destination"    />
        <result property="externalEnvironment"    column="external_environment"    />
        <result property="otherConditions"    column="other_conditions"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceDischargeInfoVo">
        select id, service_area_id, area_type, outlet_location, discharge_destination, external_environment, other_conditions, remark, create_time, update_time, creator, modifier from sc_service_discharge_info
    </sql>

    <select id="selectServiceDischargeInfoList" parameterType="ServiceDischargeInfo" resultMap="ServiceDischargeInfoResult">
        <include refid="selectServiceDischargeInfoVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="outletLocation != null  and outletLocation != ''"> and outlet_location like concat('%', #{outletLocation}, '%')</if>
            <if test="dischargeDestination != null  and dischargeDestination != ''"> and discharge_destination like concat('%', #{dischargeDestination}, '%')</if>
            <if test="externalEnvironment != null  and externalEnvironment != ''"> and external_environment like concat('%', #{externalEnvironment}, '%')</if>
        </where>
    </select>
    
    <select id="selectServiceDischargeInfoById" parameterType="Long" resultMap="ServiceDischargeInfoResult">
        <include refid="selectServiceDischargeInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceDischargeInfoByServiceAreaId" parameterType="Long" resultMap="ServiceDischargeInfoResult">
        <include refid="selectServiceDischargeInfoVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceDischargeInfo" parameterType="ServiceDischargeInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_discharge_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="outletLocation != null">outlet_location,</if>
            <if test="dischargeDestination != null">discharge_destination,</if>
            <if test="externalEnvironment != null">external_environment,</if>
            <if test="otherConditions != null">other_conditions,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="outletLocation != null">#{outletLocation},</if>
            <if test="dischargeDestination != null">#{dischargeDestination},</if>
            <if test="externalEnvironment != null">#{externalEnvironment},</if>
            <if test="otherConditions != null">#{otherConditions},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceDischargeInfo" parameterType="ServiceDischargeInfo">
        update sc_service_discharge_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="outletLocation != null">outlet_location = #{outletLocation},</if>
            <if test="dischargeDestination != null">discharge_destination = #{dischargeDestination},</if>
            <if test="externalEnvironment != null">external_environment = #{externalEnvironment},</if>
            <if test="otherConditions != null">other_conditions = #{otherConditions},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceDischargeInfoById" parameterType="Long">
        delete from sc_service_discharge_info where id = #{id}
    </delete>

    <delete id="deleteServiceDischargeInfoByIds" parameterType="String">
        delete from sc_service_discharge_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceDischargeInfoByServiceAreaId" parameterType="Long">
        delete from sc_service_discharge_info where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
