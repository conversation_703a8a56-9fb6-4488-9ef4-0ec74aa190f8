<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorNoiseMapper">

    <resultMap type="MonitorNoise" id="ScMonitorNoiseResult">
        <result property="id" column="id"/>
        <result property="monitorCode" column="monitor_code"/>
        <result property="bKey" column="b_key"/>
        <result property="topic" column="topic"/>
        <result property="l10" column="l10"/>
        <result property="ldn" column="ldn"/>
        <result property="ln" column="ln"/>
        <result property="l50" column="l50"/>
        <result property="l5" column="l5"/>
        <result property="ld" column="ld"/>
        <result property="prf" column="prf"/>
        <result property="l95" column="l95"/>
        <result property="rate" column="rate"/>
        <result property="l90" column="l90"/>
        <result property="sd" column="sd"/>
        <result property="leq" column="leq"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="modifier" column="modifier"/>
    </resultMap>

    <sql id="selectScMonitorNoiseVo">
        select id, monitor_code, b_key, topic, l10, ldn, ln, l50, l5, ld, prf, l95, rate, l90, sd, leq, remark, create_time, update_time, creator, modifier
        from sc_monitor_noise
    </sql>

    <select id="selectScMonitorNoiseList" parameterType="MonitorNoise" resultMap="ScMonitorNoiseResult">
        <include refid="selectScMonitorNoiseVo"/>
        <where>
            <if test="monitorCode != null and monitorCode != ''">
                AND monitor_code = #{monitorCode}
            </if>
            <if test="bKey != null and bKey != ''">
                AND b_key = #{bKey}
            </if>
            <if test="createTime != null">
                AND create_time = #{createTime}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectScMonitorNoiseById" parameterType="Long" resultMap="ScMonitorNoiseResult">
        <include refid="selectScMonitorNoiseVo"/>
        where id = #{id}
    </select>
    <select id="selectByParams" resultType="java.util.Map">
        select s.*
        from sc_monitor_noise s
        where create_time between #{startTime} and  #{endTime}
          and monitor_code = #{systemCode}
        order by create_time desc
    </select>
    <select id="selectTimeRange" resultType="com.tunnel.domain.MonitorDTO">
        select max(create_time) endTime from sc_monitor_noise
        where  monitor_code = #{systemCode}
    </select>

    <insert id="insertScMonitorNoise" parameterType="MonitorNoise" useGeneratedKeys="true" keyProperty="id">
        insert into sc_monitor_noise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monitorCode != null">monitor_code,</if>
            <if test="bKey != null">b_key,</if>
            <if test="topic != null">topic,</if>
            <if test="l10 != null">l10,</if>
            <if test="ldn != null">ldn,</if>
            <if test="ln != null">ln,</if>
            <if test="l50 != null">l50,</if>
            <if test="l5 != null">l5,</if>
            <if test="ld != null">ld,</if>
            <if test="prf != null">prf,</if>
            <if test="l95 != null">l95,</if>
            <if test="rate != null">rate,</if>
            <if test="l90 != null">l90,</if>
            <if test="sd != null">sd,</if>
            <if test="leq != null">leq,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monitorCode != null">#{monitorCode},</if>
            <if test="bKey != null">#{bKey},</if>
            <if test="topic != null">#{topic},</if>
            <if test="l10 != null">#{l10},</if>
            <if test="ldn != null">#{ldn},</if>
            <if test="ln != null">#{ln},</if>
            <if test="l50 != null">#{l50},</if>
            <if test="l5 != null">#{l5},</if>
            <if test="ld != null">#{ld},</if>
            <if test="prf != null">#{prf},</if>
            <if test="l95 != null">#{l95},</if>
            <if test="rate != null">#{rate},</if>
            <if test="l90 != null">#{l90},</if>
            <if test="sd != null">#{sd},</if>
            <if test="leq != null">#{leq},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sc_monitor_noise (monitor_code, b_key, topic, l10, ldn, ln, l50, l5, ld, prf, l95, rate, l90, sd, leq, remark, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.monitorCode}, #{item.bKey}, #{item.topic}, #{item.l10}, #{item.ldn}, #{item.ln}, #{item.l50}, #{item.l5}, #{item.ld}, #{item.prf}, #{item.l95}, #{item.rate}, #{item.l90}, #{item.sd}, #{item.leq}, #{item.remark}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateScMonitorNoise" parameterType="MonitorNoise">
        update sc_monitor_noise
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorCode != null">monitor_code = #{monitorCode},</if>
            <if test="bKey != null">b_key = #{bKey},</if>
            <if test="topic != null">topic = #{topic},</if>
            <if test="l10 != null">l10 = #{l10},</if>
            <if test="ldn != null">ldn = #{ldn},</if>
            <if test="ln != null">ln = #{ln},</if>
            <if test="l50 != null">l50 = #{l50},</if>
            <if test="l5 != null">l5 = #{l5},</if>
            <if test="ld != null">ld = #{ld},</if>
            <if test="prf != null">prf = #{prf},</if>
            <if test="l95 != null">l95 = #{l95},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="l90 != null">l90 = #{l90},</if>
            <if test="sd != null">sd = #{sd},</if>
            <if test="leq != null">leq = #{leq},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScMonitorNoiseById" parameterType="Long">
        delete from sc_monitor_noise where id = #{id}
    </delete>

    <delete id="deleteScMonitorNoiseByIds" parameterType="Long">
        delete from sc_monitor_noise where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
