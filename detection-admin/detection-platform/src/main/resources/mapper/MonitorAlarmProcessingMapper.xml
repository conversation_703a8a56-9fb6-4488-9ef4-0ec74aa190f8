<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorAlarmProcessingMapper">
    
    <resultMap type="com.tunnel.domain.MonitorAlarmProcessing" id="MonitorAlarmProcessingResult">
        <result property="id"    column="id"    />
        <result property="alarmLimit"    column="alarm_limit"    />
        <result property="factorId"    column="factor_id"    />
<!--        <result property="factorCode"    column="factor_code"    />-->
        <result property="factorName"    column="factor_name"    />
        <result property="handled"    column="handled"    />
        <result property="level"    column="level"    />
        <result property="max"    column="max"    />
        <result property="min"    column="min"    />
        <result property="serviceArea"    column="service_area"    />
        <result property="serviceId"    column="service_id"    />
        <result property="systemCode"    column="system_code"    />
        <result property="systemName"    column="system_name"    />
        <result property="time"    column="time"    />
<!--        <result property="realValue"    column="real_value"    />-->
<!--        <result property="alarmLevel"    column="alarm_level"    />-->
        <result property="value"    column="value"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, alarm_limit, factor_id,
          factor_name, handled, level, max, min, service_area, service_id, system_code, system_name, time,
          value, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorAlarmProcessingList" parameterType="com.tunnel.domain.MonitorAlarmProcessing" resultMap="MonitorAlarmProcessingResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        <where>  
            <if test="alarmLimit != null "> and alarm_limit = #{alarmLimit}</if>
            <if test="factorId != null  and factorId != ''"> and factor_id = #{factorId}</if>
<!--            <if test="factorCode != null  and factorCode != ''"> and factor_code = #{factorCode}</if>-->
            <if test="factorName != null  and factorName != ''"> and factor_name like concat('%', #{factorName}, '%')</if>
            <if test="handled != null "> and handled = #{handled}</if>
            <if test="level != null  and level != ''"> and level = #{level}</if>
            <if test="max != null "> and max = #{max}</if>
            <if test="min != null "> and min = #{min}</if>
            <if test="serviceArea != null  and serviceArea != ''"> and service_area = #{serviceArea}</if>
            <if test="serviceId != null  and serviceId != ''"> and service_id = #{serviceId}</if>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="time != null  and time != ''"> and time = #{time}</if>
<!--            <if test="realValue != null "> and real_value = #{realValue}</if>-->
<!--            <if test="alarmLevel != null  and alarmLevel != ''"> and alarm_level = #{alarmLevel}</if>-->
            <if test="value != null "> and value = #{value}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
            <if test="startTime != null"> and time &gt;= #{startTime}</if>
            <if test="endTime != null"> and time &lt;= #{endTime}</if>
        </where>
    </select>
    
    <select id="selectMonitorAlarmProcessingById" parameterType="Long" resultMap="MonitorAlarmProcessingResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertMonitorAlarmProcessing" parameterType="com.tunnel.domain.MonitorAlarmProcessing" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmLimit != null">alarm_limit,</if>
            <if test="factorId != null">factor_id,</if>
<!--            <if test="factorCode != null">factor_code,</if>-->
            <if test="factorName != null">factor_name,</if>
            <if test="handled != null">handled,</if>
            <if test="level != null">level,</if>
            <if test="max != null">max,</if>
            <if test="min != null">min,</if>
            <if test="serviceArea != null">service_area,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="systemCode != null">system_code,</if>
            <if test="systemName != null">system_name,</if>
            <if test="time != null">time,</if>
<!--            <if test="realValue != null">real_value,</if>-->
<!--            <if test="alarmLevel != null">alarm_level,</if>-->
            <if test="value != null">value,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmLimit != null">#{alarmLimit},</if>
            <if test="factorId != null">#{factorId},</if>
<!--            <if test="factorCode != null">#{factorCode},</if>-->
            <if test="factorName != null">#{factorName},</if>
            <if test="handled != null">#{handled},</if>
            <if test="level != null">#{level},</if>
            <if test="max != null">#{max},</if>
            <if test="min != null">#{min},</if>
            <if test="serviceArea != null">#{serviceArea},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="systemCode != null">#{systemCode},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="time != null">#{time},</if>
<!--            <if test="realValue != null">#{realValue},</if>-->
<!--            <if test="alarmLevel != null">#{alarmLevel},</if>-->
            <if test="value != null">#{value},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateMonitorAlarmProcessing" parameterType="com.tunnel.domain.MonitorAlarmProcessing">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmLimit != null">alarm_limit = #{alarmLimit},</if>
            <if test="factorId != null">factor_id = #{factorId},</if>
<!--            <if test="factorCode != null">factor_code = #{factorCode},</if>-->
            <if test="factorName != null">factor_name = #{factorName},</if>
            <if test="handled != null">handled = #{handled},</if>
            <if test="level != null">level = #{level},</if>
            <if test="max != null">max = #{max},</if>
            <if test="min != null">min = #{min},</if>
            <if test="serviceArea != null">service_area = #{serviceArea},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="systemCode != null">system_code = #{systemCode},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="time != null">time = #{time},</if>
<!--            <if test="realValue != null">real_value = #{realValue},</if>-->
<!--            <if test="alarmLevel != null">alarm_level = #{alarmLevel},</if>-->
            <if test="value != null">value = #{value},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorAlarmProcessingById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteMonitorAlarmProcessingByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_alarm_processing
    </sql>
</mapper>