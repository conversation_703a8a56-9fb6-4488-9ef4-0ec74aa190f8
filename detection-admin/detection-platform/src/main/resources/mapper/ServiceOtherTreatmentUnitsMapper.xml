<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceOtherTreatmentUnitsMapper">
    
    <resultMap type="ServiceOtherTreatmentUnits" id="ServiceOtherTreatmentUnitsResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="areaType"    column="area_type"    />
        <result property="unitName"    column="unit_name"    />
        <result property="operationStatus"    column="operation_status"    />
        <result property="equipmentName"    column="equipment_name"    />
        <result property="manufacturerModel"    column="manufacturer_model"    />
        <result property="equipmentParameters"    column="equipment_parameters"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceOtherTreatmentUnitsVo">
        select id, service_area_id, area_type, unit_name, operation_status, equipment_name, manufacturer_model, equipment_parameters, remark, create_time, update_time, creator, modifier from sc_service_other_treatment_units
    </sql>

    <select id="selectServiceOtherTreatmentUnitsList" parameterType="ServiceOtherTreatmentUnits" resultMap="ServiceOtherTreatmentUnitsResult">
        <include refid="selectServiceOtherTreatmentUnitsVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="operationStatus != null  and operationStatus != ''"> and operation_status like concat('%', #{operationStatus}, '%')</if>
            <if test="equipmentName != null  and equipmentName != ''"> and equipment_name like concat('%', #{equipmentName}, '%')</if>
        </where>
    </select>
    
    <select id="selectServiceOtherTreatmentUnitsById" parameterType="Long" resultMap="ServiceOtherTreatmentUnitsResult">
        <include refid="selectServiceOtherTreatmentUnitsVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceOtherTreatmentUnitsByServiceAreaId" parameterType="Long" resultMap="ServiceOtherTreatmentUnitsResult">
        <include refid="selectServiceOtherTreatmentUnitsVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceOtherTreatmentUnits" parameterType="ServiceOtherTreatmentUnits" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_other_treatment_units
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="unitName != null and unitName != ''">unit_name,</if>
            <if test="operationStatus != null">operation_status,</if>
            <if test="equipmentName != null">equipment_name,</if>
            <if test="manufacturerModel != null">manufacturer_model,</if>
            <if test="equipmentParameters != null">equipment_parameters,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="unitName != null and unitName != ''">#{unitName},</if>
            <if test="operationStatus != null">#{operationStatus},</if>
            <if test="equipmentName != null">#{equipmentName},</if>
            <if test="manufacturerModel != null">#{manufacturerModel},</if>
            <if test="equipmentParameters != null">#{equipmentParameters},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceOtherTreatmentUnits" parameterType="ServiceOtherTreatmentUnits">
        update sc_service_other_treatment_units
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="unitName != null and unitName != ''">unit_name = #{unitName},</if>
            <if test="operationStatus != null">operation_status = #{operationStatus},</if>
            <if test="equipmentName != null">equipment_name = #{equipmentName},</if>
            <if test="manufacturerModel != null">manufacturer_model = #{manufacturerModel},</if>
            <if test="equipmentParameters != null">equipment_parameters = #{equipmentParameters},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceOtherTreatmentUnitsById" parameterType="Long">
        delete from sc_service_other_treatment_units where id = #{id}
    </delete>

    <delete id="deleteServiceOtherTreatmentUnitsByIds" parameterType="String">
        delete from sc_service_other_treatment_units where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceOtherTreatmentUnitsByServiceAreaId" parameterType="Long">
        delete from sc_service_other_treatment_units where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
