<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RequirementReportMapper">
    
    <resultMap type="com.tunnel.domain.RequirementReport" id="RequirementReportResult">
        <result property="id"    column="id"    />
        <result property="topic"  column="topic"    />
        <result property="key"    column="b_key"    />
        <result property="gatewayId"    column="gateway_id"    />
        <result property="i160"    column="i16_0"    />
        <result property="i161"    column="i16_1"    />
        <result property="i162"    column="i16_2"    />
        <result property="i163"    column="i16_3"    />
        <result property="i164"    column="i16_4"    />
        <result property="i165"    column="i16_5"    />
        <result property="i166"    column="i16_6"    />
        <result property="i167"    column="i16_7"    />
        <result property="i170"    column="i17_0"    />
        <result property="i171"    column="i17_1"    />
        <result property="i172"    column="i17_2"    />
        <result property="i173"    column="i17_3"    />
        <result property="i174"    column="i17_4"    />
        <result property="i200"    column="i20_0"    />
        <result property="i201"    column="i20_1"    />
        <result property="i202"    column="i20_2"    />
        <result property="i203"    column="i20_3"    />
        <result property="i204"    column="i20_4"    />
        <result property="i205"    column="i20_5"    />
        <result property="i206"    column="i20_6"    />
        <result property="i210"    column="i21_0"    />
        <result property="i211"    column="i21_1"    />
        <result property="i212"    column="i21_2"    />
        <result property="i213"    column="i21_3"    />
        <result property="i214"    column="i21_4"    />
        <result property="vd200"    column="vd200"    />
        <result property="vd230"    column="vd230"    />
        <result property="vd260"    column="vd260"    />
        <result property="vd2068"    column="vd2068"    />
        <result property="vd404"    column="vd404"    />
        <result property="vd408"    column="vd408"    />
        <result property="vd704"    column="vd704"    />
        <result property="vd712"    column="vd712"    />
        <result property="vd716"    column="vd716"    />
        <result property="vd720"    column="vd720"    />
        <result property="vd728"    column="vd728"    />
        <result property="vd736"    column="vd736"    />
        <result property="vd740"    column="vd740"    />
        <result property="vd744"    column="vd744"    />
        <result property="vd752"    column="vd752"    />
        <result property="vd760"    column="vd760"    />
        <result property="vd764"    column="vd764"    />
        <result property="vd904"    column="vd904"    />
        <result property="vd912"    column="vd912"    />
        <result property="vd916"    column="vd916"    />
        <result property="vd928"    column="vd928"    />
        <result property="vd936"    column="vd936"    />
        <result property="vd940"    column="vd940"    />
        <result property="vd952"    column="vd952"    />
        <result property="vd960"    column="vd960"    />
        <result property="vd964"    column="vd964"    />
        <result property="vd4020"    column="vd4020"    />
        <result property="vd4028"    column="vd4028"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectRequirementReportVo">
        select id, b_key, topic, gateway_id, 
               i16_0, i16_1, i16_2, i16_3, i16_4, i16_5, i16_6, i16_7,
               i17_0, i17_1, i17_2, i17_3, i17_4,
               i20_0, i20_1, i20_2, i20_3, i20_4, i20_5, i20_6, i21_0, i21_1, i21_2, i21_3, i21_4, 
               vd200, vd230, vd260, vd2068, vd404, vd408, vd704, vd712, vd716, vd720, vd728, vd736, vd740, vd744, vd752, vd760, vd764, vd904, vd912, vd916, vd928, vd936, vd940, vd952, vd960, vd964, vd4020, vd4028,
            remark, create_time, update_time, creator, modifier
        from sc_requirement_report
    </sql>

    <select id="selectRequirementReportList" parameterType="com.tunnel.domain.RequirementReport" resultMap="RequirementReportResult">
        <include refid="selectRequirementReportVo"/>
        <where>  
            <if test="key != null  and key != ''"> and b_key = #{key}</if>
            <if test="topic != null  and topic != ''"> and topic = #{topic}</if>
            <if test="i160 != null  and i160 != ''"> and i16_0 = #{i160}</if>
            <if test="i161 != null  and i161 != ''"> and i16_1 = #{i161}</if>
            <if test="i162 != null  and i162 != ''"> and i16_2 = #{i162}</if>
            <if test="i163 != null  and i163 != ''"> and i16_3 = #{i163}</if>
            <if test="i164 != null  and i164 != ''"> and i16_4 = #{i164}</if>
            <if test="i165 != null  and i165 != ''"> and i16_5 = #{i165}</if>
            <if test="i166 != null  and i166 != ''"> and i16_6 = #{i166}</if>
            <if test="i167 != null  and i167 != ''"> and i16_7 = #{i167}</if>
            <if test="i170 != null  and i170 != ''"> and i17_0 = #{i170}</if>
            <if test="i171 != null  and i171 != ''"> and i17_1 = #{i171}</if>
            <if test="i172 != null  and i172 != ''"> and i17_2 = #{i172}</if>
            <if test="i173 != null  and i173 != ''"> and i17_3 = #{i173}</if>
            <if test="i174 != null  and i174 != ''"> and i17_4 = #{i174}</if>
            <if test="i200 != null  and i200 != ''"> and i20_0 = #{i200}</if>
            <if test="i201 != null  and i201 != ''"> and i20_1 = #{i201}</if>
            <if test="i202 != null  and i202 != ''"> and i20_2 = #{i202}</if>
            <if test="i203 != null  and i203 != ''"> and i20_3 = #{i203}</if>
            <if test="i204 != null  and i204 != ''"> and i20_4 = #{i204}</if>
            <if test="i205 != null  and i205 != ''"> and i20_5 = #{i205}</if>
            <if test="i206 != null  and i206 != ''"> and i20_6 = #{i206}</if>
            <if test="i210 != null  and i210 != ''"> and i21_0 = #{i210}</if>
            <if test="i211 != null  and i211 != ''"> and i21_1 = #{i211}</if>
            <if test="i212 != null  and i212 != ''"> and i21_2 = #{i212}</if>
            <if test="i213 != null  and i213 != ''"> and i21_3 = #{i213}</if>
            <if test="i214 != null  and i214 != ''"> and i21_4 = #{i214}</if>
            <if test="vd200 != null  and vd200 != ''"> and vd200 = #{vd200}</if>
            <if test="vd230 != null  and vd230 != ''"> and vd230 = #{vd230}</if>
            <if test="vd260 != null  and vd260 != ''"> and vd260 = #{vd260}</if>
            <if test="vd2068 != null  and vd2068 != ''"> and vd2068 = #{vd2068}</if>
            <if test="vd404 != null  and vd404 != ''"> and vd404 = #{vd404}</if>
            <if test="vd408 != null  and vd408 != ''"> and vd408 = #{vd408}</if>
            <if test="vd704 != null  and vd704 != ''"> and vd704 = #{vd704}</if>
            <if test="vd712 != null  and vd712 != ''"> and vd712 = #{vd712}</if>
            <if test="vd716 != null  and vd716 != ''"> and vd716 = #{vd716}</if>
            <if test="vd720 != null  and vd720 != ''"> and vd720 = #{vd720}</if>
            <if test="vd728 != null  and vd728 != ''"> and vd728 = #{vd728}</if>
            <if test="vd736 != null  and vd736 != ''"> and vd736 = #{vd736}</if>
            <if test="vd740 != null  and vd740 != ''"> and vd740 = #{vd740}</if>
            <if test="vd744 != null  and vd744 != ''"> and vd744 = #{vd744}</if>
            <if test="vd752 != null  and vd752 != ''"> and vd752 = #{vd752}</if>
            <if test="vd760 != null  and vd760 != ''"> and vd760 = #{vd760}</if>
            <if test="vd764 != null  and vd764 != ''"> and vd764 = #{vd764}</if>
            <if test="vd904 != null  and vd904 != ''"> and vd904 = #{vd904}</if>
            <if test="vd912 != null  and vd912 != ''"> and vd912 = #{vd912}</if>
            <if test="vd916 != null  and vd916 != ''"> and vd916 = #{vd916}</if>
            <if test="vd928 != null  and vd928 != ''"> and vd928 = #{vd928}</if>
            <if test="vd936 != null  and vd936 != ''"> and vd936 = #{vd936}</if>
            <if test="vd940 != null  and vd940 != ''"> and vd940 = #{vd940}</if>
            <if test="vd952 != null  and vd952 != ''"> and vd952 = #{vd952}</if>
            <if test="vd960 != null  and vd960 != ''"> and vd960 = #{vd960}</if>
            <if test="vd964 != null  and vd964 != ''"> and vd964 = #{vd964}</if>
        </where>
    </select>
    
    <select id="selectRequirementReportById" parameterType="String" resultMap="RequirementReportResult">
        <include refid="selectRequirementReportVo"/>
        where id = #{id}
    </select>

    <insert id="insertRequirementReport" parameterType="com.tunnel.domain.RequirementReport" useGeneratedKeys="true" keyProperty="id">
        insert into sc_requirement_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="key != null and key != ''">b_key,</if>
            <if test="topic != null  and topic != ''">topic,</if>
            <if test="gatewayId != null  and gatewayId != ''">gateway_id,</if>
            <if test="i160 != null">i16_0,</if>
            <if test="i161 != null">i16_1,</if>
            <if test="i162 != null">i16_2,</if>
            <if test="i163 != null">i16_3,</if>
            <if test="i164 != null">i16_4,</if>
            <if test="i165 != null">i16_5,</if>
            <if test="i166 != null">i16_6,</if>
            <if test="i167 != null">i16_7,</if>
            <if test="i170 != null">i17_0,</if>
            <if test="i171 != null">i17_1,</if>
            <if test="i172 != null">i17_2,</if>
            <if test="i173 != null">i17_3,</if>
            <if test="i174 != null">i17_4,</if>
            <if test="i200 != null">i20_0,</if>
            <if test="i201 != null">i20_1,</if>
            <if test="i202 != null">i20_2,</if>
            <if test="i203 != null">i20_3,</if>
            <if test="i204 != null">i20_4,</if>
            <if test="i205 != null">i20_5,</if>
            <if test="i206 != null">i20_6,</if>
            <if test="i210 != null">i21_0,</if>
            <if test="i211 != null">i21_1,</if>
            <if test="i212 != null">i21_2,</if>
            <if test="i213 != null">i21_3,</if>
            <if test="i214 != null">i21_4,</if>
            <if test="vd200 != null">vd200,</if>
            <if test="vd230 != null">vd230,</if>
            <if test="vd260 != null">vd260,</if>
            <if test="vd2068 != null">vd2068,</if>
            <if test="vd404 != null">vd404,</if>
            <if test="vd408 != null">vd408,</if>
            <if test="vd704 != null">vd704,</if>
            <if test="vd712 != null">vd712,</if>
            <if test="vd716 != null">vd716,</if>
            <if test="vd720 != null">vd720,</if>
            <if test="vd728 != null">vd728,</if>
            <if test="vd736 != null">vd736,</if>
            <if test="vd740 != null">vd740,</if>
            <if test="vd744 != null">vd744,</if>
            <if test="vd752 != null">vd752,</if>
            <if test="vd760 != null">vd760,</if>
            <if test="vd764 != null">vd764,</if>
            <if test="vd904 != null">vd904,</if>
            <if test="vd912 != null">vd912,</if>
            <if test="vd916 != null">vd916,</if>
            <if test="vd928 != null">vd928,</if>
            <if test="vd936 != null">vd936,</if>
            <if test="vd940 != null">vd940,</if>
            <if test="vd952 != null">vd952,</if>
            <if test="vd960 != null">vd960,</if>
            <if test="vd964 != null">vd964,</if>
            <if test="vd4020 != null">vd4020,</if>
            <if test="vd4028 != null">vd4028,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="key != null and key != ''">#{key},</if>
            <if test="topic != null  and topic != ''">#{topic},</if>
            <if test="gatewayId != null  and gatewayId != ''">#{gatewayId},</if>
            <if test="i160 != null">#{i160},</if>
            <if test="i161 != null">#{i161},</if>
            <if test="i162 != null">#{i162},</if>
            <if test="i163 != null">#{i163},</if>
            <if test="i164 != null">#{i164},</if>
            <if test="i165 != null">#{i165},</if>
            <if test="i166 != null">#{i166},</if>
            <if test="i167 != null">#{i167},</if>
            <if test="i170 != null">#{i170},</if>
            <if test="i171 != null">#{i171},</if>
            <if test="i172 != null">#{i172},</if>
            <if test="i173 != null">#{i173},</if>
            <if test="i174 != null">#{i174},</if>
            <if test="i200 != null">#{i200},</if>
            <if test="i201 != null">#{i201},</if>
            <if test="i202 != null">#{i202},</if>
            <if test="i203 != null">#{i203},</if>
            <if test="i204 != null">#{i204},</if>
            <if test="i205 != null">#{i205},</if>
            <if test="i206 != null">#{i206},</if>
            <if test="i210 != null">#{i210},</if>
            <if test="i211 != null">#{i211},</if>
            <if test="i212 != null">#{i212},</if>
            <if test="i213 != null">#{i213},</if>
            <if test="i214 != null">#{i214},</if>
            <if test="vd200 != null">#{vd200},</if>
            <if test="vd230 != null">#{vd230},</if>
            <if test="vd260 != null">#{vd260},</if>
            <if test="vd2068 != null">#{vd2068},</if>
            <if test="vd404 != null">#{vd404},</if>
            <if test="vd408 != null">#{vd408},</if>
            <if test="vd704 != null">#{vd704},</if>
            <if test="vd712 != null">#{vd712},</if>
            <if test="vd716 != null">#{vd716},</if>
            <if test="vd720 != null">#{vd720},</if>
            <if test="vd728 != null">#{vd728},</if>
            <if test="vd736 != null">#{vd736},</if>
            <if test="vd740 != null">#{vd740},</if>
            <if test="vd744 != null">#{vd744},</if>
            <if test="vd752 != null">#{vd752},</if>
            <if test="vd760 != null">#{vd760},</if>
            <if test="vd764 != null">#{vd764},</if>
            <if test="vd904 != null">#{vd904},</if>
            <if test="vd912 != null">#{vd912},</if>
            <if test="vd916 != null">#{vd916},</if>
            <if test="vd928 != null">#{vd928},</if>
            <if test="vd936 != null">#{vd936},</if>
            <if test="vd940 != null">#{vd940},</if>
            <if test="vd952 != null">#{vd952},</if>
            <if test="vd960 != null">#{vd960},</if>
            <if test="vd964 != null">#{vd964},</if>
            <if test="vd4020 != null">#{vd4020},</if>
            <if test="vd4028 != null">#{vd4028},</if>
         </trim>
    </insert>

    <insert id="insertBatch">
        insert into sc_requirement_report (
            b_key, topic, gateway_id,
            i16_0, i16_1, i16_2, i16_3, i16_4, i16_5, i16_6, i16_7,
            i17_0, i17_1, i17_2, i17_3, i17_4,
            i20_0, i20_1, i20_2, i20_3, i20_4, i20_5, i20_6,
            i21_0, i21_1, i21_2, i21_3, i21_4,
            vd200, vd230, vd260, vd2068, vd404, vd408,
            vd704, vd712, vd716, vd720, vd728, vd736, vd740,
            vd744, vd752, vd760, vd764,
            vd904, vd912, vd916, vd928, vd936, vd940, vd952, vd960, vd964, vd4020, vd4028,create_time
        ) values
        <foreach collection="list" item="it" separator=",">
            (
                #{it.key}, #{it.topic}, #{it.gatewayId},
                #{it.i160}, #{it.i161}, #{it.i162}, #{it.i163}, #{it.i164}, #{it.i165}, #{it.i166}, #{it.i167},
                #{it.i170}, #{it.i171}, #{it.i172}, #{it.i173}, #{it.i174},
                #{it.i200}, #{it.i201}, #{it.i202}, #{it.i203}, #{it.i204}, #{it.i205}, #{it.i206},
                #{it.i210}, #{it.i211}, #{it.i212}, #{it.i213}, #{it.i214},
                #{it.vd200}, #{it.vd230}, #{it.vd260}, #{it.vd2068}, #{it.vd404}, #{it.vd408},
                #{it.vd704}, #{it.vd712}, #{it.vd716}, #{it.vd720}, #{it.vd728}, #{it.vd736}, #{it.vd740},
                #{it.vd744}, #{it.vd752}, #{it.vd760}, #{it.vd764},
                #{it.vd904}, #{it.vd912}, #{it.vd916}, #{it.vd928}, #{it.vd936}, #{it.vd940}, #{it.vd952}, #{it.vd960}, #{it.vd964}, #{it.vd4020}, #{it.vd4028}, #{it.createTime}
            )
        </foreach>
    </insert>

    <update id="updateRequirementReport" parameterType="com.tunnel.domain.RequirementReport">
        update sc_requirement_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="key != null and key != ''">b_key = #{key},</if>
            <if test="topic != null  and topic != ''">topic = #{topic},</if>
            <if test="i160 != null">i16_0 = #{i160},</if>
            <if test="i161 != null">i16_1 = #{i161},</if>
            <if test="i162 != null">i16_2 = #{i162},</if>
            <if test="i163 != null">i16_3 = #{i163},</if>
            <if test="i164 != null">i16_4 = #{i164},</if>
            <if test="i165 != null">i16_5 = #{i165},</if>
            <if test="i166 != null">i16_6 = #{i166},</if>
            <if test="i167 != null">i16_7 = #{i167},</if>
            <if test="i170 != null">i17_0 = #{i170},</if>
            <if test="i171 != null">i17_1 = #{i171},</if>
            <if test="i172 != null">i17_2 = #{i172},</if>
            <if test="i173 != null">i17_3 = #{i173},</if>
            <if test="i174 != null">i17_4 = #{i174},</if>
            <if test="i200 != null">i20_0 = #{i200},</if>
            <if test="i201 != null">i20_1 = #{i201},</if>
            <if test="i202 != null">i20_2 = #{i202},</if>
            <if test="i203 != null">i20_3 = #{i203},</if>
            <if test="i204 != null">i20_4 = #{i204},</if>
            <if test="i205 != null">i20_5 = #{i205},</if>
            <if test="i206 != null">i20_6 = #{i206},</if>
            <if test="i210 != null">i21_0 = #{i210},</if>
            <if test="i211 != null">i21_1 = #{i211},</if>
            <if test="i212 != null">i21_2 = #{i212},</if>
            <if test="i213 != null">i21_3 = #{i213},</if>
            <if test="i214 != null">i21_4 = #{i214},</if>
            <if test="vd200 != null">vd200 = #{vd200},</if>
            <if test="vd230 != null">vd230 = #{vd230},</if>
            <if test="vd260 != null">vd260 = #{vd260},</if>
            <if test="vd2068 != null">vd2068 = #{vd2068},</if>
            <if test="vd404 != null">vd404 = #{vd404},</if>
            <if test="vd408 != null">vd408 = #{vd408},</if>
            <if test="vd704 != null">vd704 = #{vd704},</if>
            <if test="vd712 != null">vd712 = #{vd712},</if>
            <if test="vd716 != null">vd716 = #{vd716},</if>
            <if test="vd720 != null">vd720 = #{vd720},</if>
            <if test="vd728 != null">vd728 = #{vd728},</if>
            <if test="vd736 != null">vd736 = #{vd736},</if>
            <if test="vd740 != null">vd740 = #{vd740},</if>
            <if test="vd744 != null">vd744 = #{vd744},</if>
            <if test="vd752 != null">vd752 = #{vd752},</if>
            <if test="vd760 != null">vd760 = #{vd760},</if>
            <if test="vd764 != null">vd764 = #{vd764},</if>
            <if test="vd904 != null">vd904 = #{vd904},</if>
            <if test="vd912 != null">vd912 = #{vd912},</if>
            <if test="vd916 != null">vd916 = #{vd916},</if>
            <if test="vd928 != null">vd928 = #{vd928},</if>
            <if test="vd936 != null">vd936 = #{vd936},</if>
            <if test="vd940 != null">vd940 = #{vd940},</if>
            <if test="vd952 != null">vd952 = #{vd952},</if>
            <if test="vd960 != null">vd960 = #{vd960},</if>
            <if test="vd964 != null">vd964 = #{vd964},</if>
            <if test="vd4020 != null">vd4020 = #{vd4020},</if>
            <if test="vd4028 != null">vd4028 = #{vd4028},</if>
            <choose>
                <when test="updateTime != null">update_time = #{updateTime},</when>
                <otherwise>update_time = now(),</otherwise>
            </choose>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRequirementReportById" parameterType="String">
        delete from sc_requirement_report where id = #{id}
    </delete>

    <delete id="deleteRequirementReportByIds" parameterType="String">
        delete from sc_requirement_report where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="listByKey" resultMap="RequirementReportResult">
        <include refid="selectRequirementReportVo"/>
        where b_key = #{key}
    </select>

    <select id="getAllFields" resultType="java.util.Map">
        SELECT
            COLUMN_NAME as fieldName,
            COLUMN_COMMENT as fieldComment
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = (SELECT DATABASE())
        AND TABLE_NAME = 'sc_requirement_report'
        AND COLUMN_NAME NOT IN ('id','gateway_id','b_key','topic', 'remark', 'create_time', 'update_time', 'creator', 'modifier')
        AND COLUMN_COMMENT IS NOT NULL
        AND COLUMN_COMMENT != ''
        ORDER BY ORDINAL_POSITION
    </select>
    <select id="selectTimeRange" resultType="com.tunnel.domain.CheckDTO">
        select max(create_time) endTime
        from sc_requirement_report where  gateway_id = #{gatewayId}
    </select>
    <select id="selectByParams" resultType="java.util.Map">
        select s.*
        from sc_requirement_report s
        where create_time between #{startTime} and  #{endTime}
          and gateway_id = #{gatewayId}
        order by create_time desc
    </select>
</mapper>