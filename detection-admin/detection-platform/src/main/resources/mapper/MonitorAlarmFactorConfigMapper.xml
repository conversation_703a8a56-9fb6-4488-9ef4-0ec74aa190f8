<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorAlarmFactorConfigMapper">
    
    <resultMap type="com.tunnel.domain.MonitorAlarmFactorConfig" id="MonitorAlarmConfigResult">
        <result property="id"    column="id"    />
        <result property="alarmEnabled"    column="alarm_enabled"    />
        <result property="alarmLimit"    column="alarm_limit"    />
        <result property="factorId"    column="factor_id"    />
        <result property="serviceId"    column="service_id"    />
        <result property="systemCode"    column="system_code"    />
        <result property="systemName"    column="system_name"    />
        <result property="factorName"    column="factor_name"    />
        <result property="max"    column="max"    />
        <result property="min"    column="min"    />
        <result property="serviceArea"    column="service_area"    />
        <result property="name"    column="name"    />
        <result property="emailAddress"    column="email_address"    />
        <result property="emailEnabled"    column="email_enabled"    />
        <result property="lastTime"    column="last_time"    />
        <result property="messageEnabled"    column="message_enabled"    />
        <result property="phoneNum"    column="phone_num"    />
        <result property="timeInterval"    column="time_interval"    />
        <result property="lastMessageTime"    column="last_message_time"    />
        <result property="messageTimeInterval"    column="message_time_interval"    />
        <result property="floorAlarmLimit"    column="floor_alarm_limit"    />
        <result property="pairAlarm"    column="pair_alarm"    />
        <result property="pollutionType"    column="pollution_type"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, alarm_enabled, alarm_limit, factor_id, service_id, system_code, system_name, factor_name, max, min, service_area, name, email_address, email_enabled, last_time, message_enabled, phone_num, time_interval, last_message_time, message_time_interval, floor_alarm_limit, pair_alarm, pollution_type, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorAlarmConfigList" parameterType="com.tunnel.domain.MonitorAlarmFactorConfig" resultMap="MonitorAlarmConfigResult">
        select
        <include refid="allColumn"/>
        from <include refid="tableName"/>
        <where>
            <if test="alarmEnabled != null "> and alarm_enabled = #{alarmEnabled}</if>
            <if test="alarmLimit != null "> and alarm_limit = #{alarmLimit}</if>
            <if test="factorId != null  and factorId != ''"> and factor_id = #{factorId}</if>
            <if test="serviceId != null  and serviceId != ''"> and service_id = #{serviceId}</if>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="factorName != null  and factorName != ''"> and factor_name like concat('%', #{factorName}, '%')</if>
            <if test="max != null "> and max = #{max}</if>
            <if test="min != null "> and min = #{min}</if>
            <if test="serviceArea != null  and serviceArea != ''"> and service_area = #{serviceArea}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="emailAddress != null  and emailAddress != ''"> and email_address = #{emailAddress}</if>
            <if test="emailEnabled != null "> and email_enabled = #{emailEnabled}</if>
            <if test="lastTime != null  and lastTime != ''"> and last_time = #{lastTime}</if>
            <if test="messageEnabled != null "> and message_enabled = #{messageEnabled}</if>
            <if test="phoneNum != null  and phoneNum != ''"> and phone_num = #{phoneNum}</if>
            <if test="timeInterval != null "> and time_interval = #{timeInterval}</if>
            <if test="lastMessageTime != null  and lastMessageTime != ''"> and last_message_time = #{lastMessageTime}</if>
            <if test="messageTimeInterval != null "> and message_time_interval = #{messageTimeInterval}</if>
            <if test="floorAlarmLimit != null "> and floor_alarm_limit = #{floorAlarmLimit}</if>
            <if test="pairAlarm != null "> and pair_alarm = #{pairAlarm}</if>
            <if test="pollutionType != null  and pollutionType != ''"> and pollution_type = #{pollutionType}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="modifier != null "> and modifier = #{modifier}</if>
        </where>
    </select>
    
    <select id="selectMonitorAlarmConfigById" parameterType="Long" resultMap="MonitorAlarmConfigResult">
        select <include refid="allColumn"/>
        from <include refid="tableName"/>
        where id = #{id}
    </select>
        
    <insert id="insertMonitorAlarmConfig" parameterType="com.tunnel.domain.MonitorAlarmFactorConfig" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmEnabled != null">alarm_enabled,</if>
            <if test="alarmLimit != null">alarm_limit,</if>
            <if test="factorId != null">factor_id,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="systemCode != null">system_code,</if>
            <if test="systemName != null">system_name,</if>
            <if test="factorName != null">factor_name,</if>
            <if test="max != null">max,</if>
            <if test="min != null">min,</if>
            <if test="serviceArea != null">service_area,</if>
            <if test="name != null">name,</if>
            <if test="emailAddress != null">email_address,</if>
            <if test="emailEnabled != null">email_enabled,</if>
            <if test="lastTime != null">last_time,</if>
            <if test="messageEnabled != null">message_enabled,</if>
            <if test="phoneNum != null">phone_num,</if>
            <if test="timeInterval != null">time_interval,</if>
            <if test="lastMessageTime != null">last_message_time,</if>
            <if test="messageTimeInterval != null">message_time_interval,</if>
            <if test="floorAlarmLimit != null">floor_alarm_limit,</if>
            <if test="pairAlarm != null">pair_alarm,</if>
            <if test="pollutionType != null">pollution_type,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmEnabled != null">#{alarmEnabled},</if>
            <if test="alarmLimit != null">#{alarmLimit},</if>
            <if test="factorId != null">#{factorId},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="systemCode != null">#{systemCode},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="factorName != null">#{factorName},</if>
            <if test="max != null">#{max},</if>
            <if test="min != null">#{min},</if>
            <if test="serviceArea != null">#{serviceArea},</if>
            <if test="name != null">#{name},</if>
            <if test="emailAddress != null">#{emailAddress},</if>
            <if test="emailEnabled != null">#{emailEnabled},</if>
            <if test="lastTime != null">#{lastTime},</if>
            <if test="messageEnabled != null">#{messageEnabled},</if>
            <if test="phoneNum != null">#{phoneNum},</if>
            <if test="timeInterval != null">#{timeInterval},</if>
            <if test="lastMessageTime != null">#{lastMessageTime},</if>
            <if test="messageTimeInterval != null">#{messageTimeInterval},</if>
            <if test="floorAlarmLimit != null">#{floorAlarmLimit},</if>
            <if test="pairAlarm != null">#{pairAlarm},</if>
            <if test="pollutionType != null">#{pollutionType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateMonitorAlarmConfig" parameterType="com.tunnel.domain.MonitorAlarmFactorConfig">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmEnabled != null">alarm_enabled = #{alarmEnabled},</if>
            <if test="alarmLimit != null">alarm_limit = #{alarmLimit},</if>
            <if test="factorId != null">factor_id = #{factorId},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="systemCode != null">system_code = #{systemCode},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="factorName != null">factor_name = #{factorName},</if>
            <if test="max != null">max = #{max},</if>
            <if test="min != null">min = #{min},</if>
            <if test="serviceArea != null">service_area = #{serviceArea},</if>
            <if test="name != null">name = #{name},</if>
            <if test="emailAddress != null">email_address = #{emailAddress},</if>
            <if test="emailEnabled != null">email_enabled = #{emailEnabled},</if>
            <if test="lastTime != null">last_time = #{lastTime},</if>
            <if test="messageEnabled != null">message_enabled = #{messageEnabled},</if>
            <if test="phoneNum != null">phone_num = #{phoneNum},</if>
            <if test="timeInterval != null">time_interval = #{timeInterval},</if>
            <if test="lastMessageTime != null">last_message_time = #{lastMessageTime},</if>
            <if test="messageTimeInterval != null">message_time_interval = #{messageTimeInterval},</if>
            <if test="floorAlarmLimit != null">floor_alarm_limit = #{floorAlarmLimit},</if>
            <if test="pairAlarm != null">pair_alarm = #{pairAlarm},</if>
            <if test="pollutionType != null">pollution_type = #{pollutionType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorAlarmConfigById" parameterType="Long">
        delete from <include refid="tableName"/>
                   where id = #{id}
    </delete>

    <delete id="deleteMonitorAlarmConfigByIds" parameterType="String">
        delete from <include refid="tableName"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_alarm_factor_config
    </sql>
</mapper>