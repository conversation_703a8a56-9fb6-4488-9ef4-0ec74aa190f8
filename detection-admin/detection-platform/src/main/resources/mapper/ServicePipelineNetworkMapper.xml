<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServicePipelineNetworkMapper">
    
    <resultMap type="ServicePipelineNetwork" id="ServicePipelineNetworkResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="areaType"    column="area_type"    />
        <result property="rainSewageSeparation"    column="rain_sewage_separation"    />
        <result property="pipeDiameter"    column="pipe_diameter"    />
        <result property="pipeMaterial"    column="pipe_material"    />
        <result property="operationStatus"    column="operation_status"    />
        <result property="otherConditions"    column="other_conditions"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServicePipelineNetworkVo">
        select id, service_area_id, area_type, rain_sewage_separation, pipe_diameter, pipe_material, operation_status, other_conditions, remark, create_time, update_time, creator, modifier from sc_service_pipeline_network
    </sql>

    <select id="selectServicePipelineNetworkList" parameterType="ServicePipelineNetwork" resultMap="ServicePipelineNetworkResult">
        <include refid="selectServicePipelineNetworkVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="rainSewageSeparation != null  and rainSewageSeparation != ''"> and rain_sewage_separation like concat('%', #{rainSewageSeparation}, '%')</if>
            <if test="pipeMaterial != null  and pipeMaterial != ''"> and pipe_material like concat('%', #{pipeMaterial}, '%')</if>
            <if test="operationStatus != null  and operationStatus != ''"> and operation_status like concat('%', #{operationStatus}, '%')</if>
        </where>
    </select>
    
    <select id="selectServicePipelineNetworkById" parameterType="Long" resultMap="ServicePipelineNetworkResult">
        <include refid="selectServicePipelineNetworkVo"/>
        where id = #{id}
    </select>

    <select id="selectServicePipelineNetworkByServiceAreaId" parameterType="Long" resultMap="ServicePipelineNetworkResult">
        <include refid="selectServicePipelineNetworkVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServicePipelineNetwork" parameterType="ServicePipelineNetwork" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_pipeline_network
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="rainSewageSeparation != null">rain_sewage_separation,</if>
            <if test="pipeDiameter != null">pipe_diameter,</if>
            <if test="pipeMaterial != null">pipe_material,</if>
            <if test="operationStatus != null">operation_status,</if>
            <if test="otherConditions != null">other_conditions,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="rainSewageSeparation != null">#{rainSewageSeparation},</if>
            <if test="pipeDiameter != null">#{pipeDiameter},</if>
            <if test="pipeMaterial != null">#{pipeMaterial},</if>
            <if test="operationStatus != null">#{operationStatus},</if>
            <if test="otherConditions != null">#{otherConditions},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServicePipelineNetwork" parameterType="ServicePipelineNetwork">
        update sc_service_pipeline_network
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="rainSewageSeparation != null">rain_sewage_separation = #{rainSewageSeparation},</if>
            <if test="pipeDiameter != null">pipe_diameter = #{pipeDiameter},</if>
            <if test="pipeMaterial != null">pipe_material = #{pipeMaterial},</if>
            <if test="operationStatus != null">operation_status = #{operationStatus},</if>
            <if test="otherConditions != null">other_conditions = #{otherConditions},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServicePipelineNetworkById" parameterType="Long">
        delete from sc_service_pipeline_network where id = #{id}
    </delete>

    <delete id="deleteServicePipelineNetworkByIds" parameterType="String">
        delete from sc_service_pipeline_network where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServicePipelineNetworkByServiceAreaId" parameterType="Long">
        delete from sc_service_pipeline_network where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
