<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceSecondarySettlingTankInfoMapper">
    
    <resultMap type="ServiceSecondarySettlingTankInfo" id="ServiceSecondarySettlingTankInfoResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="areaType"    column="area_type"    />
        <result property="fillerType"    column="filler_type"    />
        <result property="fillerStatus"    column="filler_status"    />
        <result property="processParameters"    column="process_parameters"    />
        <result property="sludgeRefluxStatus"    column="sludge_reflux_status"    />
        <result property="floatingSludgeStatus"    column="floating_sludge_status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceSecondarySettlingTankInfoVo">
        select id, service_area_id, area_type, filler_type, filler_status, process_parameters, sludge_reflux_status, floating_sludge_status, remark, create_time, update_time, creator, modifier from sc_service_secondary_settling_tank_info
    </sql>

    <select id="selectServiceSecondarySettlingTankInfoList" parameterType="ServiceSecondarySettlingTankInfo" resultMap="ServiceSecondarySettlingTankInfoResult">
        <include refid="selectServiceSecondarySettlingTankInfoVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="fillerType != null  and fillerType != ''"> and filler_type like concat('%', #{fillerType}, '%')</if>
            <if test="fillerStatus != null  and fillerStatus != ''"> and filler_status like concat('%', #{fillerStatus}, '%')</if>
        </where>
    </select>
    
    <select id="selectServiceSecondarySettlingTankInfoById" parameterType="Long" resultMap="ServiceSecondarySettlingTankInfoResult">
        <include refid="selectServiceSecondarySettlingTankInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceSecondarySettlingTankInfoByServiceAreaId" parameterType="Long" resultMap="ServiceSecondarySettlingTankInfoResult">
        <include refid="selectServiceSecondarySettlingTankInfoVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceSecondarySettlingTankInfo" parameterType="ServiceSecondarySettlingTankInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_secondary_settling_tank_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="fillerType != null">filler_type,</if>
            <if test="fillerStatus != null">filler_status,</if>
            <if test="processParameters != null">process_parameters,</if>
            <if test="sludgeRefluxStatus != null">sludge_reflux_status,</if>
            <if test="floatingSludgeStatus != null">floating_sludge_status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="fillerType != null">#{fillerType},</if>
            <if test="fillerStatus != null">#{fillerStatus},</if>
            <if test="processParameters != null">#{processParameters},</if>
            <if test="sludgeRefluxStatus != null">#{sludgeRefluxStatus},</if>
            <if test="floatingSludgeStatus != null">#{floatingSludgeStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceSecondarySettlingTankInfo" parameterType="ServiceSecondarySettlingTankInfo">
        update sc_service_secondary_settling_tank_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="fillerType != null">filler_type = #{fillerType},</if>
            <if test="fillerStatus != null">filler_status = #{fillerStatus},</if>
            <if test="processParameters != null">process_parameters = #{processParameters},</if>
            <if test="sludgeRefluxStatus != null">sludge_reflux_status = #{sludgeRefluxStatus},</if>
            <if test="floatingSludgeStatus != null">floating_sludge_status = #{floatingSludgeStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceSecondarySettlingTankInfoById" parameterType="Long">
        delete from sc_service_secondary_settling_tank_info where id = #{id}
    </delete>

    <delete id="deleteServiceSecondarySettlingTankInfoByIds" parameterType="String">
        delete from sc_service_secondary_settling_tank_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceSecondarySettlingTankInfoByServiceAreaId" parameterType="Long">
        delete from sc_service_secondary_settling_tank_info where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
