<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceBaseInfoMapper">
    
    <resultMap type="ServiceBaseInfo" id="ServiceBaseInfoResult">
        <result property="id"    column="id"    />
        <result property="facilityType"    column="facility_type"    />
        <result property="facilityName"    column="facility_name"    />
        <result property="fileNumber"    column="file_number"    />
        <result property="branchCompany"    column="branch_company"    />
        <result property="highwayName"    column="highway_name"    />
        <result property="constructionYear"    column="construction_year"    />
        <result property="hasInterchange"    column="has_interchange"    />
        <result property="buildingComposition"    column="building_composition"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceBaseInfoVo">
        select id, facility_type, facility_name, file_number, branch_company, highway_name, construction_year, has_interchange, building_composition, remark, create_time, update_time, creator, modifier from sc_service_base_info
    </sql>

    <select id="selectServiceBaseInfoList" parameterType="ServiceBaseInfo" resultMap="ServiceBaseInfoResult">
        <include refid="selectServiceBaseInfoVo"/>
        <where>  
            <if test="facilityType != null "> and facility_type = #{facilityType}</if>
            <if test="facilityName != null  and facilityName != ''"> and facility_name like concat('%', #{facilityName}, '%')</if>
            <if test="fileNumber != null  and fileNumber != ''"> and file_number = #{fileNumber}</if>
            <if test="branchCompany != null  and branchCompany != ''"> and branch_company like concat('%', #{branchCompany}, '%')</if>
            <if test="highwayName != null  and highwayName != ''"> and highway_name like concat('%', #{highwayName}, '%')</if>
            <if test="constructionYear != null "> and construction_year = #{constructionYear}</if>
            <if test="hasInterchange != null "> and has_interchange = #{hasInterchange}</if>
        </where>
    </select>
    
    <select id="selectServiceBaseInfoById" parameterType="Long" resultMap="ServiceBaseInfoResult">
        <include refid="selectServiceBaseInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceBaseInfoByName" parameterType="String" resultMap="ServiceBaseInfoResult">
        <include refid="selectServiceBaseInfoVo"/>
        where facility_name = #{facilityName}
    </select>

    <select id="countServiceBaseInfo" parameterType="ServiceBaseInfo" resultType="int">
        select count(*) from sc_service_base_info
        <where>  
            <if test="facilityType != null "> and facility_type = #{facilityType}</if>
            <if test="facilityName != null  and facilityName != ''"> and facility_name like concat('%', #{facilityName}, '%')</if>
            <if test="fileNumber != null  and fileNumber != ''"> and file_number = #{fileNumber}</if>
            <if test="branchCompany != null  and branchCompany != ''"> and branch_company like concat('%', #{branchCompany}, '%')</if>
            <if test="highwayName != null  and highwayName != ''"> and highway_name like concat('%', #{highwayName}, '%')</if>
            <if test="constructionYear != null "> and construction_year = #{constructionYear}</if>
            <if test="hasInterchange != null "> and has_interchange = #{hasInterchange}</if>
        </where>
    </select>
        
    <insert id="insertServiceBaseInfo" parameterType="ServiceBaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="facilityType != null">facility_type,</if>
            <if test="facilityName != null and facilityName != ''">facility_name,</if>
            <if test="fileNumber != null">file_number,</if>
            <if test="branchCompany != null">branch_company,</if>
            <if test="highwayName != null">highway_name,</if>
            <if test="constructionYear != null">construction_year,</if>
            <if test="hasInterchange != null">has_interchange,</if>
            <if test="buildingComposition != null">building_composition,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="facilityType != null">#{facilityType},</if>
            <if test="facilityName != null and facilityName != ''">#{facilityName},</if>
            <if test="fileNumber != null">#{fileNumber},</if>
            <if test="branchCompany != null">#{branchCompany},</if>
            <if test="highwayName != null">#{highwayName},</if>
            <if test="constructionYear != null">#{constructionYear},</if>
            <if test="hasInterchange != null">#{hasInterchange},</if>
            <if test="buildingComposition != null">#{buildingComposition},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceBaseInfo" parameterType="ServiceBaseInfo">
        update sc_service_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="facilityType != null">facility_type = #{facilityType},</if>
            <if test="facilityName != null and facilityName != ''">facility_name = #{facilityName},</if>
            <if test="fileNumber != null">file_number = #{fileNumber},</if>
            <if test="branchCompany != null">branch_company = #{branchCompany},</if>
            <if test="highwayName != null">highway_name = #{highwayName},</if>
            <if test="constructionYear != null">construction_year = #{constructionYear},</if>
            <if test="hasInterchange != null">has_interchange = #{hasInterchange},</if>
            <if test="buildingComposition != null">building_composition = #{buildingComposition},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceBaseInfoById" parameterType="Long">
        delete from sc_service_base_info where id = #{id}
    </delete>

    <delete id="deleteServiceBaseInfoByIds" parameterType="String">
        delete from sc_service_base_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
