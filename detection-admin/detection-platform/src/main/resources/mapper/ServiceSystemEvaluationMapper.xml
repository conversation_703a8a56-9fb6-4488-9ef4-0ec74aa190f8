<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceSystemEvaluationMapper">
    
    <resultMap type="ServiceSystemEvaluation" id="ServiceSystemEvaluationResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="evaluationContent"    column="evaluation_content"    />
        <result property="evaluationDate"    column="evaluation_date"    />
        <result property="evaluator"    column="evaluator"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceSystemEvaluationVo">
        select id, service_area_id, evaluation_content, evaluation_date, evaluator, remark, create_time, update_time, creator, modifier from sc_service_system_evaluation
    </sql>

    <select id="selectServiceSystemEvaluationList" parameterType="ServiceSystemEvaluation" resultMap="ServiceSystemEvaluationResult">
        <include refid="selectServiceSystemEvaluationVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="evaluationDate != null "> and evaluation_date = #{evaluationDate}</if>
            <if test="evaluator != null  and evaluator != ''"> and evaluator like concat('%', #{evaluator}, '%')</if>
        </where>
    </select>
    
    <select id="selectServiceSystemEvaluationById" parameterType="Long" resultMap="ServiceSystemEvaluationResult">
        <include refid="selectServiceSystemEvaluationVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceSystemEvaluationByServiceAreaId" parameterType="Long" resultMap="ServiceSystemEvaluationResult">
        <include refid="selectServiceSystemEvaluationVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceSystemEvaluation" parameterType="ServiceSystemEvaluation" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_system_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="evaluationContent != null">evaluation_content,</if>
            <if test="evaluationDate != null">evaluation_date,</if>
            <if test="evaluator != null">evaluator,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="evaluationContent != null">#{evaluationContent},</if>
            <if test="evaluationDate != null">#{evaluationDate},</if>
            <if test="evaluator != null">#{evaluator},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceSystemEvaluation" parameterType="ServiceSystemEvaluation">
        update sc_service_system_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="evaluationContent != null">evaluation_content = #{evaluationContent},</if>
            <if test="evaluationDate != null">evaluation_date = #{evaluationDate},</if>
            <if test="evaluator != null">evaluator = #{evaluator},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceSystemEvaluationById" parameterType="Long">
        delete from sc_service_system_evaluation where id = #{id}
    </delete>

    <delete id="deleteServiceSystemEvaluationByIds" parameterType="String">
        delete from sc_service_system_evaluation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceSystemEvaluationByServiceAreaId" parameterType="Long">
        delete from sc_service_system_evaluation where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
