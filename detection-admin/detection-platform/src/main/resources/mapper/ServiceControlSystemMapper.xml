<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceControlSystemMapper">
    
    <resultMap type="ServiceControlSystem" id="ServiceControlSystemResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="area"    column="area"    />
        <result property="hasPlc"    column="has_plc"    />
        <result property="plcStatus"    column="plc_status"    />
        <result property="controlMethod"    column="control_method"    />
        <result property="electricalComponents"    column="electrical_components"    />
        <result property="otherSituation"    column="other_situation"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectServiceControlSystemVo">
        select id, service_area_id, area, has_plc, plc_status, control_method, electrical_components, other_situation, create_time, update_time from sc_service_control_system
    </sql>

    <select id="selectServiceControlSystemList" parameterType="ServiceControlSystem" resultMap="ServiceControlSystemResult">
        <include refid="selectServiceControlSystemVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="area != null  and area != ''"> and area like concat('%', #{area}, '%')</if>
            <if test="hasPlc != null  and hasPlc != ''"> and has_plc = #{hasPlc}</if>
            <if test="plcStatus != null  and plcStatus != ''"> and plc_status like concat('%', #{plcStatus}, '%')</if>
            <if test="controlMethod != null  and controlMethod != ''"> and control_method like concat('%', #{controlMethod}, '%')</if>
            <if test="electricalComponents != null  and electricalComponents != ''"> and electrical_components like concat('%', #{electricalComponents}, '%')</if>
            <if test="otherSituation != null  and otherSituation != ''"> and other_situation like concat('%', #{otherSituation}, '%')</if>
        </where>
    </select>
    
    <select id="selectServiceControlSystemById" parameterType="Long" resultMap="ServiceControlSystemResult">
        <include refid="selectServiceControlSystemVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceControlSystemByServiceAreaId" parameterType="Long" resultMap="ServiceControlSystemResult">
        <include refid="selectServiceControlSystemVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceControlSystem" parameterType="ServiceControlSystem" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_control_system
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="area != null and area != ''">area,</if>
            <if test="hasPlc != null and hasPlc != ''">has_plc,</if>
            <if test="plcStatus != null and plcStatus != ''">plc_status,</if>
            <if test="controlMethod != null and controlMethod != ''">control_method,</if>
            <if test="electricalComponents != null and electricalComponents != ''">electrical_components,</if>
            <if test="otherSituation != null and otherSituation != ''">other_situation,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="area != null and area != ''">#{area},</if>
            <if test="hasPlc != null and hasPlc != ''">#{hasPlc},</if>
            <if test="plcStatus != null and plcStatus != ''">#{plcStatus},</if>
            <if test="controlMethod != null and controlMethod != ''">#{controlMethod},</if>
            <if test="electricalComponents != null and electricalComponents != ''">#{electricalComponents},</if>
            <if test="otherSituation != null and otherSituation != ''">#{otherSituation},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateServiceControlSystem" parameterType="ServiceControlSystem">
        update sc_service_control_system
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            <if test="hasPlc != null and hasPlc != ''">has_plc = #{hasPlc},</if>
            <if test="plcStatus != null and plcStatus != ''">plc_status = #{plcStatus},</if>
            <if test="controlMethod != null and controlMethod != ''">control_method = #{controlMethod},</if>
            <if test="electricalComponents != null and electricalComponents != ''">electrical_components = #{electricalComponents},</if>
            <if test="otherSituation != null and otherSituation != ''">other_situation = #{otherSituation},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceControlSystemById" parameterType="Long">
        delete from sc_service_control_system where id = #{id}
    </delete>

    <delete id="deleteServiceControlSystemByIds" parameterType="String">
        delete from sc_service_control_system where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceControlSystemByServiceAreaId" parameterType="Long">
        delete from sc_service_control_system where service_area_id = #{serviceAreaId}
    </delete>

</mapper>
