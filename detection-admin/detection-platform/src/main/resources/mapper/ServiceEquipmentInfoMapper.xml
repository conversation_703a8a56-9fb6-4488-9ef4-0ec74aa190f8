<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.ServiceEquipmentInfoMapper">
    
    <resultMap type="ServiceEquipmentInfo" id="ServiceEquipmentInfoResult">
        <result property="id"    column="id"    />
        <result property="serviceAreaId"    column="service_area_id"    />
        <result property="areaType"    column="area_type"    />
        <result property="equipmentCategory"    column="equipment_category"    />
        <result property="equipmentType"    column="equipment_type"    />
        <result property="location"    column="location"    />
        <result property="quantity"    column="quantity"    />
        <result property="manufacturerModel"    column="manufacturer_model"    />
        <result property="equipmentParameters"    column="equipment_parameters"    />
        <result property="operationStatus"    column="operation_status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectServiceEquipmentInfoVo">
        select id, service_area_id, area_type, equipment_category, equipment_type, location, quantity, manufacturer_model, equipment_parameters, operation_status, remark, create_time, update_time, creator, modifier from sc_service_equipment_info
    </sql>

    <select id="selectServiceEquipmentInfoList" parameterType="ServiceEquipmentInfo" resultMap="ServiceEquipmentInfoResult">
        <include refid="selectServiceEquipmentInfoVo"/>
        <where>  
            <if test="serviceAreaId != null "> and service_area_id = #{serviceAreaId}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="equipmentCategory != null  and equipmentCategory != ''"> and equipment_category like concat('%', #{equipmentCategory}, '%')</if>
            <if test="equipmentType != null  and equipmentType != ''"> and equipment_type like concat('%', #{equipmentType}, '%')</if>
            <if test="location != null  and location != ''"> and location like concat('%', #{location}, '%')</if>
            <if test="operationStatus != null  and operationStatus != ''"> and operation_status = #{operationStatus}</if>
        </where>
    </select>
    
    <select id="selectServiceEquipmentInfoById" parameterType="Long" resultMap="ServiceEquipmentInfoResult">
        <include refid="selectServiceEquipmentInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceEquipmentInfoByServiceAreaId" parameterType="Long" resultMap="ServiceEquipmentInfoResult">
        <include refid="selectServiceEquipmentInfoVo"/>
        where service_area_id = #{serviceAreaId}
    </select>
        
    <insert id="insertServiceEquipmentInfo" parameterType="ServiceEquipmentInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sc_service_equipment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="equipmentCategory != null and equipmentCategory != ''">equipment_category,</if>
            <if test="equipmentType != null">equipment_type,</if>
            <if test="location != null">location,</if>
            <if test="quantity != null">quantity,</if>
            <if test="manufacturerModel != null">manufacturer_model,</if>
            <if test="equipmentParameters != null">equipment_parameters,</if>
            <if test="operationStatus != null">operation_status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceAreaId != null">#{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="equipmentCategory != null and equipmentCategory != ''">#{equipmentCategory},</if>
            <if test="equipmentType != null">#{equipmentType},</if>
            <if test="location != null">#{location},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="manufacturerModel != null">#{manufacturerModel},</if>
            <if test="equipmentParameters != null">#{equipmentParameters},</if>
            <if test="operationStatus != null">#{operationStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateServiceEquipmentInfo" parameterType="ServiceEquipmentInfo">
        update sc_service_equipment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceAreaId != null">service_area_id = #{serviceAreaId},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="equipmentCategory != null and equipmentCategory != ''">equipment_category = #{equipmentCategory},</if>
            <if test="equipmentType != null">equipment_type = #{equipmentType},</if>
            <if test="location != null">location = #{location},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="manufacturerModel != null">manufacturer_model = #{manufacturerModel},</if>
            <if test="equipmentParameters != null">equipment_parameters = #{equipmentParameters},</if>
            <if test="operationStatus != null">operation_status = #{operationStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceEquipmentInfoById" parameterType="Long">
        delete from sc_service_equipment_info where id = #{id}
    </delete>

    <delete id="deleteServiceEquipmentInfoByIds" parameterType="String">
        delete from sc_service_equipment_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceEquipmentInfoByServiceAreaId" parameterType="Long">
        delete from sc_service_equipment_info where service_area_id = #{serviceAreaId}
    </delete>
</mapper>
