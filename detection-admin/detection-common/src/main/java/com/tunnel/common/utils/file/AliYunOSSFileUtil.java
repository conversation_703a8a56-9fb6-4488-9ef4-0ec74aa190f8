package com.tunnel.common.utils.file;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.OSSClient;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.PinyinUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.uuid.IdUtils;
import com.tunnel.common.utils.uuid.UUID;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Random;

/**
 * <AUTHOR>
 * @date ：Created in 2020/1/29 14:33
 * 文件上传到阿里云OSS对象存储
 */
@Slf4j
@Component
public class AliYunOSSFileUtil {

    // endpoint是访问OSS的域名。如果您已经在OSS的控制台上 创建了Bucket，请在控制台上查看域名。
    // 如果您还没有创建Bucket，endpoint选择请参看文档中心的“开发人员指南 > 基本概念 > 访问域名”，
    // 链接地址是：https://help.aliyun.com/document_detail/oss/user_guide/oss_concept/endpoint.html?spm=5176.docoss/user_guide/endpoint_region
    // endpoint的格式形如“http://oss-cn-hangzhou.aliyuncs.com/”，注意http://后不带bucket名称，
    // 比如“http://bucket-name.oss-cn-hangzhou.aliyuncs.com”，是错误的endpoint，请去掉其中的“bucket-name”。

    private static String endpoint;

    // accessKeyId和accessKeySecret是OSS的访问密钥，您可以在控制台上创建和查看，
    // 创建和查看访问密钥的链接地址是：https://ak-console.aliyun.com/#/。
    // 注意：accessKeyId和accessKeySecret前后都没有空格，从控制台复制时请检查并去除多余的空格。
    private static String accessKeyId = "LTAI5t5kYEpQA7qqQyePgXvA";
    private static String accessKeySecret = "******************************";

    // Bucket用来管理所存储Object的存储空间，详细描述请参看“开发人员指南 > 基本概念 > OSS基本概念介绍”。
    // Bucket命名规范如下：只能包括小写字母，数字和短横线（-），必须以小写字母或者数字开头，长度必须在3-63字节之间。
    private static String bucketName = "smart-highway";

    //windows系统临时文件存储路径
    private static String WINDOWS_TMP_PATH="E:\\tmp\\upload";

    //mac系统临时文件存储路径
    private static String MAC_TMP_PATH="/Users/<USER>/Downloads/files";

    //linux系统临时文件存储路径
    private static String LINUX_TMP_PATH="/tmp/files/";

    private static String OSS_PATH="https://smart-highway.oss-cn-shanghai.aliyuncs.com/";


    @Qualifier("uploadPicThread")
    @Autowired
    private ThreadPoolTaskExecutor executorService;

    @Value("${oss.endpoint}")
    public void setEndpoint(String endpoint){
        AliYunOSSFileUtil.endpoint=endpoint;
    }

    /**
     * 异步上传单个文件
     * @param file 图片
     * @param isNeedSmall 是否需要压缩文件
     */
    public JSONObject asyncUploadPicture(MultipartFile file, Integer isNeedSmall){
        //封装返回的结果集
        JSONObject jsonObject=new JSONObject();
        String systemType=System.getProperty("os.name");
        String tmpPath="";
        if(systemType.toLowerCase().contains("linux")){
            tmpPath=LINUX_TMP_PATH;
        }else if(systemType.toLowerCase().contains("mac")){
            tmpPath=MAC_TMP_PATH;
        }else {
            tmpPath=WINDOWS_TMP_PATH;
        }
        // 文件临时存放路径
        File mediaPath = new File(tmpPath);
        if (!mediaPath.exists()) {
            mediaPath.mkdirs();
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        // 生成文件名称
        String oldFileName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf("."));
        // 生成文件名称
        String nameSuffix = PinyinUtils.toPinyin(oldFileName)
                .replaceAll("\\s+", "_")
                .replaceAll("[^a-zA-Z0-9_]", "")
                + format.format(DateUtil.date())
                + new Random().nextInt(1000);
        // 文件后缀
        String fileSuffix = file.getOriginalFilename()
                .substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        // 上传文件名加后缀
        String fileName = nameSuffix + "." + fileSuffix;
        //压缩图片名称
        String compressFileName=nameSuffix+"_small"+"." + fileSuffix;
        // 转存文件
        File tmpFile=new File(mediaPath, fileName);
        try {
            file.transferTo(tmpFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        //拼装返回的数据
        jsonObject.put("compressFilePath",OSS_PATH+compressFileName);
        jsonObject.put("fileName",fileName);
        jsonObject.put("filePath",OSS_PATH+fileName);
        jsonObject.put("fileType",file.getContentType());
        jsonObject.put("fileSuffix",fileSuffix);

        String tmpPathFinal="tmpPath";
        //上传文件--异步
        executorService.execute(()->{
            uploadToOss(tmpFile,fileName,tmpPathFinal,isNeedSmall,compressFileName);
        });
        return jsonObject;


    }

    private void uploadToOss(File tmpFile,String fileName,String tmpPath,Integer isNeedSmall,String compressFileName){
        log.info("文件上传到阿里云 start");
        // 生成OSSClient，您可以指定一些参数，详见“SDK手册 > Java-SDK > 初始化”，
        OSSClient ossClient= new OSSClient(endpoint, accessKeyId, accessKeySecret);
        try{

            // 判断Bucket是否存在。详细请参看“SDK手册 > Java-SDK > 管理Bucket”。
            if (ossClient.doesBucketExist(bucketName)) {
                log.info("您已经创建Bucket：" + bucketName + "。");
            } else {
                log.info("您的Bucket不存在，创建Bucket：" + bucketName + "。");
                ossClient.createBucket(bucketName);
            }
            //上传图片
            ossClient.putObject(bucketName, fileName, tmpFile);
            //删除临时图片
            tmpFile.delete();
            //需要压缩
            if(null!=isNeedSmall && 1==isNeedSmall){

                File compressTmpFile=new File(tmpPath+compressFileName);
                // 压缩图片
                Thumbnails.of(tmpPath+fileName).size(150, 150).keepAspectRatio(false).toFile(compressTmpFile);
                //上传压缩图片
                ossClient.putObject(bucketName, compressFileName, compressTmpFile);
                compressTmpFile.delete();
            }
        }catch (Exception e) {
            log.error("文件上传到阿里云错误；",e);
        } finally {
            ossClient.shutdown();
            log.info("文件上传到阿里云 end");
        }
    }



    /**
     * 同步上传单个文件
     * @param file  图片
     * @Params  isNeedSmall 是否需要压缩文件
     * @return 返回json串包含了文件的所有信息
     */
    public static JSONObject uploadPicture(MultipartFile file, Integer isNeedSmall){
        //封装返回的结果集
        JSONObject jsonObject=new JSONObject();
        log.info("文件上传 start");
        // 生成OSSClient，您可以指定一些参数，详见“SDK手册 > Java-SDK > 初始化”，
        OSSClient ossClient= new OSSClient(endpoint, accessKeyId, accessKeySecret);
        try {
            String systemType=System.getProperty("os.name");
            String tmpPath="";
            if(systemType.toLowerCase().contains("linux")){
                tmpPath=LINUX_TMP_PATH;
            }else if(systemType.toLowerCase().contains("mac")){
                tmpPath=MAC_TMP_PATH;
            }else {
                tmpPath=WINDOWS_TMP_PATH;
            }
            // 文件临时存放路径
            File mediaPath = new File(tmpPath);
            if (!mediaPath.exists()) {
                mediaPath.mkdirs();
            }
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            // 生成文件名称
            String oldFileName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf("."));
            // 生成文件名称
            String nameSuffix = PinyinUtils.toPinyin(oldFileName)
                    .replaceAll("\\s+", "_")
                    .replaceAll("[^a-zA-Z0-9_]", "")
                    + format.format(DateUtil.date())
                    + new Random().nextInt(1000);
            // 文件后缀
            String fileSuffix = file.getOriginalFilename()
                    .substring(file.getOriginalFilename().lastIndexOf(".") + 1);
            // 上传文件名加后缀
            String fileName = nameSuffix + "." + fileSuffix;

            // 转存文件
            File tmpFile=new File(mediaPath.toString(), fileName);
            file.transferTo(tmpFile);
            // 判断Bucket是否存在。详细请参看“SDK手册 > Java-SDK > 管理Bucket”。
            if (ossClient.doesBucketExist(bucketName)) {
                log.info("您已经创建Bucket：" + bucketName + "。");
            } else {
                log.info("您的Bucket不存在，创建Bucket：" + bucketName + "。");
                ossClient.createBucket(bucketName);
            }
            //上传图片
            ossClient.putObject(bucketName, fileName, tmpFile);
            //删除临时图片
            tmpFile.delete();
            //需要压缩
            if(null!=isNeedSmall && 1==isNeedSmall){
                //压缩图片名称
                String compressFileName=nameSuffix+"_small"+"." + fileSuffix;
                File compressTmpFile=new File(tmpPath+compressFileName);
                // 压缩图片
                Thumbnails.of(tmpPath+fileName).size(150, 150).keepAspectRatio(false).toFile(compressTmpFile);
                //上传压缩图片
                ossClient.putObject(bucketName, compressFileName, compressTmpFile);
                jsonObject.put("compressFilePath",OSS_PATH+compressFileName);
                compressTmpFile.delete();
            }
            //拼装返回的数据
            jsonObject.put("fileName",fileName);
            jsonObject.put("filePath",OSS_PATH+fileName);
            jsonObject.put("fileType",file.getContentType());
            jsonObject.put("fileSuffix",fileSuffix);



            log.info("Object：" + fileName + "存入OSS成功。");
        }  catch (Exception e) {
            log.error("上传文件错误；",e);
        } finally {
            ossClient.shutdown();
            log.info("文件上传 end");
        }
        return jsonObject;
    }


    public static JSONObject uploadFile(File file){
        //封装返回的结果集
        JSONObject jsonObject=new JSONObject();
        log.info("文件上传 start");
        // 生成OSSClient，您可以指定一些参数，详见“SDK手册 > Java-SDK > 初始化”，
        OSSClient ossClient= new OSSClient(endpoint, accessKeyId, accessKeySecret);
        try {
            String systemType=System.getProperty("os.name");
            String tmpPath="";
            if(systemType.toLowerCase().contains("linux")){
                tmpPath=LINUX_TMP_PATH;
            }else if(systemType.toLowerCase().contains("mac")){
                tmpPath=MAC_TMP_PATH;
            }else {
                tmpPath=WINDOWS_TMP_PATH;
            }
            // 文件临时存放路径
            File mediaPath = new File(tmpPath);
            if (!mediaPath.exists()) {
                mediaPath.mkdirs();
            }
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            // 生成文件名称
            String nameSuffix = format.format(DateUtil.date())
                    + new Random().nextInt(1000);
            // 上传文件名加后缀
            String fileName = file.getName();

            // 判断Bucket是否存在。详细请参看“SDK手册 > Java-SDK > 管理Bucket”。
            if (ossClient.doesBucketExist(bucketName)) {
                log.info("您已经创建Bucket：" + bucketName + "。");
            } else {
                log.info("您的Bucket不存在，创建Bucket：" + bucketName + "。");
                ossClient.createBucket(bucketName);
            }
            //上传图片
            ossClient.putObject(bucketName, fileName, file);
            try {
                file.delete();
            } catch (Exception e) {
                log.error("上传后删除文件错误；", e);
            }
            //拼装返回的数据
            jsonObject.put("fileName",fileName);
            jsonObject.put("filePath",OSS_PATH+fileName);
            log.info("Object：" + fileName + "存入OSS成功。");
        }  catch (Exception e) {
            log.error("上传文件错误；",e);
            throw e;
        } finally {
            ossClient.shutdown();
            log.info("文件上传 end");
        }
        log.info(jsonObject.toJSONString());
        return jsonObject;
    }


    /**
     * base64转图片,返回阿里云OSS文件地址
     * @param base64String
     * @return
     */
    public static String uploadBase64File(String base64String,String fileType,String picNo) throws IOException {
        String systemType=System.getProperty("os.name");
        String tmpPath="";
        if(systemType.toLowerCase().contains("linux")){
            tmpPath=LINUX_TMP_PATH;
        }else if(systemType.toLowerCase().contains("mac")){
            tmpPath=MAC_TMP_PATH;
        }else {
            tmpPath=WINDOWS_TMP_PATH;
        }
        // 文件临时存放路径
        File mediaPath = new File(tmpPath);
        if (!mediaPath.exists()) {
            mediaPath.mkdirs();
        }
        if(StringUtils.isEmpty(picNo)){
            picNo= String.valueOf(new Random().nextInt(1000));
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        // 生成文件名称
        String nameSuffix = UUID.fastUUID().toString().replaceAll(" ", "_").replaceAll(",", "") + format.format(DateUtil.date())
                +"_"+ picNo;
        // 上传文件名加后缀
        String fileName = nameSuffix + "."+fileType;
        // 图片保存路径
        String filePath = tmpPath+fileName;
        // 去除前缀
        String pureBase64Encoded = base64String.substring(base64String.indexOf(",") + 1);
        byte[] imageBytes = Base64.decodeBase64(pureBase64Encoded);

        // 读取图片
        InputStream inputStream = new ByteArrayInputStream(imageBytes);
        BufferedImage originalImage = ImageIO.read(inputStream);
        // 获取原始宽高
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        System.out.println("裁剪前:"+originalWidth+"--"+originalHeight);
        // 计算4:3比例的宽度
        int newWidth = (originalHeight * 4) / 3;
        // 如果图片宽度大于4:3比例的宽度，则进行裁剪
        int x = (originalWidth - newWidth)/2;  // 水平居中裁剪
        if(x>0){
            BufferedImage croppedImage = originalImage.getSubimage(x, 0, newWidth, originalHeight);
            // 将裁剪后的图片转换为Base64格式
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(croppedImage, "jpg", outputStream);
            imageBytes = outputStream.toByteArray();
        }
        // 处理数据
        for (int i = 0; i < imageBytes.length; ++i) {
            if (imageBytes[i] < 0) {
                imageBytes[i] += 256;
            }
        }
        // 保存图片
        try {
            FileOutputStream out = new FileOutputStream(filePath);
            out.write(imageBytes);
            out.flush();
            out.close();
        } catch (FileNotFoundException e) {
            log.info("文件未找到");
            e.printStackTrace();
        } catch (IOException e) {
            log.info("写入失败");
            e.printStackTrace();
        }
        File file=new File(filePath);
        JSONObject jsonObject=uploadFile(file);
        //删除临时图片
        file.delete();
        return jsonObject.getString("filePath");
    }
    /**
     * 通过文件名下载文件
     *
     * @param objectName    要下载的文件名
     * @param localFileName 本地要创建的文件名
     */
    public static String downloadFileLocal(String objectName, String localFileName) throws Exception {
        try {
            URL url = new URL(objectName);
            InputStream in = url.openConnection().getInputStream();
            int i = objectName.lastIndexOf(".");
            String filetype=objectName.substring(i);
            String pathName = DateUtils.datePath() + "/" + IdUtils.fastUUID()+"/" +localFileName+ filetype;
            String systemType=System.getProperty("os.name");
            String tmpPath="";
            if(systemType.toLowerCase().contains("linux")){
                tmpPath=LINUX_TMP_PATH;
            }else if(systemType.toLowerCase().contains("mac")){
                tmpPath=MAC_TMP_PATH;
            }else {
                tmpPath=WINDOWS_TMP_PATH;
            }
            File file = FileUploadUtils.getAbsoluteFile(tmpPath, pathName);
            OutputStream out = new FileOutputStream(file);
            byte[] car = new byte[1024];
            int L = 0;
            while ((L = in.read(car)) != -1) {
                out.write(car, 0, L);
            }

            if (out != null) {
                out.flush();
                out.close();
            }
            if (in != null) {
                in.close();
            }
            return file.getAbsolutePath();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] readImage(String imagePath) throws IOException {
        FileInputStream inputStream = new FileInputStream(imagePath);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length = 0;
        while ((length = inputStream.read(buffer)) >= 0) {
            outputStream.write(buffer, 0, length);
        }
        inputStream.close();
        outputStream.close();
        return outputStream.toByteArray();
    }
}
