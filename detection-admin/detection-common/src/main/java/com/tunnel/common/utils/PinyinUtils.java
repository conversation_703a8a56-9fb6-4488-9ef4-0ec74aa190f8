package com.tunnel.common.utils;

import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * 本项目引入pinyin4j依赖报错，替换使用UUID
 */
@Slf4j
public class PinyinUtils {
    /**
     * 生成唯一文件名（不含扩展名）
     */
    public static String toPinyin(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        // 使用UUID生成唯一标识符，移除横线
        return UUID.randomUUID().toString().replace("-", "");
    }
}