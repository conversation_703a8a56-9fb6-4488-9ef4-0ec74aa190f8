package com.tunnel.common.utils;

import com.tunnel.common.utils.file.AliYunOSSFileUtil;
import org.apache.poi.hssf.usermodel.HSSFPictureData;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel图片处理工具类
 * 
 * <AUTHOR>
 */
public class ExcelImageUtils {
    private static final Logger log = LoggerFactory.getLogger(ExcelImageUtils.class);
    
    /**
     * 从Excel工作表中解析图片
     * 
     * @param sheet Excel工作表
     * @param remarkCols 备注列索引数组（图片所在列）
     * @param imagePrefix 图片文件名前缀
     * @return 每行的图片URL列表
     */
    public static List<String> parseImagesFromSheet(Sheet sheet, int[] remarkCols, String imagePrefix) {
        List<String> imageUrls = new ArrayList<>();
        
        try {
            log.info("开始解析工作表中的图片，备注列：{}", java.util.Arrays.toString(remarkCols));
            
            // 初始化行图片URL列表
            int lastRowNum = sheet.getLastRowNum();
            for (int i = 0; i <= lastRowNum; i++) {
                imageUrls.add(null); // 初始化为null
            }
            
            // 如果是XLSX格式，使用Drawing对象精确定位图片
            if (sheet.getWorkbook() instanceof XSSFWorkbook) {
                // 先进行图片解析诊断
                diagnoseXSSFImages((XSSFSheet) sheet, remarkCols);
                
                Map<Integer, List<String>> rowImageMap = parseXSSFImages((XSSFSheet) sheet, imagePrefix);
                
                // 将行图片映射转换为列表格式
                for (Map.Entry<Integer, List<String>> entry : rowImageMap.entrySet()) {
                    int rowIndex = entry.getKey();
                    List<String> rowImages = entry.getValue();
                    
                    if (rowImages != null && !rowImages.isEmpty()) {
                        String combinedUrls = String.join(",", rowImages);
                        if (rowIndex < imageUrls.size()) {
                            imageUrls.set(rowIndex, combinedUrls);
                        }
                    }
                }
                
                log.info("XLSX图片解析完成，共处理{}行图片", rowImageMap.size());
                
            } else if (sheet.getWorkbook() instanceof HSSFWorkbook) {
                // 对于XLS格式，使用按备注列内容判断的方法
                List<HSSFPictureData> allPictures = ((HSSFWorkbook) sheet.getWorkbook()).getAllPictures();
                log.info("XLS工作簿中共有{}张图片", allPictures.size());
                
                if (!allPictures.isEmpty()) {
                    int currentPictureIndex = 0;
                    
                    // 从第2行开始（跳过标题行）
                    for (int rowNum = 2; rowNum <= lastRowNum + 1; rowNum++) {
                        Row row = sheet.getRow(rowNum - 1);
                        if (row == null) {
                            continue;
                        }
                        
                        int expectedImageCount = getExpectedImageCountForRow(row, rowNum, remarkCols);
                        if (expectedImageCount > 0 && currentPictureIndex < allPictures.size()) {
                            List<String> rowImageUrls = new ArrayList<>();
                            
                            for (int i = 0; i < expectedImageCount && currentPictureIndex < allPictures.size(); i++) {
                                HSSFPictureData pictureData = allPictures.get(currentPictureIndex);
                                String imageUrl = uploadHSSFImageToOSS(pictureData, currentPictureIndex, imagePrefix);
                                
                                if (StringUtils.isNotEmpty(imageUrl)) {
                                    rowImageUrls.add(imageUrl);
                                    log.info("第{}行第{}张图片上传成功：{}", rowNum, i + 1, imageUrl);
                                }
                                
                                currentPictureIndex++;
                            }
                            
                            if (!rowImageUrls.isEmpty()) {
                                String combinedUrls = String.join(",", rowImageUrls);
                                if (rowNum - 1 < imageUrls.size()) {
                                    imageUrls.set(rowNum - 1, combinedUrls);
                                }
                            }
                        }
                    }
                    
                    log.info("XLS图片解析完成，共处理{}张图片", currentPictureIndex);
                }
            }
            
        } catch (Exception e) {
            log.error("解析Excel图片失败", e);
        }
        
        return imageUrls;
    }
    
    /**
     * 解析XLSX格式的图片，使用Drawing对象精确定位
     */
    private static Map<Integer, List<String>> parseXSSFImages(XSSFSheet sheet, String imagePrefix) {
        Map<Integer, List<String>> rowImageMap = new HashMap<>();
        
        try {
            log.info("开始解析XLSX格式图片");
            
            XSSFDrawing drawing = sheet.getDrawingPatriarch();
            if (drawing == null) {
                log.warn("未找到Drawing对象，可能Excel中没有图片或图片不是嵌入式的");
                
                // 尝试使用getAllPictures方法作为备用
                try {
                    List<XSSFPictureData> allPictures = ((XSSFWorkbook) sheet.getWorkbook()).getAllPictures();
                    log.info("通过getAllPictures找到{}张图片", allPictures.size());
                    
                    if (!allPictures.isEmpty()) {
                        // 使用备用方法：按行顺序分配图片
                        return parseXSSFPicturesWithBackupMethod(sheet, allPictures, imagePrefix);
                    }
                } catch (Exception e) {
                    log.error("备用图片解析方法也失败", e);
                }
                
                return rowImageMap;
            }
            
            List<XSSFShape> shapes = drawing.getShapes();
            log.info("Drawing对象中共有{}个形状", shapes.size());
            
            int pictureCount = 0;
            for (XSSFShape shape : shapes) {
                log.debug("处理形状类型：{}", shape.getClass().getSimpleName());
                
                if (shape instanceof XSSFPicture) {
                    pictureCount++;
                    XSSFPicture picture = (XSSFPicture) shape;
                    XSSFClientAnchor anchor = (XSSFClientAnchor) picture.getAnchor();
                    int rowIndex = anchor.getRow1();
                    int colIndex = anchor.getCol1();
                    
                    log.info("找到第{}张图片，位置：第{}行第{}列", pictureCount, rowIndex + 1, colIndex + 1);
                    
                    // 上传图片到OSS
                    byte[] imageData = picture.getPictureData().getData();
                    String imageFormat = picture.getPictureData().suggestFileExtension();
                    log.info("图片数据大小：{}字节，格式：{}", imageData.length, imageFormat);
                    
                    String imageUrl = uploadPictureToOSS(imageData, imageFormat, rowIndex, imagePrefix);
                    
                    if (StringUtils.isNotEmpty(imageUrl)) {
                        rowImageMap.computeIfAbsent(rowIndex, k -> new ArrayList<>()).add(imageUrl);
                        log.info("第{}行图片上传成功：{}", rowIndex + 1, imageUrl);
                    } else {
                        log.error("第{}行图片上传失败", rowIndex + 1);
                    }
                }
            }
            
            log.info("XLSX图片定位完成，共找到{}张图片，{}行包含图片", pictureCount, rowImageMap.size());
            
        } catch (Exception e) {
            log.error("解析XLSX图片失败", e);
        }
        
        return rowImageMap;
    }
    
    /**
     * 备用方法：使用getAllPictures按行顺序分配图片
     */
    private static Map<Integer, List<String>> parseXSSFPicturesWithBackupMethod(XSSFSheet sheet, List<XSSFPictureData> allPictures, String imagePrefix) {
        Map<Integer, List<String>> rowImageMap = new HashMap<>();
        
        try {
            log.info("使用备用方法解析{}张图片", allPictures.size());
            
            int currentPictureIndex = 0;
            int lastRowNum = sheet.getLastRowNum();
            
            // 从第2行开始（跳过标题行）
            for (int rowNum = 2; rowNum <= lastRowNum + 1; rowNum++) {
                Row row = sheet.getRow(rowNum - 1);
                if (row == null) {
                    continue;
                }
                
                // 检查当前行的备注列，判断应该有几张图片
                int expectedImageCount = getExpectedImageCountForRow(row, rowNum, getDefaultRemarkCols());
                log.debug("第{}行预期图片数量：{}", rowNum, expectedImageCount);
                
                if (expectedImageCount > 0 && currentPictureIndex < allPictures.size()) {
                    List<String> rowImageUrls = new ArrayList<>();
                    
                    // 从图片列表中取出对应数量的图片
                    for (int i = 0; i < expectedImageCount && currentPictureIndex < allPictures.size(); i++) {
                        XSSFPictureData pictureData = allPictures.get(currentPictureIndex);
                        
                        byte[] imageData = pictureData.getData();
                        String imageFormat = pictureData.suggestFileExtension();
                        String imageUrl = uploadPictureToOSS(imageData, imageFormat, rowNum - 1, imagePrefix);
                        
                        if (StringUtils.isNotEmpty(imageUrl)) {
                            rowImageUrls.add(imageUrl);
                            log.info("第{}行第{}张图片上传成功：{}", rowNum, i + 1, imageUrl);
                        }
                        
                        currentPictureIndex++;
                    }
                    
                    if (!rowImageUrls.isEmpty()) {
                        rowImageMap.put(rowNum - 1, rowImageUrls);
                    }
                }
            }
            
            log.info("备用方法解析完成，共处理{}张图片", currentPictureIndex);
            
        } catch (Exception e) {
            log.error("备用图片解析方法失败", e);
        }
        
        return rowImageMap;
    }
    
    /**
     * 诊断XLSX图片解析问题
     */
    private static void diagnoseXSSFImages(XSSFSheet sheet, int[] remarkCols) {
        try {
            log.info("=== 开始图片解析诊断 ===");
            
            // 1. 检查工作簿中的图片
            XSSFWorkbook workbook = (XSSFWorkbook) sheet.getWorkbook();
            List<XSSFPictureData> allPictures = workbook.getAllPictures();
            log.info("工作簿中共有{}张图片", allPictures.size());
            
            // 2. 检查Drawing对象
            XSSFDrawing drawing = sheet.getDrawingPatriarch();
            if (drawing == null) {
                log.warn("Drawing对象为null，可能的原因：");
                log.warn("1. Excel中没有图片");
                log.warn("2. 图片不是嵌入式的（可能是链接图片）");
                log.warn("3. 图片在其他工作表中");
            } else {
                List<XSSFShape> shapes = drawing.getShapes();
                log.info("Drawing对象中共有{}个形状", shapes.size());
                
                int pictureCount = 0;
                for (XSSFShape shape : shapes) {
                    if (shape instanceof XSSFPicture) {
                        pictureCount++;
                        XSSFPicture picture = (XSSFPicture) shape;
                        XSSFClientAnchor anchor = (XSSFClientAnchor) picture.getAnchor();
                        log.info("图片{}：行{}-{}，列{}-{}", 
                            pictureCount, 
                            anchor.getRow1() + 1, anchor.getRow2() + 1,
                            anchor.getCol1() + 1, anchor.getCol2() + 1);
                    }
                }
                log.info("Drawing中共有{}张图片", pictureCount);
            }
            
            // 3. 检查备注列内容
            int lastRowNum = sheet.getLastRowNum();
            log.info("工作表共有{}行数据", lastRowNum + 1);
            
            for (int rowNum = 2; rowNum <= Math.min(lastRowNum + 1, 10); rowNum++) { // 只检查前10行
                Row row = sheet.getRow(rowNum - 1);
                if (row != null) {
                    int expectedImageCount = getExpectedImageCountForRow(row, rowNum, remarkCols);
                    if (expectedImageCount > 0) {
                        log.info("第{}行预期有{}张图片", rowNum, expectedImageCount);
                    }
                }
            }
            
            log.info("=== 图片解析诊断完成 ===");
            
        } catch (Exception e) {
            log.error("图片解析诊断失败", e);
        }
    }
    
    /**
     * 检查当前行的备注列，判断应该有几张图片
     */
    private static int getExpectedImageCountForRow(Row row, int rowNum, int[] remarkCols) {
        int imageCount = 0;
        
        try {
            // 检查指定的备注列
            for (int col : remarkCols) {
                Cell cell = row.getCell(col);
                if (cell != null && !isCellEmpty(cell)) {
                    imageCount++;
                    log.debug("第{}行第{}列有内容，预期有图片", rowNum, col + 1);
                }
            }
            
        } catch (Exception e) {
            log.warn("检查第{}行备注列失败", rowNum, e);
        }
        
        return imageCount;
    }
    
    /**
     * 判断单元格是否为空
     */
    private static boolean isCellEmpty(Cell cell) {
        if (cell == null) {
            return true;
        }
        
        switch (cell.getCellType()) {
            case BLANK:
                return true;
            case STRING:
                return StringUtils.isEmpty(cell.getStringCellValue().trim());
            case NUMERIC:
                return false; // 数字类型认为不为空
            case BOOLEAN:
                return false; // 布尔类型认为不为空
            case FORMULA:
                // 对于公式，检查计算结果
                try {
                    return StringUtils.isEmpty(cell.getStringCellValue().trim());
                } catch (Exception e) {
                    return false; // 如果无法获取字符串值，认为不为空
                }
            default:
                return false;
        }
    }
    
    /**
     * 上传图片到OSS
     */
    private static String uploadPictureToOSS(byte[] imageData, String imageFormat, int rowIndex, String imagePrefix) {
        try {
            // 创建临时文件名
            String fileName = imagePrefix + "_excel_image_" + System.currentTimeMillis() + "_" + rowIndex + "." + imageFormat;
            
            // 创建MultipartFile包装类
            MultipartFile multipartFile = new ByteArrayMultipartFile(
                imageData, 
                fileName, 
                "image/" + imageFormat
            );
            
            // 上传到OSS
            com.alibaba.fastjson2.JSONObject result = AliYunOSSFileUtil.uploadPicture(multipartFile, 0);
            return result.getString("filePath");
            
        } catch (Exception e) {
            log.error("上传图片到OSS失败：{}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 上传HSSF图片到OSS
     */
    private static String uploadHSSFImageToOSS(HSSFPictureData pictureData, int index, String imagePrefix) {
        try {
            byte[] imageData = pictureData.getData();
            String format = getHSSFImageFormat(pictureData);
            
            // 创建临时文件名
            String fileName = imagePrefix + "_excel_image_" + System.currentTimeMillis() + "_" + index + "." + format;
            
            // 创建MultipartFile包装类
            MultipartFile multipartFile = new ByteArrayMultipartFile(
                imageData, 
                fileName, 
                "image/" + format
            );
            
            // 上传到OSS
            com.alibaba.fastjson2.JSONObject result = AliYunOSSFileUtil.uploadPicture(multipartFile, 0);
            return result.getString("filePath");
            
        } catch (Exception e) {
            log.error("上传HSSF图片到OSS失败：{}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取HSSF图片格式
     */
    private static String getHSSFImageFormat(HSSFPictureData pictureData) {
        String mimeType = pictureData.getMimeType();
        if (mimeType.contains("jpeg") || mimeType.contains("jpg")) {
            return "jpg";
        } else if (mimeType.contains("png")) {
            return "png";
        } else if (mimeType.contains("gif")) {
            return "gif";
        } else {
            return "jpg"; // 默认格式
        }
    }
    
    /**
     * 获取默认备注列（桥梁格式：J、K、L列）
     */
    private static int[] getDefaultRemarkCols() {
        return new int[]{9, 10, 11}; // J=9, K=10, L=11
    }
    
    /**
     * 字节数组MultipartFile包装类
     */
    private static class ByteArrayMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String contentType;
        
        public ByteArrayMultipartFile(byte[] content, String name, String contentType) {
            this.content = content;
            this.name = name;
            this.contentType = contentType;
        }
        
        @Override
        public String getName() {
            return "file";
        }
        
        @Override
        public String getOriginalFilename() {
            return name;
        }
        
        @Override
        public String getContentType() {
            return contentType;
        }
        
        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }
        
        @Override
        public long getSize() {
            return content.length;
        }
        
        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }
        
        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }
        
        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
} 