-- SQL UPDATE statements to fill missing data in sc_monitor_station table
-- Generated based on Excel file: 实业公司2025年8月-12月维护服务区情况表.xlsx
-- Date: 2025-08-30
-- 
-- This script updates 27 service areas with complete information from the Excel file
-- Fields updated: opening_status, maintenance_category, sewage_process, equipment_model, 
--                equipment_quantity, processing_scale, remark, update_time

-- Update 武当山服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '甲类', sewage_process = '已接入市政管网', equipment_quantity = 1, update_time = CURRENT_TIMESTAMP WHERE name = '武当山服务区';

-- Update 枣阳服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅱ型', equipment_quantity = 1, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '枣阳服务区';

-- Update 鲍峡服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅱ型', equipment_quantity = 1, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '鲍峡服务区';

-- Update 郧阳服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅰ型', equipment_quantity = 2, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '郧阳服务区';

-- Update 香口停车区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'M2p型', equipment_quantity = 1, processing_scale = 50, update_time = CURRENT_TIMESTAMP WHERE name = '香口停车区';

-- Update 野三关服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅲ型', equipment_quantity = 1, processing_scale = 300, update_time = CURRENT_TIMESTAMP WHERE name = '野三关服务区';

-- Update 长乐服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅱ型', equipment_quantity = 1, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '长乐服务区';

-- Update 芭蕉服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅱ型', equipment_quantity = 1, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '芭蕉服务区';

-- Update 朝阳坡停车区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅰ型', equipment_quantity = 1, processing_scale = 100, update_time = CURRENT_TIMESTAMP WHERE name = '朝阳坡停车区';

-- Update 京珠孝感服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅱ型', equipment_quantity = 2, processing_scale = 400, update_time = CURRENT_TIMESTAMP WHERE name = '京珠孝感服务区';

-- Update 安陆服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅱ型', equipment_quantity = 1, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '安陆服务区';

-- Update 随州服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅰ型', equipment_quantity = 2, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '随州服务区';

-- Update 京山服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅱ型', equipment_quantity = 1, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '京山服务区';

-- Update 均川服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅱ型', equipment_quantity = 1, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '均川服务区';

-- Update 封江服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅰ型', equipment_quantity = 2, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '封江服务区';

-- Update 云梦北停车区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅰ型', equipment_quantity = 2, processing_scale = 200, update_time = CURRENT_TIMESTAMP WHERE name = '云梦北停车区';

-- Update 装八寨服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '丙类', sewage_process = 'A/O+过滤', equipment_quantity = 1, processing_scale = 400, update_time = CURRENT_TIMESTAMP WHERE name = '装八寨服务区';

-- Update 汉川西服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '丙类', sewage_process = 'A/O+过滤', equipment_quantity = 1, processing_scale = 400, update_time = CURRENT_TIMESTAMP WHERE name = '汉川西服务区';

-- Update 应城北服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '丙类', sewage_process = 'A/O+过滤', equipment_quantity = 1, processing_scale = 400, update_time = CURRENT_TIMESTAMP WHERE name = '应城北服务区';

-- Update 东西湖服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '丁类', sewage_process = 'A/O', equipment_quantity = 1, processing_scale = 400, update_time = CURRENT_TIMESTAMP WHERE name = '东西湖服务区';

-- Update 汉十孝感服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '甲类', sewage_process = '已接入市政管网', equipment_quantity = 1, remark = '2026年1月过质保', update_time = CURRENT_TIMESTAMP WHERE name = '汉十孝感服务区';

-- Update 进山河停车区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅰ型', equipment_quantity = 1, processing_scale = 100, update_time = CURRENT_TIMESTAMP WHERE name = '进山河停车区';

-- Update 排市服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅳ型', equipment_quantity = 1, processing_scale = 400, update_time = CURRENT_TIMESTAMP WHERE name = '排市服务区';

-- Update 崇阳服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅲ型', equipment_quantity = 1, processing_scale = 300, update_time = CURRENT_TIMESTAMP WHERE name = '崇阳服务区';

-- Update 通城服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅳ型', equipment_quantity = 1, processing_scale = 400, update_time = CURRENT_TIMESTAMP WHERE name = '通城服务区';

-- Update 沙坪停车区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'M2p型', equipment_quantity = 1, processing_scale = 50, update_time = CURRENT_TIMESTAMP WHERE name = '沙坪停车区';

-- Update 高家堰服务区
UPDATE sc_monitor_station SET opening_status = '已开通', maintenance_category = '乙类', sewage_process = 'MABR', equipment_model = 'S-Ⅳ型', equipment_quantity = 1, processing_scale = 400, update_time = CURRENT_TIMESTAMP WHERE name = '高家堰服务区';

-- Summary: Updated 27 service areas with complete information
-- Fields filled: opening_status, maintenance_category, sewage_process, equipment_model, equipment_quantity, processing_scale, remark
