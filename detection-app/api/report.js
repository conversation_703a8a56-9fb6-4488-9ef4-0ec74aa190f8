import request from '@/utils/request'
import config from '@/config'
import { getToken } from '@/utils/auth'

// 上传图片
export function uploadPicture(file) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: config.baseUrl + '/app/highway/upload/uploadPicture',
      filePath: file,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + getToken()
      },
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 提交或更新报告
export function submitReport(data) {
  return request({
    url: '/common/report/insertOrUpdate',
    method: 'post',
    data
  })
}

export function searchReportList(data) {
  return request({
    'url': '/common/report/list',
    'method': 'get',
    'params': data
  })
}

// 删除上报记录
export function deleteReport(id, data) {
  return request({
    url: `/common/report/delete/${id}`,
    method: 'get',
    'params': data
  })
}

// 获取维修列表
export function searchRepairList(data) {
  return request({
    'url': '/common/report/repairList',
    'method': 'get',
    'params': data
  })
}

// 提交维修记录
export function submitRepairRecord(data) {
  return request({
    url: '/report/repairRecord/insert',
    method: 'post',
    data
  })
}

// 更新维修记录
export function updateRepairRecord(data) {
  return request({
    url: '/report/repairRecord/update',
    method: 'post',
    data
  })
}

export function auditReport(data) {
  return request({
    url: '/common/report/audit',
    method: 'post',
    data
  })
}