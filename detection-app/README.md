## 项目结构
```
文件结构
├── api                        // 所有请求  
├── components                 // 全局公用组件
├── pages                      // 页面文件
├── plugins                    // 通用方法
├── store                      // 全局 store管理
├── utils                      // 公用方法
├── static                     // 公共文件
│   ├── favicon.ico            // favicon图标
│   └── index.html             // html模板
│   └── logo.png               // logo图片
├── uni_modules                // uniui组件
│   ├── uni-badge              // 数字角标
│   ├── .........              // ........
│   ├── .........              // ........
├── App.vue                    // 应用配置
├── config.js                  // 环境配置
├── main.js                    // Vue初始化入口文件
├── manifest.json              // 配置打包
├── pages.json                 // 配置页面路由
├── permission.js              // 权限拦截
├── uni.scss                   // 全局样式变量
```

```