const TokenKey = 'App-Token'
const TokenExpireKey = 'App-Token-Expire'

// 获取token
export function getToken() {
  const token = uni.getStorageSync(TokenKey)
  const expireTime = uni.getStorageSync(TokenExpireKey)

  // 检查token是否过期
  if (token && expireTime) {
    const now = new Date().getTime()
    if (now > expireTime) {
      // token已过期，清除token
      removeToken()
      return ''
    }
  }
  return token
}

// 设置token
export function setToken(token) {
  // 设置token过期时间，例如24小时后过期
  const expireTime = new Date().getTime() + 24 * 60 * 60 * 1000
  uni.setStorageSync(TokenExpireKey, expireTime)
  return uni.setStorageSync(TokenKey, token)
}

// 移除token
export function removeToken() {
  uni.removeStorageSync(TokenExpireKey)
  return uni.removeStorageSync(TokenKey)
}