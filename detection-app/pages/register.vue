<template>
  <view class="normal-login-container">
    <view class="logo-content align-center justify-center flex">
      <text class="title">信息报送注册</text>
    </view>
    <view class="login-form-content">
      <view class="input-item flex align-center">
        <uni-easyinput
          v-model="registerForm.username"
          type="text"
          placeholder="请输入账号"
          maxlength="30"
          prefixIcon="person"
        />
      </view>
      <view class="input-item flex align-center">
        <uni-easyinput
          v-model="registerForm.password"
          type="password"
          placeholder="请输入密码"
          maxlength="20"
          prefixIcon="locked"
          passwordIcon
        />
      </view>
      <view class="input-item flex align-center">
        <uni-easyinput
          v-model="registerForm.confirmPassword"
          type="password"
          placeholder="请输入重复密码"
          maxlength="20"
          prefixIcon="locked"
          passwordIcon
        />
      </view>
      <view class="action-btn">
        <button @click="handleRegister" class="login-btn cu-btn block bg-blue lg round">注册</button>
      </view>
    </view>
  </view>
</template>

<script>
import {register} from '@/api/login'

export default {
  data() {
    return {
      codeUrl: "",
      globalConfig: getApp().globalData.config,
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        code: "",
        uuid: ""
      }
    }
  },

  methods: {

    /**
     * 用户登录
     */
    handleUserLogin() {
      this.$tab.navigateTo(`/pages/login`)
    },

    /**
     * 注册方法
     * @returns {Promise<void>}
     */
    async handleRegister() {
      if (this.registerForm.username === "") {
        this.$modal.msgError("请输入您的账号")
      } else if (this.registerForm.password === "") {
        this.$modal.msgError("请输入您的密码")
      } else if (this.registerForm.confirmPassword === "") {
        this.$modal.msgError("请再次输入您的密码")
      } else if (this.registerForm.password !== this.registerForm.confirmPassword) {
        this.$modal.msgError("两次输入的密码不一致")
      } else if (this.registerForm.code === "") {
        this.$modal.msgError("请输入验证码")
      } else {
        this.$modal.loading("注册中，请耐心等待...")
        await this.register()
      }
    },

    /**
     * 用户注册
     * @returns {Promise<void>}
     */
    async register() {
      register(this.registerForm).then(() => {
        this.$modal.closeLoading()
        uni.showModal({
          title: "系统提示",
          content: "恭喜你，您的账号 " + this.registerForm.username + " 注册成功！",
          success: function (res) {
            if (res.confirm) {
              uni.redirectTo({url: `/pages/login`});
            }
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #ffffff;
}

.normal-login-container {
  width: 100%;

  .logo-content {
    width: 100%;
    font-size: 21px;
    text-align: center;
    padding-top: 15%;

    image {
      border-radius: 4px;
    }

    .title {
      margin-left: 10px;
    }
  }

  .login-form-content {
    text-align: center;
    margin: 15% auto 20px;
    width: 80%;

    .input-item {
      margin: 20px auto;
      background-color: #f5f6f7;
      height: 45px;
      border-radius: 20px;

      .icon {
        font-size: 38rpx;
        margin-left: 10px;
        color: #999;
      }

      .input {
        width: 100%;
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        padding-left: 15px;
      }

    }

    .register-btn {
      margin-top: 40px;
      height: 45px;
    }

    .xieyi {
      color: #333;
      margin-top: 20px;
    }

    .login-code {
      height: 38px;
      float: right;

      .login-code-img {
        height: 38px;
        position: absolute;
        margin-left: 10px;
        width: 200rpx;
      }
    }
  }
}

</style>
