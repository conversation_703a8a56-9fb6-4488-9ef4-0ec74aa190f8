<template>
  <view class="mine-container" :style="{height: `${windowHeight}px`}">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="user-profile">
        <image v-if="avatar" :src="avatar" class="avatar" mode="aspectFill"></image>
        <view class="user-detail">
          <view class="nickname">{{ user.nickName }}</view>
          <view class="role">{{roleGroup}}</view>
        </view>
      </view>
    </view>

    <!--功能菜单-->
    <view class="menu-list">
      <view class="list-cell" @click="handleToUpgrade">
        <view class="menu-item-box">
          <view class="iconfont icon-refresh menu-icon"></view>
          <view>检查更新</view>
        </view>
      </view>
      <view class="list-cell" @click="handleCleanTmp">
        <view class="menu-item-box">
          <view class="iconfont icon-clean menu-icon"></view>
          <view>清理缓存</view>
        </view>
      </view>
    </view>

    <!--退出登录-->
    <view class="cu-list menu">
      <view class="cu-item item-box">
        <view class="content text-center" @click="handleLogout">
          <text class="text-black">退出登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserProfile } from "@/api/system/user"

export default {
  data() {
    return {
      user: {},
      roleGroup: "",
      postGroup: ""
    }
  },

  computed: {
    avatar() {
      return this.$store.state.user.avatar
    },
    windowHeight() {
      const info = uni.getWindowInfo()
      return info.windowHeight - 50
    }
  },

  onLoad() {
    this.getUser()
  },

  methods: {
    /**
     * 获取用户信息
     */
    getUser() {
      getUserProfile().then(response => {
        console.log(response)
        this.user = response.data
        this.roleGroup = response.roleGroup
        this.postGroup = response.postGroup
      })
    },

    /**
     * 检查更新
     */
    handleToUpgrade() {
      // 判断运行环境
      // #ifdef MP-WEIXIN
      this.checkWeixinUpdate()
      // #endif
    
      // #ifdef APP-PLUS
      this.checkAppUpdate()
      // #endif
      
      // #ifdef H5
      this.$modal.showToast('H5版本无需更新')
      // #endif
    },

    /**
     * 微信小程序检查更新
     */
    checkWeixinUpdate() {
      const updateManager = uni.getUpdateManager()
      if (!updateManager) {
        this.$modal.showToast('当前微信版本过低，无法使用更新功能')
        return
      }
      uni.showLoading({
        title: '检查更新中...'
      })
      // 检查是否有新版本
      updateManager.onCheckForUpdate((res) => {
        uni.hideLoading()
        if (res.hasUpdate) {
          uni.showModal({
            title: '发现新版本',
            content: '发现新版本，是否立即更新？更新后小程序将重启。',
            confirmText: '立即更新',
            cancelText: '稍后更新',
            success: (modalRes) => {
              if (modalRes.confirm) {
                this.downloadWeixinUpdate(updateManager)
              }
            }
          })
        } else {
          this.$modal.showToast('当前已是最新版本')
        }
      })
      // 监听更新失败事件
      updateManager.onUpdateFailed(() => {
        uni.hideLoading()
        uni.showModal({
          title: '更新失败',
          content: '新版本下载失败，请检查网络后重试',
          showCancel: false
        })
      })
    },

    /**
     * 下载微信小程序更新
     */
    downloadWeixinUpdate(updateManager) {
      uni.showLoading({
        title: '下载更新中...',
        mask: true
      })
      // 监听下载进度
      updateManager.onUpdateReady(() => {
        uni.hideLoading()
        uni.showModal({
          title: '更新完成',
          content: '新版本已下载完成，是否立即重启应用？',
          confirmText: '立即重启',
          cancelText: '稍后重启',
          success: (res) => {
            if (res.confirm) {
              // 应用新版本并重启
              updateManager.applyUpdate()
            }
          }
        })
      })
    },

    /**
     * App端检查更新（预留方法）
     */
    checkAppUpdate() {
      uni.showLoading({
        title: '检查更新中...'
      })
      // 获取当前应用版本信息
      const currentVersion = plus.runtime.version
      // 这里可以调用后端API检查版本
      // 示例：uni.request({ url: '/api/version/check' })
      setTimeout(() => {
        uni.hideLoading()
        // 模拟检查结果
        const hasUpdate = false
        if (hasUpdate) {
          uni.showModal({
            title: '发现新版本',
            content: '发现新版本，是否立即更新？',
            confirmText: '立即更新',
            cancelText: '稍后更新',
            success: (res) => {
              if (res.confirm) {
                this.downloadAppUpdate()
              }
            }
          })
        } else {
          this.$modal.showToast('当前已是最新版本')
        }
      }, 1500)
    },

    /**
     * App端下载更新（预留方法）
     */
    downloadAppUpdate() {
      uni.showLoading({
        title: '下载更新中...'
      })
      // 使用plus.downloader进行文件下载
      // const downloadTask = plus.downloader.createDownload(downloadUrl, {}, callback)
      setTimeout(() => {
        uni.hideLoading()
        this.$modal.showToast('更新下载完成，请重启应用')
      }, 3000)
    },

    /**
     * 清理缓存
     */
    handleCleanTmp() {
      uni.showModal({
        title: '清理缓存',
        content: '清理缓存将删除所有本地数据，包括登录状态，您需要重新登录。确定要继续吗？',
        confirmText: '确定清理',
        confirmColor: '#e74c3c',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.clearCache()
          }
        }
      })
    },

    /**
     * 执行清理缓存
     */
    clearCache() {
      uni.showLoading({
        title: '清理中...',
        mask: true
      })
      try {
        // 清理本地存储（包括token）
        uni.clearStorageSync()
        // 清理Vuex store中的用户信息
        this.$store.dispatch('LogOut').then(() => {
          // 清理完成后的处理
          setTimeout(() => {
            uni.hideLoading()
            uni.showModal({
              title: '清理完成',
              content: '缓存清理完成，即将跳转到登录页面',
              showCancel: false,
              confirmText: '确定',
              success: () => {
                // 跳转到登录页面并清空页面栈
                uni.reLaunch({
                  url: '/pages/login'
                })
              }
            })
          }, 1000)
        }).catch((error) => {
          console.error('清理store失败:', error)
          // 即使store清理失败，也要跳转到登录页
          setTimeout(() => {
            uni.hideLoading()
            uni.reLaunch({
              url: '/pages/login'
            })
          }, 1000)
        })
        
      } catch (error) {
        uni.hideLoading()
        console.error('清理缓存失败:', error)
        uni.showModal({
          title: '清理失败',
          content: '缓存清理失败，但为了安全起见，建议您重新登录',
          confirmText: '去登录',
          cancelText: '稍后',
          success: (res) => {
            if (res.confirm) {
              uni.reLaunch({
                url: '/pages/login'
              })
            }
          }
        })
      }
    },

    /**
     * 退出登录
     */
    handleLogout() {
      this.$modal.confirm('确定注销并退出系统吗？').then(() => {
        this.$store.dispatch('LogOut').then(() => {}).finally(()=>{
          this.$tab.reLaunch('/pages/index')
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f6f7;
}

.mine-container {
  width: 100%;
  height: 100%;

  .header-section {
    padding: 48rpx 32rpx;
    background: linear-gradient(135deg, #4a9eff 0%, #2d85f3 100%);
    color: white;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    .user-profile {
      display: flex;
      align-items: center;

      .avatar {
        width: 140rpx;
        height: 140rpx;
        border-radius: 50%;
        border: 6rpx solid rgba(255, 255, 255, 0.6);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
      }

      .user-detail {
        margin-left: 36rpx;
        flex: 1;

        .nickname {
          font-size: 40rpx;
          font-weight: 600;
          margin-bottom: 12rpx;
          letter-spacing: 0.5px;
        }

        .username {
          font-size: 28rpx;
          opacity: 0.85;
          margin-bottom: 8rpx;
          letter-spacing: 0.3px;
        }

        .role {
          font-size: 24rpx;
          opacity: 0.85;
          background-color: rgba(255, 255, 255, 0.25);
          padding: 6rpx 20rpx;
          border-radius: 24rpx;
          display: inline-block;
          letter-spacing: 0.3px;
          backdrop-filter: blur(4px);
        }
      }
    }
  }

  .menu-list {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 24rpx 24rpx 0;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    
    .list-cell {
      padding: 32rpx;
      border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);
      
      &:last-child {
        border-bottom: none;
      }
      
      .menu-item-box {
        display: flex;
        align-items: center;
        
        .menu-icon {
          font-size: 44rpx;
          color: #3c96f3;
          margin-right: 24rpx;
        }
      }
    }
  }

  .item-box {
    background-color: #FFFFFF;
    margin: 32rpx 24rpx;
    padding: 28rpx;
    border-radius: 16rpx;
    text-align: center;
    color: #303133;
    font-size: 32rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    
    &:active {
      background-color: #f8f8f8;
    }
  }
}
</style>

