<template>
  <view class="normal-login-container">
    <view class="logo-content align-center justify-center flex">
      <text class="title">信息报送登录</text>
    </view>
    <view class="login-form-content">
      <view class="input-item flex align-center">
        <uni-easyinput
          v-model="loginForm.username"
          type="text"
          placeholder="请输入账号"
          maxlength="30"
          prefixIcon="person"
        />
      </view>
      <view class="input-item flex align-center">
        <uni-easyinput
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          maxlength="20"
          prefixIcon="locked"
          passwordIcon
        />
      </view>
      <view class="action-btn">
        <button @click="handleLogin" class="login-btn cu-btn block bg-blue lg round">登录</button>
      </view>
      <view class="reg text-center" v-if="register">
        <text class="text-grey1">没有账号？</text>
        <text @click="handleUserRegister" class="text-blue">立即注册</text>
      </view>
    </view>
  </view>
</template>

<script>
import {getToken} from '@/utils/auth'

export default {
  data() {
    return {
      codeUrl: "",
      captchaEnabled: true,
      // 用户注册开关
      register: false,
      globalConfig: getApp().globalData.config,
      loginForm: {
        username: "",
        password: "",
      }
    }
  },


  onLoad() {
    // 开发环境下跳过登录检查
    // #ifdef MP-WEIXIN
    // if (process.env.NODE_ENV === 'development') {
    //   this.$tab.reLaunch('/pages/home/<USER>')
    //   return
    // }
    // #endif
    if (getToken()) {
      this.$tab.reLaunch('/pages/home/<USER>')
    }
  },


  methods: {
    /**
     * 用户注册
     */
    handleUserRegister() {
      this.$tab.redirectTo(`/pages/register`)
    },

    /**
     * 登录方法
     * @returns {Promise<void>}
     */
    async handleLogin() {
      if (this.loginForm.username === "") {
        this.$modal.msgError("请输入账号")
      } else if (this.loginForm.password === "") {
        this.$modal.msgError("请输入密码")
      } else {
        this.$modal.loading("登录中，请耐心等待...")
        await this.pwdLogin()
      }
    },

    /**
     * 密码登录
     * @returns {Promise<void>}
     */
    async pwdLogin() {
      this.$store.dispatch('Login', this.loginForm).then(() => {
        this.$modal.closeLoading()
        this.loginSuccess()
      })
    },

    /**
     * 登录成功后，处理函数
     * @param result
     */
    loginSuccess(result) {
      // 设置用户信息
      this.$store.dispatch('GetInfo').then(res => {
        this.$tab.reLaunch('/pages/home/<USER>')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #ffffff;
}

.normal-login-container {
  width: 100%;

  .logo-content {
    width: 100%;
    font-size: 21px;
    text-align: center;
    padding-top: 15%;

    image {
      border-radius: 4px;
    }

    .title {
      margin-left: 10px;
    }
  }

  .login-form-content {
    text-align: center;
    margin: 15% auto 20px;
    width: 80%;

    .input-item {
      margin: 20px auto;
      width: 100%;

      :deep(.uni-easyinput__content) {
        background-color: #f5f6f7;
        height: 45px;
        border-radius: 20px;
      }
    }

    .login-btn {
      margin-top: 40px;
      height: 45px;
    }

    .reg {
      margin-top: 15px;
    }

    .xieyi {
      color: #333;
      margin-top: 20px;
    }

    .login-code {
      height: 38px;
      float: right;

      .login-code-img {
        height: 38px;
        position: absolute;
        margin-left: 10px;
        width: 200rpx;
      }
    }
  }
}

</style>
