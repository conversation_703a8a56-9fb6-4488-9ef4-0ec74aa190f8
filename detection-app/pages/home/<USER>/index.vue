<template>
  <view class="container">
    <view class="header">
      <view class="title">
        <view class="title-block"></view>
        <text>运营公司</text>
      </view>
      <uni-forms ref="searchForm" :model="formData">
        <view class="form-box">
          <view class="form-item">
            <uni-data-select
              v-model="formData.companyName"
              :localdata="companyList"
              placeholder="请选择公司"
              @change="handleSearch"
              class="company-select"
            />
          </view>
          <view class="form-item">
            <view class="search-input-group">
              <uni-easyinput
                v-model="formData.roadCodeOrName"
                placeholder="请输入路线编号或路线名称"
                class="road-input"
              />
              <button class="search-btn" @click="handleSearch">搜索</button>
            </view>
          </view>
        </view>
      </uni-forms>
    </view>

    <view class="content">
      <uni-list class="road-list">
        <uni-list-item v-for="(item, index) in roadList" :key="index"
          :title="`${index + 1}.${item.roadName}(${item.roadCode})`"
          :note="`起止桩号：${item.startCode || '未知'} - ${item.endCode || '未知'}`"
          clickable
          class="road-item">
          <template v-slot:footer>
            <button class="report-btn" @click.stop="handleReport(item)">立即上报</button>
          </template>
        </uni-list-item>
      </uni-list>
      <view style="height: 200rpx">
        <uni-load-more :status="status"/>
      </view>
    </view>
  </view>
</template>

<script>
import {getAllCompany, searchRoad} from "@/api/road";

export default {
  data() {
    return {
      status: "more",
      formData: {
        pageNum: 1,
        pageSize: 10,
        companyName: '',
        roadCodeOrName: ''
      },
      companyList: [],
      roadList: []
    };
  },

  onLoad() {
    this.getAllCompany();
    this.loadRoadList();
  },

  // 添加触底加载事件
  onReachBottom() {
    if (this.status === 'more') {
      this.formData.pageNum += 1;
      this.loadRoadList();
    }
  },

  methods: {
    /**
     * 获取所有公司
     */
    getAllCompany() {
      getAllCompany().then(res => {
        if (res.data) {
          this.companyList = res.data.map(item => ({
            text: item,
            value: item
          }));
        }
      });
    },

    /**
     * 点击搜索或者选择公司
     */
    handleSearch() {
      // 重置页码
      this.formData.pageNum = 1;
      // 清空列表数据
      this.roadList = [];
      // 加载数据
      this.loadRoadList();
    },

    /**
     * 加载道路列表数据
     */
    loadRoadList() {
      this.status = "loading";
      searchRoad(this.formData).then(res => {
        console.log(res)
        const dataList = res.rows;
        if (dataList.length < 10) {
          this.status = "noMore";
        } else {
          this.status = "more";
        }
        // 追加数据到列表
        this.roadList = [...this.roadList, ...dataList];
      })
    },

    /**
     * 点击上报
     * @param road
     */
    handleReport(road) {
      const params = {
        companyName: road.companyName,
        roadCode: road.roadCode,
        roadName: road.roadName,
        roadId: road.id
      };
      uni.navigateTo({
        url: `/pages/home/<USER>/index?params=${encodeURIComponent(JSON.stringify(params))}`
      });
    }
  }
}
</script>

<style lang="scss">
.container {
  padding: 30rpx 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .title {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;

    .title-block {
      width: 6rpx;
      height: 32rpx;
      background-color: #3c96f3;
      margin-right: 16rpx;
      border-radius: 4rpx;
    }
  }

  .form-box {
    .form-item {
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .company-select {
        .uni-select__input-box {
          height: 72rpx;
          border-radius: 8rpx;
          border: none;
        }

        .uni-select__input-text,
        .uni-select__input-placeholder {
          color: #999;
          padding-left: 0;
        }
      }

      .search-input-group {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .road-input {
          flex: 1;

          .uni-easyinput__content {
            height: 72rpx;
            border-radius: 8rpx;
            background-color: #f5f7fa;
            padding: 0 24rpx;
            border: none;
          }

          .uni-easyinput__placeholder-class {
            font-size: 28rpx;
            color: #999;
            padding-left: 0;
          }
        }

        .search-btn {
          background-color: #3c96f3;
          color: #fff;
          font-size: 28rpx;
          height: 72rpx;
          line-height: 72rpx;
          padding: 0 40rpx;
          border-radius: 8rpx;
          border: none;
          flex-shrink: 0;

          &:active {
            opacity: 0.9;
          }
        }
      }
    }
  }
}

.content {
  background-color: #f8f8f8;
  border-radius: 16rpx;

  .road-list {
    background-color: transparent;

    .road-item {
      margin-bottom: 16rpx;
      background-color: #fff;
      border-radius: 8rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

      &:last-child {
        margin-bottom: 0;
      }

      &::after {
        display: none;
      }

      .uni-list-item__container {
        padding: 24rpx;
      }

      .uni-list-item__content-title {
        font-size: 28rpx;
        color: #333;
        font-weight: normal;
      }

      .uni-list-item__content-note {
        font-size: 24rpx;
        color: #666;
        margin-top: 8rpx;
      }

      .report-btn {
        background-color: #07c160;
        color: #fff;
        font-size: 28rpx;
        height: 56rpx;
        line-height: 56rpx;
        padding: 0 32rpx;
        border-radius: 8rpx;
        border: none;

        &:active {
          opacity: 0.9;
        }
      }
    }
  }
}
</style>
