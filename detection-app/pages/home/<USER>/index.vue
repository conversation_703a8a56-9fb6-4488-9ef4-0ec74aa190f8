<template>
  <view class="container">
    <view class="search-section">
      <view class="search-row">
        <view class="search-item">
          <uni-data-select
            v-model="searchForm.companyName"
            :localdata="companyList"
            placeholder="请选择公司"
            class="form-select"
            clearable
            @change="handleSearch"
          />
        </view>
        <view class="search-item">
          <uni-data-select
            v-model="searchForm.type"
            :localdata="typeOptions"
            placeholder="请选择类型"
            class="form-select"
            :clear="false"
            @change="handleSearch"
          />
        </view>
      </view>
      <view class="search-row">
        <view class="search-input-box">
          <uni-easyinput
            v-model="searchForm.roadCodeOrName"
            placeholder="请输入路线编号或名称"
            class="form-input"
          />
        </view>
        <button class="search-btn" @click="handleSearch">搜索</button>
      </view>
    </view>

    <view class="list-section">
      <view
        v-for="(item, index) in dataList"
        :key="index"
        class="list-item"
        @click="handleView(item)"
      >
        <view class="item-header">
          <view class="title-wrap">
            <text class="item-title">{{ `${index + 1}.${item.roadName}(${item.roadCode})` }}</text>
            <uni-tag :text="item.typeName" type="primary" size="small" inverted />
          </view>
          <view class="item-actions">
            <button 
              v-if="item.repairId"
              class="action-btn edit" 
              @click.stop="handleEdit(item)"
            >修改</button>
            <button 
              v-else
              class="action-btn repair"
              @click.stop="handleRepair(item)"
            >维修</button>
          </view>
        </view>
        <view class="item-content">
          <view class="content-row">
            <text class="label">位置：</text>
            <text class="value" v-if="searchForm.type === 0">{{ item.bridgeName ? item.bridgeName : ''}}</text>
            <text class="value" v-if="searchForm.type === 1">{{ item.tunnelName ? item.tunnelName : '' }}</text>
            <text class="value" v-if="searchForm.type === 2">{{ item.holeName ? item.holeName : '' }}</text>
            <text class="value" v-if="searchForm.type === 3">{{ item.slopeType ? item.slopeType : '' }}</text>
            <text class="value" v-if="searchForm.type === 4">{{ item.pile ? item.pile : '' }}</text>
          </view>
          <view class="content-row">
            <text class="label">缺陷：</text>
            <text class="value">{{ item.defectDesc ? item.defectDesc : '' }}</text>
          </view>
          <view class="content-row">
            <text class="label">状态：</text>
            <uni-tag :inverted="true" :circle="true" :text="item.repairId ? '已维修' : '未维修'" :type="item.repairId ? 'primary' : 'error'" size="small" />
          </view>
        </view>
      </view>
      <view style="height: 200rpx">
        <uni-load-more :status="status"/>
      </view>
    </view>
  </view>
</template>

<script>
import {searchRepairList} from "@/api/report";
import {getAllCompany} from "@/api/road";

export default {
  data() {
    return {
      status: "more",
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        companyName: '',
        type: 0,
        roadCodeOrName: ''
      },
      companyList: [],
      typeOptions: [
        { value: 0, text: '桥梁' },
        { value: 1, text: '隧道' },
        { value: 2, text: '涵洞' },
        { value: 3, text: '边坡' },
        { value: 4, text: '路面' }
      ],
      dataList: [],
      needRefresh: true,
    };
  },

  onShow() {
    if (this.needRefresh) {
      this.getAllCompany();
      this.handleSearch();
    }
    // 重置刷新标识
    this.needRefresh = true;
  },

  // 添加触底加载事件
  onReachBottom() {
    if (this.status === 'more') {
      this.searchForm.pageNum += 1;
      this.loadRepairList();
    }
  },

  methods: {
    /**
     * 获取所有公司
     */
    getAllCompany() {
      getAllCompany().then(res => {
        if (res.data) {
          this.companyList = res.data.map(item => ({
            text: item.length > 14 ? item.substring(0, 14) + '...' : item,
            value: item
          }));
        }
      });
    },

    /**
     * 点击搜索
     */
    handleSearch() {
      // 重置页码
      this.searchForm.pageNum = 1;
      // 清空列表数据
      this.dataList = [];
      // 加载数据
      this.loadRepairList();
    },

    /**
     * 加载维修列表数据
     */
    loadRepairList() {
      this.status = "loading";
      searchRepairList(this.searchForm).then(res => {
        console.log(res)
        const list = res.rows;
        if (list.length < 10) {
          this.status = "noMore";
        } else {
          this.status = "more";
        }
        list.forEach(item => {
          //设置类型type
          item.type = this.searchForm.type;
          item.typeName = this.typeOptions.find(option => option.value === this.searchForm.type)?.text;
        });
        // 追加数据到列表
        this.dataList = [...this.dataList, ...list];
      })
    },

    handleEdit(item) {
      item.typeName = this.typeOptions.find(it => it.value === item.type).text;
      uni.navigateTo({
        url: `/pages/home/<USER>/repair/index?params=${encodeURIComponent(JSON.stringify(item))}`
      });
    },

    handleRepair(item) {
      item.typeName = this.typeOptions.find(it => it.value === item.type).text;
      uni.navigateTo({
        url: `/pages/home/<USER>/repair/index?params=${encodeURIComponent(JSON.stringify(item))}`
      });
    },
    
    /**
     * 点击查看
     */
    handleView(item) {
      this.needRefresh = false;
      // 补充查看模式标识
      const viewItem = {
        ...item,
        viewMode: true // 标识为查看模式
      };
      // 跳转到查看页面（复用维修页面）
      uni.navigateTo({
        url: `/pages/home/<USER>/repair/index?params=${encodeURIComponent(JSON.stringify(viewItem))}`
      });
    },
  }
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
}

.search-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .search-row {
    display: flex;
    gap: 20rpx;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .search-item {
      &:first-child {
        flex: 2;
      }
      &:last-child {
        flex: 1;
      }
      min-width: 0;
    }

    .search-input-box {
      flex: 1;
    }

    .search-btn {
      width: 160rpx;
      height: 72rpx;
      line-height: 72rpx;
      background-color: #3c96f3;
      color: #fff;
      font-size: 28rpx;
      border-radius: 8rpx;
      padding: 0;

      &:active {
        opacity: 0.9;
      }
    }
  }

  :deep(.uni-data-select) {
    .uni-select__input-box {
      background-color: #f5f7fa;
      border-radius: 8rpx;
      border: none;
      height: 72rpx;
      padding: 0 20rpx;
    }
  }

  :deep(.uni-easyinput__content) {
    background-color: #f5f7fa;
    border-radius: 8rpx;
    border: none;
    height: 72rpx;
    line-height: 72rpx;
  }
}

.list-section {
  .list-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

    &:last-child {
      margin-bottom: 0;
    }

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      padding-bottom: 16rpx;
      border-bottom: 1px solid #f5f5f5;

      .title-wrap {
        display: flex;
        align-items: center;
        gap: 16rpx;
      }

      .item-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }

      .item-actions {
        display: flex;
        gap: 16rpx;

        .action-btn {
          min-width: 100rpx;
          height: 48rpx;
          line-height: 48rpx;
          font-size: 24rpx;
          border-radius: 6rpx;
          padding: 0 16rpx;

          &.edit {
            background-color: #4caf50;
            color: #fff;
          }

          &.repair {
            background-color: #ff9f43;
            color: #fff;
          }

          &:active {
            opacity: 0.9;
          }
        }
      }
    }

    .item-content {
      .content-row {
        display: flex;
        margin-bottom: 12rpx;
        
        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 26rpx;
          color: #666;
          width: 80rpx;
          flex-shrink: 0;
        }

        .value {
          font-size: 26rpx;
          color: #333;
          flex: 1;
          word-break: break-all;
        }
      }

      .status {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-top: 12rpx;
      }
    }
  }
}
.list-section {
  .list-item {
    cursor: pointer; // 添加鼠标指针样式
    
    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      padding-bottom: 16rpx;
      border-bottom: 1px solid #f5f5f5;

      .title-wrap {
        display: flex;
        align-items: center;
        gap: 16rpx;
      }

      .item-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }

      .item-actions {
        display: flex;
        gap: 16rpx;

        .action-btn {
          min-width: 100rpx;
          height: 48rpx;
          line-height: 48rpx;
          font-size: 24rpx;
          border-radius: 6rpx;
          padding: 0 16rpx;

          &.edit {
            background-color: #4caf50;
            color: #fff;
          }

          &.repair {
            background-color: #ff9f43;
            color: #fff;
          }

          &:active {
            opacity: 0.9;
          }
        }
      }
    }

    .item-content {
      .content-row {
        display: flex;
        margin-bottom: 12rpx;
        
        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 26rpx;
          color: #666;
          width: 80rpx;
          flex-shrink: 0;
        }

        .value {
          font-size: 26rpx;
          color: #333;
          flex: 1;
          word-break: break-all;
        }
      }

      .status {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-top: 12rpx;
      }
    }
  }
}
</style>
