<template>
  <view class="container">
    <view class="search-section">
      <view class="search-row">
        <view class="search-item">
          <uni-data-select
            v-model="searchForm.companyName"
            :localdata="companyList"
            placeholder="请选择公司"
            class="form-select"
            clearable
            @change="handleSearch"
          />
        </view>
        <view class="search-item">
          <uni-data-select
            v-model="searchForm.type"
            :localdata="typeOptions"
            placeholder="请选择类型"
            class="form-select"
            :clear="false"
            @change="handleSearch"
          />
        </view>
      </view>
      <view class="search-row">
        <view class="search-item">
          <uni-data-select
            v-model="searchForm.riskLevel"
            :localdata="riskLevelOptions"
            placeholder="请选择风险等级"
            class="form-select"
            clearable
            @change="handleSearch"
          />
        </view>
        <view class="search-item">
          <uni-data-select
            v-model="searchForm.checkStatus"
            :localdata="auditStatusOptions"
            placeholder="请选择审核状态"
            class="form-select"
            clearable
            @change="handleSearch"
          />
        </view>
      </view>
      <view class="search-row">
        <view class="search-input-box">
          <uni-easyinput
            v-model="searchForm.roadCodeOrName"
            placeholder="请输入路线编号或名称"
            class="form-input"
            clearable
          />
        </view>
        <button class="search-btn" @click="handleSearch">搜索</button>
      </view>
    </view>

    <view class="list-section">
      <view
        v-for="(item, index) in dataList"
        :key="index"
        class="list-item"
        @click="handleView(item)">
        <!-- 第一行：标题和标签 -->
        <view class="title-row">
          <view class="title-content">
            <text class="item-title">{{ `${index + 1}.${item.roadName}(${item.roadCode})` }}</text>
            <uni-tag :text="item.typeName" type="primary" size="small" inverted />
            <view class="risk-level-tag" 
                  :class="{
                    'level-high': item.riskLevel === '1' || item.riskLevel === '2',
                    'level-low': item.riskLevel === '3' || item.riskLevel === '4'
                  }">
              {{ getRiskLevelText(item.riskLevel)}}
            </view>
          </view>
          <view class="audit-status-tag" 
                :class="{
                  'pending': item.checkStatus === 0,
                  'approved': item.checkStatus === 1,
                  'rejected': item.checkStatus === 2
                }">
            {{ item.checkStatus === 1 ? '已通过' : item.checkStatus === 2 ? '未通过' : '待审核' }}
          </view>
        </view>
        
        <!-- 第二行：位置信息 -->
        <view class="location-row">
          <text class="label">位置：</text>
          <text class="location-text" v-if="searchForm.type === 0">{{ item.bridgeName ? item.bridgeName : ''}}</text>
          <text class="location-text" v-if="searchForm.type === 1">{{ item.tunnelName ? item.tunnelName : '' }}</text>
          <text class="location-text" v-if="searchForm.type === 2">{{ item.holeName ? item.holeName : '' }}</text>
          <text class="location-text" v-if="searchForm.type === 3">{{ item.slopeType ? item.slopeType : '' }}</text>
          <text class="location-text" v-if="searchForm.type === 4">{{ item.pile ? item.pile : '' }}</text>
        </view>
        
        <!-- 第三行：缺陷描述 -->
        <view class="defect-row">
          <view class="defect-content">
            <text class="label">缺陷：</text>
            <text class="defect-text">{{ item.defectDesc ? item.defectDesc : '' }}</text>
          </view>
          <!-- 驳回理由（如果有） -->
          <view class="reject-row" v-if="item.checkStatus === 2 && item.remark">
            <text class="reject-text">驳回理由：{{ item.remark }}</text>
          </view>
        </view>
        
        <!-- 第四行：操作按钮 -->
        <view class="action-buttons-row">
          <view class="action-buttons">
            <button class="action-btn delete" @click.stop="handleDelete(item)">删除</button>
            <button class="action-btn edit" @click.stop="handleEdit(item)">修改</button>
            <button 
              v-if="getIsAudit()"
              :class="{
                'action-btn': true,
                'audit': true,
                'disabled': item.checkStatus !== 0
              }"
              @click.stop="handleAudit(item)"
            >
              审核
            </button>
          </view>
        </view>
      </view>
      <view style="height: 200rpx">
        <uni-load-more :status="status"/>
      </view>
    </view>
  </view>
</template>

<script>
import {getAllCompany} from "@/api/road";
import {deleteReport, searchReportList} from "@/api/report";

export default {
  data() {
    return {
      status: "more",
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        companyName: '',
        type: 0,
        roadCodeOrName: '',
        riskLevel: '',
        checkStatus: ''
      },
      companyList: [],
      typeOptions: [
        { value: 0, text: '桥梁' },
        { value: 1, text: '隧道' },
        { value: 2, text: '涵洞' },
        { value: 3, text: '边坡' },
        { value: 4, text: '路面' }
      ],
      riskLevelOptions: [
        { value: '1', text: '一级' },
        { value: '2', text: '二级' },
        { value: '3', text: '三级' },
        { value: '4', text: '四级' }
      ],
      auditStatusOptions: [
        { value: 0, text: '待审核' },
        { value: 1, text: '已通过' },
        { value: 2, text: '未通过' }
      ],
      dataList: [],
      needRefresh: true,
    };
  },

  onShow() {
    if (this.needRefresh) {
      this.getAllCompany();
      this.handleSearch();
    }
    // 重置刷新标识
    this.needRefresh = true;
  },

  // 添加触底加载事件
  onReachBottom() {
    if (this.status === 'more') {
      this.searchForm.pageNum += 1;
      this.loadReportList();
    }
  },

  methods: {
    /**
     * 获取所有公司
     */
    getAllCompany() {
      getAllCompany().then(res => {
        if (res.data) {
          this.companyList = res.data.map(item => ({
            text: item.length > 14 ? item.substring(0, 14) + '...' : item,
            value: item
          }));
        }
      });
    },

    /**
     * 是否是审核人
     */
    getIsAudit() {
      return this.$auth.hasRoleOr(["admin", "XMJL"]);
    },

    /**
     * 点击搜索
     */
    handleSearch() {
      // 重置页码
      this.searchForm.pageNum = 1;
      // 清空列表数据
      this.dataList = [];
      // 加载数据
      this.loadReportList();
    },

    /**
     * 加载上报列表数据
     */
    loadReportList() {
      this.status = "loading";
      searchReportList(this.searchForm).then(res => {
        console.log(res)
        const list = res.rows;
        if (list.length < 10) {
          this.status = "noMore";
        } else {
          this.status = "more";
        }
        list.forEach(item => {
          //设置类型typeName
          item.typeName = this.typeOptions.find(option => option.value === this.searchForm.type)?.text;
        });
        // 追加数据到列表
        this.dataList = [...this.dataList, ...list];
      })
    },

    /**
     * 点击审核
     */
    handleAudit(item) {
      if (item.checkStatus === 1 || item.checkStatus === 2) {
        uni.showToast({
          title: '该记录已审核',
          icon: 'none'
        });
        return;
      }
    const auditItem = {
    ...item,
    type: this.searchForm.type,
    };
    // 跳转到审核页面
    uni.navigateTo({
    url: `/pages/home/<USER>/audit/index?params=${encodeURIComponent(JSON.stringify(auditItem))}`
    });
    },


    /**
     * 点击修改
     */
    handleEdit(item) {
      uni.navigateTo({
        url: `/pages/home/<USER>/index?params=${encodeURIComponent(JSON.stringify(item))}`
      });
    },

    /**
     * 点击查看
     */
    handleView(item) {
      this.needRefresh = false;
      // 补充type字段和查看模式标识
      const viewItem = {
        ...item,
        type: this.searchForm.type,
        viewMode: true
      };
      // 跳转到查看页面（复用修改页面）
      uni.navigateTo({
        url: `/pages/home/<USER>/index?params=${encodeURIComponent(JSON.stringify(viewItem))}`
      });
    },

    /**
     * 点击删除
     */
    handleDelete(item) {
      uni.showModal({
        title: '提示',
        content: '确认删除该记录吗？',
        success: (res) => {
          if (res.confirm) {
            let data = {
              type: this.searchForm.type
            }
            deleteReport(item.id, data).then(() => {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              // 重新加载列表数据
              this.handleSearch();
            }).catch(() => {
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              });
            });
          }
        }
      });
    },

    // 获取风险等级中文
    getRiskLevelText(level) {
      const levelMap = {
        1: '一级',
        2: '二级',
        3: '三级',
        4: '四级'
      }
      return levelMap[level] || level
    }
  }
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
}

.search-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .search-row {
    display: flex;
    gap: 20rpx;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    // 第一行：公司和类型保持原有比例
    &:first-child {
      .search-item {
        &:first-child {
          flex: 3;
        }
        &:last-child {
          flex: 1;
        }
        min-width: 0; // 防止内容溢出
      }
    }

    // 第二行：风险等级和审核状态平分宽度
    &:nth-child(2) {
      .search-item {
        flex: 1;
        min-width: 0; // 防止内容溢出
      }
    }

    // 第三行：搜索输入框和按钮
    &:nth-child(3) {
      .search-input-box {
        flex: 1;
      }

      .search-btn {
        width: 160rpx;
        height: 72rpx;
        line-height: 72rpx;
        background-color: #3c96f3;
        color: #fff;
        font-size: 28rpx;
        border-radius: 8rpx;
        padding: 0;

        &:active {
          opacity: 0.9;
        }
      }
    }
  }

  :deep(.uni-data-select) {
    .uni-select__input-box {
      background-color: #f5f7fa;
      border-radius: 8rpx;
      border: none;
      height: 72rpx;
      padding: 0 20rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  :deep(.uni-easyinput__content) {
    background-color: #f5f7fa;
    border-radius: 8rpx;
    border: none;
    height: 72rpx;
    line-height: 72rpx;
  }
}

.list-section {
  .list-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

    &:last-child {
      margin-bottom: 0;
    }

    // 第一行：标题和标签
    .title-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16rpx;
      gap: 12rpx;

      .title-content {
        display: flex;
        align-items: center;
        gap: 12rpx;
        flex: 1;
        min-width: 0;
        flex-wrap: wrap;

        .item-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
          flex-shrink: 0;
        }

        // 风险等级标签
        .risk-level-tag {
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
          font-size: 22rpx;
          font-weight: 500;
          flex-shrink: 0;
          
          // 一级、二级显示红色
          &.level-high {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
          }
          
          // 三级、四级显示蓝色
          &.level-low {
            background-color: #e3f2fd;
            color: #1565c0;
            border: 1px solid #bbdefb;
          }
          
          // 默认样式（如果没有匹配到等级）
          &:not(.level-high):not(.level-low) {
            background-color: #e3f2fd;
            color: #1565c0;
            border: 1px solid #bbdefb;
          }
        }
      }

      // 审核状态标签
      .audit-status-tag {
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        font-size: 22rpx;
        font-weight: 500;
        flex-shrink: 0;
        
        &.pending {
          background-color: #3c96f3;
          color: white;
        }
        
        &.approved {
          background-color: green;
          color: white;
        }
        
        &.rejected {
          background-color: red;
          color: white;
        }
      }
    }

    // 第二行：位置信息
    .location-row {
      display: flex;
      margin-bottom: 16rpx;
      
      .label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
        flex-shrink: 0;
        margin-right: 8rpx;
      }

      .location-text {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
        flex: 1;
      }
    }

    // 第三行：缺陷描述
    .defect-row {
      display: flex;
      flex-direction: column;
      margin-bottom: 16rpx;
      
      .defect-content {
        display: flex;
        align-items: flex-start;
        
        .label {
          font-size: 26rpx;
          color: #666;
          font-weight: 500;
          flex-shrink: 0;
          margin-right: 8rpx;
          line-height: 1.4;
        }

        .defect-text {
          font-size: 26rpx;
          color: #666;
          line-height: 1.4;
          flex: 1;
          word-wrap: break-word;
          word-break: break-all;
        }
      }

      // 驳回理由
      .reject-row {
        margin-top: 8rpx;

        .reject-text {
          font-size: 26rpx;
          color: #ff4757;
          line-height: 1.4;
        }
      }
    }

    // 第四行：操作按钮
    .action-buttons-row {
      display: flex;
      justify-content: flex-end;
      padding-top: 16rpx;
      border-top: 1px solid #f0f0f0;

      .action-buttons {
        display: flex;
        gap: 12rpx;

        .action-btn {
          width: 80rpx;
          height: 56rpx;
          line-height: 56rpx;
          font-size: 24rpx;
          border-radius: 8rpx;
          padding: 0;
          border: none;
          font-weight: 500;

          &.audit {
            background-color: #4285f4;
            color: #fff;
            
            &.disabled {
              background-color: #ccc !important;
              color: #999 !important;
              cursor: not-allowed;
              opacity: 0.6;
            }
          }

          &.edit {
            background-color: #ff9500;
            color: #fff;
          }

          &.delete {
            background-color: #ff4757;
            color: #fff;
          }

          &:active:not(.disabled) {
            opacity: 0.8;
            transform: scale(0.98);
          }
        }
      }
    }
  }
}
</style>
