<template>
  <view class="container">
    <view class="tab-bar" v-if="!params.id && !isViewMode">
      <view
          v-for="(item, index) in tabList"
          :key="index"
          class="tab-item"
          :class="{ active: currentTab === index }"
          @click="handleTabChange(index)">
        {{ item.name }}
      </view>
    </view>
    <view class="content">
      <view class="section">
        <view class="section-title">
          <view class="title-block"></view>
          <text>路段信息</text>
        </view>
        <view class="section-content">
          <view class="info-item">
            <text class="label">运营公司：</text>
            <text class="value">{{ params.companyName }}</text>
          </view>
          <view class="info-item">
            <text class="label">路段名称：</text>
            <text class="value">{{ `${params.roadName}(${params.roadCode})` }}</text>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">
          <view class="title-block"></view>
          <text>{{ isViewMode ? '信息详情' : '信息上报' }}</text>
        </view>
        <view class="section-content">
          <!-- 桥梁信息表单 -->
          <BridgeForm v-if="currentTab === 0" ref="bridgeForm" :view-mode="isViewMode"/>
          <!-- 隧道信息表单 -->
          <TunnelForm v-if="currentTab === 1" ref="tunnelForm" :view-mode="isViewMode"/>
          <!-- 涵洞信息表单 -->
          <HoleForm v-if="currentTab === 2" ref="holeForm" :view-mode="isViewMode"/>
          <!-- 边坡信息表单 -->
          <SlopeForm v-if="currentTab === 3" ref="slopeForm" :view-mode="isViewMode"/>
          <!-- 路面信息表单 -->
          <RoadForm v-if="currentTab === 4" ref="roadForm" :view-mode="isViewMode"/>
        </view>
      </view>

      <view class="section">
        <view class="section-title">
          <view class="title-block"></view>
          <text>{{ isViewMode ? '相关照片' : '照片上报' }}</text>
        </view>
        <view class="section-content">
          <template v-if="isViewMode">
            <!-- 查看模式：只显示图片 -->
            <view class="image-list" v-if="viewImages.length > 0">
              <image
                v-for="(img, index) in viewImages"
                :key="index"
                :src="img"
                mode="aspectFill"
                class="preview-image"
                @click="previewImage(img, viewImages)"
              />
            </view>
            <view v-else class="no-images">
              <text>暂无图片</text>
            </view>
          </template>
          <template v-else>
            <!-- 编辑模式：文件选择器 -->
            <uni-file-picker
                ref="filePicker"
                :auto-upload="false"
                limit="3"
                title="最多选择3张图片（必填）"
                class="file-picker"
                file-mediatype="image"
            />
          </template>
        </view>
      </view>
      <text>{{errorMessage}}</text>

      <view class="footer">
        <template v-if="isViewMode">
          <!-- 查看模式：只显示返回按钮 -->
          <button class="back-btn" @click="handleBack">返回</button>
        </template>
        <template v-else>
          <!-- 编辑模式：显示取消和提交按钮 -->
          <button class="cancel-btn" @click="handleCancel">取消</button>
          <button class="submit-btn" @click="handleSubmit">{{ isEdit ? '修改' : '提交' }}</button>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
import BridgeForm from './components/BridgeForm.vue'
import TunnelForm from './components/TunnelForm.vue'
import HoleForm from './components/HoleForm.vue'
import SlopeForm from './components/SlopeForm.vue'
import RoadForm from "@/pages/home/<USER>/components/RoadForm.vue";
import {submitReport, uploadPicture} from "@/api/report";

export default {
  components: {
    RoadForm,
    BridgeForm,
    TunnelForm,
    HoleForm,
    SlopeForm
  },
  data() {
    return {
      isEdit: false,
      // 新增：是否为查看模式
      isViewMode: false,
      params: {},
      currentTab: 0,
      // 新增：查看模式下的图片列表
      viewImages: [],
      tabList: [
        {name: '桥梁', title: '桥梁信息上报'},
        {name: '隧道', title: '隧道信息上报'},
        {name: '涵洞', title: '涵洞信息上报'},
        {name: '边坡', title: '边坡信息上报'},
        {name: '路面', title: '路面信息上报'}
      ],
      errorMessage: ''
    };
  },

  onLoad(options) {
    if (options.params) {
      this.params = JSON.parse(decodeURIComponent(options.params));
      console.log('params', this.params)
      // 检查是否为查看模式
      this.isViewMode = this.params.viewMode || false;
      this.isEdit = this.params.id !== undefined;
      if (this.params.typeName) {
        // 根据 type 找到对应的索引
        this.currentTab = this.tabList.findIndex(tab => tab.name === this.params.typeName);
      }
      // 如果有type字段，直接使用
      if (this.params.type !== undefined) {
        this.currentTab = this.params.type;
      }
      // 设置初始标题
      this.updatePageTitle();
      // 如果是查看模式，初始化图片列表
      if (this.isViewMode && this.params.reportPhotos) {
        this.viewImages = this.params.reportPhotos.split(',');
      }
      // 确保组件已经挂载
      this.$nextTick(() => {
        this.initFormData();
      });
    }
  },

  methods: {
    /**
     * 处理标签页切换
     * @param {number} index - 目标标签页的索引
     */
    handleTabChange(index) {
      this.currentTab = index;
      this.updatePageTitle();
    },

    /**
     * 更新页面标题
     */
    updatePageTitle() {
      const title = this.isViewMode ? 
        `${this.tabList[this.currentTab].name}详情` : 
        this.tabList[this.currentTab].title;
      uni.setNavigationBarTitle({
        title: title
      });
    },
    
    /**
     * 处理返回按钮点击事件（查看模式）
     */
    handleBack() {
      uni.navigateBack();
    },
    
    /**
     * 预览图片
     */
    previewImage(current, urls) {
      uni.previewImage({
        current,
        urls
      });
    },
    
    /**
     * 处理取消按钮点击事件，返回上一页
     */
    handleCancel() {
      uni.navigateBack();
    },

    /**
     * 重置表单数据
     */
    resetForm() {
      // 重置表单数据
      let formRef = null;
      switch (this.currentTab) {
        case 0:
          formRef = this.$refs.bridgeForm;
          break;
        case 1:
          formRef = this.$refs.tunnelForm;
          break;
        case 2:
          formRef = this.$refs.holeForm;
          break;
        case 3:
          formRef = this.$refs.slopeForm;
          break;
        case 4:
          formRef = this.$refs.roadForm;
          break;
      }
      if (formRef && formRef.formData) {
        // 重置表单
        formRef.formData = {
          riskLevel: '3',
          isUrgent: 0,
        };
      }
      // 重置图片
      if (this.$refs.filePicker) {
        this.$refs.filePicker.files = [];
      }
    },


    /**
     * 处理表单提交
     */
    async handleSubmit() {
      try {
        // 1. 获取并验证图片数据（将图片验证提前）
        const fileList = this.$refs.filePicker.files;
        console.log('fileList', fileList)
        if (!fileList || fileList.length === 0) {
          uni.showToast({
            title: '请至少上传一张图片',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        // 2. 获取并验证表单数据
        let formData = null;
        switch (this.currentTab) {
          case 0:
            await this.$refs.bridgeForm.validate()
            formData = this.$refs.bridgeForm.getFormData()
            break
          case 1:
            await this.$refs.tunnelForm.validate()
            formData = this.$refs.tunnelForm.getFormData()
            break
          case 2:
            await this.$refs.holeForm.validate()
            formData = this.$refs.holeForm.getFormData()
            break
          case 3:
            await this.$refs.slopeForm.validate()
            formData = this.$refs.slopeForm.getFormData()
            break
          case 4:
            await this.$refs.roadForm.validate()
            formData = this.$refs.roadForm.getFormData()
            break
        }
        // 验证表单数据是否获取成功
        if (!formData) {
          uni.showToast({
            title: '表单获取失败',
            icon: 'error'
          })
          return
        }
        // 3. 显示加载提示
        uni.showLoading({
          title: '提交中...'
        })
        // 4. 上传图片
        const imageUrls = []
        try {
          for (const file of fileList) {
            // 如果是已有的图片URL，直接使用
            if (file.url.startsWith('https')) {
              imageUrls.push(file.url)
              continue
            }
            // 否则上传新图片
            const res = await uploadPicture(file.url)
            console.log('res', res)
            if (res.statusCode === 200) {
              const data = JSON.parse(res.data)
              if (data.code === 200 && data.data) {
                imageUrls.push(data.data)
              } else {
                uni.showToast({
                  title: '图片上传失败',
                  icon: 'error'
                })
                this.errorMessage = JSON.stringify(res);
                return
              }
            }
          }
        } catch (error) {
          uni.showToast({
            title: '图片上传失败',
            icon: 'error'
          })
          this.errorMessage = JSON.stringify(error);
          return
        }
        // 5. 构造后端请求数据
        const submitData = {
          type: this.currentTab,
          id: formData.id || null,  // 如果是修改，需要传入 id
          data: {
            roadId: this.params.roadId,
            ...formData,
            reportPhotos: imageUrls.join(',')
          }
        }
        // 6. 提交数据
        const response = await submitReport(submitData);
        if (response.code !== 200) {
          uni.hideLoading()
          uni.showToast({
            title: '提交失败',
            icon: 'error'
          })
          return
        }
        // 7. 关闭加载提示
        uni.hideLoading()
        // 8. 提交成功处理
        uni.showToast({
          title: this.isEdit ? '修改成功' : '提交成功',
          icon: 'success'
        })
        console.log("当前状态:", this.isEdit)
        // 只有新增成功后才重置表单，修改成功后不重置
        if (!this.isEdit) {
          // 重置表单
          this.resetForm();
        }
      } catch (e) {
        // 错误处理
        uni.hideLoading()
        uni.showToast({
          title: this.isEdit ? '修改失败' : '提交失败',
          icon: 'error'
        })
        console.error(e)
      }
    },


    /**
     * 初始化表单数据
     */
    initFormData() {
      // 根据不同类型初始化对应的表单
      let formRef = null;
      switch (this.currentTab) {
        case 0:
          formRef = this.$refs.bridgeForm;
          break;
        case 1:
          formRef = this.$refs.tunnelForm;
          break;
        case 2:
          formRef = this.$refs.holeForm;
          break;
        case 3:
          formRef = this.$refs.slopeForm;
          break;
        case 4:
          formRef = this.$refs.roadForm;
          break;
      }
      if (formRef) {
        // 设置表单数据
        Object.assign(formRef.formData, this.params);
        // 如果有图片且不是视图模式，初始化图片列表
        if (this.params.reportPhotos && !this.isViewMode && this.$refs.filePicker) {
          const photos = this.params.reportPhotos.split(',');
          this.$refs.filePicker.files = photos.map(url => ({
            url,
            status: 'success',
            progress: 100
          }));
        }
      }
    }
  }
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  margin-bottom: 30px;
}

.tab-bar {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 0 20rpx;
  height: 88rpx;
  position: sticky;
  top: 0;
  z-index: 1;

  .tab-item {
    position: relative;
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #666;
    transition: all 0.3s;

    &.active {
      color: #3c96f3;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 48rpx;
        height: 4rpx;
        background-color: #3c96f3;
        border-radius: 2rpx;
      }
    }
  }
}

.content {
  padding: 20rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .title-block {
      width: 6rpx;
      height: 32rpx;
      background-color: #3c96f3;
      margin-right: 16rpx;
      border-radius: 4rpx;
    }

    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .section-content {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #666;
        width: 160rpx;
        flex-shrink: 0;
      }

      .value {
        color: #333;
      }
    }

    .form-item {
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .uni-forms-item__label {
        font-size: 28rpx;
        color: #666;
        justify-content: flex-start !important;
        padding: 0;
      }

      .uni-forms-item__content {
        display: flex;
      }
    }

    .form-item-inline {
      display: flex;
      gap: 12rpx;
      margin-bottom: 16rpx;

      .uni-forms-item {
        flex: 1;

        :deep(.uni-data-select) {
          flex: 1;

          .uni-select__input-box {
            width: 100%;
            background-color: #f5f7fa;
            border: none;
            border-radius: 8rpx;
            height: 56rpx;
            line-height: 56rpx;
            padding: 0 12rpx;
          }

          .uni-select--mask {
            top: 72rpx;
            background-color: transparent;
          }

          .uni-select__selector {
            top: 72rpx;
          }
        }
      }
    }
  }
}

.footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 20rpx 50rpx;
  margin-bottom: env(safe-area-inset-bottom);

  button {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 32rpx;
    border-radius: 8rpx;

    &.cancel-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    &.submit-btn {
      background-color: #07c160;
      color: #fff;
    }

    &.back-btn {
      background-color: #3c96f3;
      color: #fff;
    }

    &:active {
      opacity: 0.9;
    }
  }
}

.uni-select__input-box {
  width: 70px !important;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  
  .preview-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 8rpx;
    object-fit: cover;
  }
}

.no-images {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

</style>
