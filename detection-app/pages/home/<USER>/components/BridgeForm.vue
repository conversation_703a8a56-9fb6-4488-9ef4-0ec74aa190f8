<template>
  <uni-forms ref="form" :model="formData">
    <uni-forms-item label="桥梁名称：" label-width="80">
      <uni-easyinput v-model="formData.bridgeName" placeholder="请输入桥梁名称" class="form-input" :disabled="viewMode" />
    </uni-forms-item>
    <uni-forms-item label="中心桩号：" label-width="80">
      <uni-easyinput v-model="formData.centerPile" placeholder="请输入中心桩号" class="form-input" :disabled="viewMode" />
    </uni-forms-item>
    <uni-forms-item label="风险因素：" label-width="80">
      <uni-easyinput v-model="formData.riskFactor" placeholder="请输入风险因素" class="form-input" :disabled="viewMode" />
    </uni-forms-item>
    <uni-forms-item label="缺陷描述：" label-width="80">
      <uni-easyinput v-model="formData.defectDesc" type="textarea" placeholder="请输入缺陷描述" height="50" :autoHeight="true" :disabled="viewMode"/>
    </uni-forms-item>
    <view class="form-item form-item-inline">
      <uni-forms-item label="风险等级：" label-width="80">
        <uni-data-select v-model="formData.riskLevel" :localdata="riskLevelOptions" placeholder="请选择" :disabled="viewMode"/>
      </uni-forms-item>
      <uni-forms-item label="是否紧急：" label-width="80">
        <uni-data-select v-model="formData.isUrgent" :localdata="yesNoOptions" placeholder="请选择" :disabled="viewMode"/>
      </uni-forms-item>
    </view>
    <uni-forms-item label="维修截止时间：" label-width="80">
      <uni-datetime-picker v-model="formData.repairEndTime" type="date" placeholder="请选择" :disabled="viewMode"/>
    </uni-forms-item>
    <uni-forms-item label="首次发现时间：" label-width="100">
      <uni-datetime-picker v-model="formData.findTime" type="date" placeholder="请选择" :disabled="viewMode"/>
    </uni-forms-item>
  </uni-forms>
</template>

<script>
import { riskLevelOptions, yesNoOptions } from '@/utils/dict.js'

export default {
  name: 'BridgeForm',
  props: {
    viewMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        bridgeName: '',
        centerPile: '',
        riskFactor: '',
        defectDesc: '',
        riskLevel: '3',
        isUrgent: 0,
        repairEndTime: '',
        findTime: ''
      },
      riskLevelOptions,
      yesNoOptions
    }
  },
  methods: {
    // 获取表单数据
    getFormData() {
      return this.formData
    },

    // 验证表单
    validate() {
      return this.$refs.form.validate()
    }
  }
}
</script>

<style scoped lang="scss">
.form-item-inline {
  display: flex;
  justify-content: space-between;
  .uni-forms-item {
    width: 48%;
  }
}
</style>