<template>
  <uni-forms ref="form" :model="formData">
    <uni-forms-item label="边坡类型：" label-width="80">
      <uni-data-select v-model="formData.slopeType" :localdata="slopeTypeOptions" placeholder="请选择" :disabled="viewMode"/>
    </uni-forms-item>
    <uni-forms-item label="方向：" label-width="80">
      <uni-data-select v-model="formData.direction" :localdata="directionOptions" placeholder="请选择" :disabled="viewMode"/>
    </uni-forms-item>
    <uni-forms-item label="边坡桩号：" label-width="80">
      <view class="stake-number">
        <uni-easyinput v-model="formData.startStake" placeholder="起始桩号" class="form-input" :disabled="viewMode" />
        <text class="stake-text">至</text>
        <uni-easyinput v-model="formData.endStake" placeholder="结束桩号" class="form-input" :disabled="viewMode" />
      </view>
    </uni-forms-item>
    <view class="form-item form-item-inline">
      <uni-forms-item label="高度(m)：" label-width="80">
        <uni-easyinput v-model="formData.height" placeholder="请输入" class="form-input" :disabled="viewMode" />
      </uni-forms-item>
      <uni-forms-item label="宽度(m)：" label-width="80">
        <uni-easyinput v-model="formData.width" placeholder="请输入" class="form-input" :disabled="viewMode" />
      </uni-forms-item>
    </view>
    <uni-forms-item label="现有支护方式：" label-width="100">
      <uni-easyinput v-model="formData.supportMethod" placeholder="请输入" class="form-input" :disabled="viewMode" />
    </uni-forms-item>
    <uni-forms-item label="截排水沟是否完好：" label-width="120">
      <uni-data-select v-model="formData.drainageStatus" :localdata="yesNoOptions" placeholder="请选择" :disabled="viewMode"/>
    </uni-forms-item>
    <uni-forms-item label="是否临水临崖：" label-width="100">
      <uni-data-select v-model="formData.isNearWater" :localdata="yesNoOptions" placeholder="请选择" :disabled="viewMode"/>
    </uni-forms-item>
    <uni-forms-item label="运营期历史病害情况：" label-width="120">
      <uni-easyinput v-model="formData.historyIssues" type="textarea" placeholder="请输入历史病害情况" height="80" :autoHeight="true" :disabled="viewMode"/>
    </uni-forms-item>
    <uni-forms-item label="风险因素：" label-width="80">
      <uni-easyinput v-model="formData.riskFactor" placeholder="请输入风险因素" class="form-input" :disabled="viewMode" />
    </uni-forms-item>
    <uni-forms-item label="缺陷描述：" label-width="80">
      <uni-easyinput v-model="formData.defectDesc" type="textarea" placeholder="请输入缺陷描述" height="50" :autoHeight="true" :disabled="viewMode"/>
    </uni-forms-item>
    <view class="form-item form-item-inline">
      <uni-forms-item label="风险等级：" label-width="80">
        <uni-data-select v-model="formData.riskLevel" :localdata="riskLevelOptions" placeholder="请选择" :disabled="viewMode"/>
      </uni-forms-item>
      <uni-forms-item label="是否紧急：" label-width="80">
        <uni-data-select v-model="formData.isUrgent" :localdata="yesNoOptions" placeholder="请选择" :disabled="viewMode"/>
      </uni-forms-item>
    </view>
    <uni-forms-item label="维修截止时间：" label-width="80">
      <uni-datetime-picker v-model="formData.repairEndTime" type="date" placeholder="请选择" :disabled="viewMode"/>
    </uni-forms-item>
    <uni-forms-item label="首次发现时间：" label-width="100">
      <uni-datetime-picker v-model="formData.findTime" type="date" placeholder="请选择" :disabled="viewMode"/>
    </uni-forms-item>
  </uni-forms>
</template>

<script>
import { riskLevelOptions, yesNoOptions } from '@/utils/dict.js'

export default {
  name: 'SlopeForm',
  props: {
    viewMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        slopeType: '',
        direction: '',
        startStake: '',
        endStake: '',
        height: '',
        width: '',
        supportMethod: '',
        drainageStatus: 1,
        isNearWater: 0,
        historyIssues: '',
        riskFactor: '',
        defectDesc: '',
        riskLevel: '3',
        isUrgent: 0,
        repairEndTime: '',
        findTime: ''
      },
      slopeTypeOptions: [
        { value: '1', text: '挖方边坡' },
        { value: '2', text: '填方边坡' }
      ],
      directionOptions: [
        { value: '1', text: '上行' },
        { value: '2', text: '下行' }
      ],
      riskLevelOptions,
      yesNoOptions
    }
  },
  methods: {
    // 获取表单数据
    getFormData() {
      return this.formData
    },

    // 验证表单
    validate() {
      return this.$refs.form.validate()
    }
  }
}
</script>

<style scoped lang="scss">
.form-item-inline {
  display: flex;
  justify-content: space-between;
  .uni-forms-item {
    width: 48%;
  }
}

.stake-number {
  display: flex;
  align-items: center;
  .stake-text {
    margin: 0 10px;
  }
  .form-input {
    flex: 1;
  }
}
</style>