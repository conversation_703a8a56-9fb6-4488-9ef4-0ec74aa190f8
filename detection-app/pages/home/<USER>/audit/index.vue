<template>
  <view class="container">
    <!-- 信息上报部分（只读） -->
    <view class="section">
      <view class="section-title">
        <view class="title-block"></view>
        <text>信息上报</text>
      </view>
      <view class="section-content">
        <view class="info-group">
          <view class="info-row">
            <view class="info-item">
              <text class="label">运营公司：</text>
              <text class="value">{{ formData.companyName }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">路段名称：</text>
              <text class="value">{{ formData.roadName }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">报送类别：</text>
              <text class="value">{{ formData.typeName }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">检测时间：</text>
              <text class="value">{{ formData.createTime }}</text>
            </view>
          </view>
          <!-- 桥梁特有字段 -->
          <template v-if="formData.type === 0">
            <view class="info-row">
              <view class="info-item">
                <text class="label">桥梁名称：</text>
                <text class="value">{{ formData.bridgeName || '暂无'}}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="label">中心桩号：</text>
                <text class="value">{{ formData.centerPile || '暂无' }}</text>
              </view>
            </view>
          </template>
          <!-- 隧道特有字段 -->
          <template v-if="formData.type === 1">
            <view class="info-row">
              <view class="info-item">
                <text class="label">隧道名称：</text>
                <text class="value">{{ formData.tunnelName || '暂无' }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="label">隧道幅别：</text>
                <text class="value">{{ formData.direction || '暂无' }}</text>
              </view>
            </view>
          </template>
          <!-- 涵洞特有字段 -->
          <template v-if="formData.type === 2">
            <view class="info-row">
              <view class="info-item">
                <text class="label">涵洞名称：</text>
                <text class="value">{{ formData.holeName || '暂无' }}</text>
              </view>
            </view>
          </template>
          <!-- 边坡特有字段 -->
          <template v-if="formData.type === 3">
            <view class="info-row">
              <view class="info-item">
                <text class="label">边坡类型：</text>
                <text class="value">{{ formData.slopeType || '暂无' }}</text>
              </view>
              <view class="info-item">
                <text class="label">方向：</text>
                <text class="value">{{ formData.direction || '暂无' }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="label">边坡桩号：</text>
                <text class="value">{{ formData.startStake }} - {{ formData.endStake }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="label">现有支护方式：</text>
                <text class="value">{{ formData.supportMethod || '暂无' }}</text>
              </view>
            </view>
          </template>
          <!-- 路面特有字段 -->
          <template v-if="formData.type === 4">
            <view class="info-row">
              <view class="info-item">
                <text class="label">路线桩号：</text>
                <text class="value">{{ formData.pile || '暂无'}}</text>
              </view>
            </view>
          </template>
          <!-- 通用字段 -->
          <view class="info-row">
            <view class="info-item">
              <text class="label">风险因素：</text>
              <text class="value">{{ formData.riskFactor || '暂无'}}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">缺陷描述：</text>
              <text class="value">{{ formData.defectDesc || '暂无'}}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">风险等级：</text>
              <text class="value">{{ getRiskLevelText(formData.riskLevel) }}</text>
            </view>
            <view class="info-item">
              <text class="label">是否紧急：</text>
              <text class="value">{{ formData.isUrgent ? '是' : '否' }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label-long">首次发现时间：</text>
              <text class="value">{{ formData.findTime || '暂无' }}</text>
            </view>
          </view>
        </view>
        <view class="image-group">
          <text class="image-title">上报照片：</text>
          <view class="image-list">
            <image
              v-for="(img, index) in formData.reportImages"
              :key="index"
              :src="img"
              mode="aspectFill"
              class="preview-image"
              @click="previewImage(img, formData.reportImages)"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 审核意见部分 -->
    <view class="section">
      <view class="section-title">
        <view class="title-block"></view>
        <text>审核意见</text>
      </view>
      <view class="section-content">
        <uni-forms ref="form" :model="auditData">
          <uni-forms-item label="审核意见：" label-width="80">
            <uni-easyinput
              v-model="auditData.auditReason"
              type="textarea"
              placeholder="请输入审核意见"
            />
          </uni-forms-item>
        </uni-forms>
      </view>
    </view>

    <view class="footer">
      <button class="reject-btn" @click="handleReject">驳回</button>
      <button class="approve-btn" @click="handleApprove">通过</button>
    </view>
  </view>
</template>

<script>
import {auditReport} from "@/api/report";

export default {
  data() {
    return {
      formData: {
        id: null,
        companyName: '',
        roadName: '',
        type: '',
        typeName: '',
        createTime: '',
        bridgeName: '',
        centerPile: '',
        tunnelName: '',
        direction: '',
        holeName: '',
        slopeType: '',
        startStake: '',
        endStake: '',
        supportMethod: '',
        pile: '',
        riskFactor: '',
        defectDesc: '',
        riskLevel: '',
        isUrgent: false,
        findTime: '',
        reportImages: []
      },
      auditData: {
        auditReason: ''
      }
    };
  },

  onLoad(options) {
    if (options.params) {
      const params = JSON.parse(decodeURIComponent(options.params));
      console.log(params);
      // 处理上报图片
      if (params.reportPhotos) {
        params.reportImages = params.reportPhotos.split(',');
      }
      this.formData = { ...this.formData, ...params };
    }
  },

  methods: {
    previewImage(current, urls) {
      uni.previewImage({
        current,
        urls
      });
    },

    // 获取风险等级中文
    getRiskLevelText(level) {
      const levelMap = {
        1: '一级',
        2: '二级',
        3: '三级',
        4: '四级'
      }
      return levelMap[level] || level
    },

    // 通过审核
    handleApprove() {
      const that = this;
      uni.showModal({
        title: '确认操作',
        content: '确认通过该项审核吗？',
        success: (res) => {
          if (res.confirm) {
            let data = {
              ids: [that.formData.id],
              checkStatus: 1,
              remark: that.auditData.auditReason,
              type: that.formData.type
            }
            auditReport(data).then(() => {
              uni.showToast({ title: '审核通过', icon: 'success' });
              setTimeout(() => {
                uni.navigateBack();
              }, 1500);
            });
          }
        }
      });
    },

    // 驳回审核
    handleReject() {
      const that = this;
      if (!this.auditData.auditReason.trim()) {
        uni.showToast({
          title: '请输入驳回原因',
          icon: 'none'
        });
        return;
      }
      uni.showModal({
        title: '确认操作',
        content: '确认驳回该项审核吗？',
        success: (res) => {
          if (res.confirm) {
            let data = {
              ids: [that.formData.id],
              checkStatus: 2,
              remark: that.auditData.auditReason,
              type: that.formData.type
            }
            auditReport(data).then(() => {
              uni.showToast({ title: '已驳回', icon: 'success' });
              setTimeout(() => {
                uni.navigateBack();
              }, 1500);
            });
          }
        }
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  margin-bottom: 30px;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .title-block {
      width: 6rpx;
      height: 32rpx;
      background-color: #3c96f3;
      margin-right: 16rpx;
      border-radius: 4rpx;
    }

    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .section-content {
    .info-group {
      .info-row {
        display: flex;
        margin-bottom: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: flex-start;

          .label {
            width: 140rpx;
            flex-shrink: 0;
          }

          .label-long {
            width: 200rpx;
            flex-shrink: 0;
          }

          .value {
            flex: 1;
            color: #666;
          }
        }
      }
    }

    .image-group {
      margin-top: 20rpx;

      .image-title {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
      }

      .image-list {
        display: flex;
        gap: 20rpx;
        margin-top: 16rpx;

        .preview-image {
          width: 200rpx;
          height: 200rpx;
          border-radius: 8rpx;
        }
      }
    }

    :deep(.uni-forms-item) {
      margin-bottom: 16rpx;

      .uni-forms-item__label {
        font-size: 28rpx;
        color: #666;
      }

      .uni-easyinput__content {
        background-color: #f5f7fa;
        border: none;
        border-radius: 8rpx;
        min-height: 120rpx;
      }
    }
  }
}

.footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 20rpx 50rpx;
  margin-bottom: env(safe-area-inset-bottom);

  button {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 32rpx;
    border-radius: 8rpx;

    &.reject-btn {
      background-color: #ff4757;
      color: #fff;

      &:active {
        opacity: 0.9;
      }
    }

    &.approve-btn {
      background-color: #07c160;
      color: #fff;

      &:active {
        opacity: 0.9;
      }
    }
  }
}
</style>
