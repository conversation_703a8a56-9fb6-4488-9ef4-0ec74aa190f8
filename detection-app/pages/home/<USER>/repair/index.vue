<template>
  <view class="container">
    <!-- 信息上报部分（只读） -->
    <view class="section">
      <view class="section-title">
        <view class="title-block"></view>
        <text>信息上报</text>
      </view>
      <view class="section-content">
        <view class="info-group">
          <view class="info-row">
            <view class="info-item">
              <text class="label">运营公司：</text>
              <text class="value">{{ formData.companyName }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">路段名称：</text>
              <text class="value">{{ formData.roadName }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">报送类别：</text>
              <text class="value">{{ formData.typeName }}</text>
            </view>
            <view class="info-item">
              <text class="label">检测时间：</text>
              <text class="value">{{ formData.createTime }}</text>
            </view>
          </view>
          <!-- 桥梁特有字段 -->
          <template v-if="formData.type === 0">
            <view class="info-row">
              <view class="info-item">
                <text class="label">桥梁名称：</text>
                <text class="value">{{ formData.bridgeName || '暂无'}}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="label">中心桩号：</text>
                <text class="value">{{ formData.centerPile || '暂无' }}</text>
              </view>
            </view>
          </template>
          <!-- 隧道特有字段 -->
          <template v-if="formData.type === 1">
            <view class="info-row">
              <view class="info-item">
                <text class="label">隧道名称：</text>
                <text class="value">{{ formData.tunnelName || '暂无' }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="label">隧道幅别：</text>
                <text class="value">{{ formData.direction || '暂无' }}</text>
              </view>
            </view>
          </template>
          <!-- 涵洞特有字段 -->
          <template v-if="formData.type === 2">
            <view class="info-row">
              <view class="info-item">
                <text class="label">涵洞名称：</text>
                <text class="value">{{ formData.holeName || '暂无' }}</text>
              </view>
            </view>
          </template>
          <!-- 边坡特有字段 -->
          <template v-if="formData.type === 3">
            <view class="info-row">
              <view class="info-item">
                <text class="label">边坡类型：</text>
                <text class="value">{{ formData.slopeType || '暂无' }}</text>
              </view>
              <view class="info-item">
                <text class="label">方向：</text>
                <text class="value">{{ formData.direction || '暂无' }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="label">边坡桩号：</text>
                <text class="value">{{ formData.startStake }} - {{ formData.endStake }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="label">现有支护方式：</text>
                <text class="value">{{ formData.supportMethod || '暂无' }}</text>
              </view>
            </view>
          </template>
          <!-- 路面特有字段 -->
          <template v-if="formData.type === 4">
            <view class="info-row">
              <view class="info-item">
                <text class="label">路线桩号：</text>
                <text class="value">{{ formData.pile || '暂无'}}</text>
              </view>
            </view>
          </template>
          <!-- 通用字段 -->
          <view class="info-row">
            <view class="info-item">
              <text class="label">风险因素：</text>
              <text class="value">{{ formData.riskFactor || '暂无'}}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">缺陷描述：</text>
              <text class="value">{{ formData.defectDesc || '暂无'}}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">风险等级：</text>
              <text class="value">{{ getRiskLevelText(formData.riskLevel) }}</text>
            </view>
            <view class="info-item">
              <text class="label">是否紧急：</text>
              <text class="value">{{ formData.isUrgent ? '是' : '否' }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">维修情况：</text>
              <text class="value">{{ formData.repairStatus ? '已维修' : '未维修' }}</text>
            </view>
            <view class="info-item">
              <text class="label-long">首次发现时间：</text>
              <text class="value">{{ formData.findTime || '暂无' }}</text>
            </view>
          </view>
        </view>
        <view class="image-group">
          <text class="image-title">维修前照片：</text>
          <view class="image-list">
            <image
              v-for="(img, index) in formData.beforeImages"
              :key="index"
              :src="img"
              mode="aspectFill"
              class="preview-image"
              @click="previewImage(img, formData.beforeImages)"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 维修跟踪部分 -->
    <view class="section">
      <view class="section-title">
        <view class="title-block"></view>
        <text>{{ isViewMode ? '维修信息' : '维修跟踪' }}</text>
      </view>
      <view class="section-content">
        <template v-if="isViewMode">
          <!-- 查看模式：只显示信息 -->
          <view class="info-item">
            <text class="label">维修情况：</text>
            <text class="value">{{ repairData.repairStatus === 1 ? '已维修' : '未维修' }}</text>
          </view>
          <view class="info-item">
            <text class="label">维修时间：</text>
            <text class="value">{{ repairData.repairStatus === 1 && repairData.repairTime ? repairData.repairTime : '暂无' }}</text>
          </view>
          <view class="info-item">
            <text class="label">维修记录：</text>
            <text class="value">{{ repairData.repairRecord || '暂无' }}</text>
          </view>
        </template>
        <template v-else>
          <!-- 编辑模式：表单 -->
          <uni-forms ref="form" :model="repairData">
            <uni-forms-item label="维修情况：" label-width="80">
              <uni-data-select
                v-model="repairData.repairStatus"
                :localdata="repairStatusOptions"
                placeholder="请选择维修情况"
              />
            </uni-forms-item>
            <uni-forms-item label="维修时间：" label-width="80">
              <uni-datetime-picker
                v-model="repairData.repairTime"
                type="date"
                placeholder="请选择维修时间"
              />
            </uni-forms-item>
            <uni-forms-item label="维修记录：" label-width="80">
              <uni-easyinput
                v-model="repairData.repairRecord"
                type="textarea"
                placeholder="请输入维修记录"
              />
            </uni-forms-item>
          </uni-forms>
        </template>
      </view>
    </view>

    <view class="section">
      <view class="section-title">
        <view class="title-block"></view>
        <text>维修后照片</text>
      </view>
      <view class="section-content">
        <template v-if="isViewMode">
          <!-- 查看模式：只显示图片 -->
          <view class="image-list" v-if="viewRepairImages.length > 0">
            <image
              v-for="(img, index) in viewRepairImages"
              :key="index"
              :src="img"
              mode="aspectFill"
              class="preview-image"
              @click="previewImage(img, viewRepairImages)"
            />
          </view>
          <view v-else class="no-images">
            <text>暂无维修后照片</text>
          </view>
        </template>
        <template v-else>
          <!-- 编辑模式：文件选择器 -->
          <uni-file-picker
            ref="filePicker"
            v-model="repairData.repairPhotos"
            limit="3"
            title="最多选择3张图片（必填）"
            class="file-picker"
            file-mediatype="image"
          />
        </template>
      </view>
    </view>

    <view class="footer">
      <template v-if="isViewMode">
        <!-- 查看模式：只显示返回按钮 -->
        <button class="back-btn" @click="handleBack">返回</button>
      </template>
      <template v-else>
        <!-- 编辑模式：显示取消和提交按钮 -->
        <button class="cancel-btn" @click="handleCancel">取消</button>
        <button class="submit-btn" @click="handleSubmit">{{ isEdit ? '修改' : '提交' }}</button>
      </template>
    </view>
  </view>
</template>

<script>
import { submitRepairRecord, updateRepairRecord, uploadPicture } from '@/api/report';

export default {
  data() {
    return {
      isEdit: false,
      isViewMode: false, // 新增：是否为查看模式
      viewRepairImages: [], // 新增：查看模式下的维修后图片列表
      formData: {
        repairId: null,
        companyName: '',
        roadName: '',
        type: '',
        checkTime: '',
        culvertName: '',
        centerPile: '',
        riskFactor: '',
        defectDesc: '',
        riskLevel: '',
        isUrgent: false,
        repairTime: '',
        findTime: '',
        beforeImages: []
      },
      repairData: {
        checkType: null,
        checkId: null,
        repairStatus: 0,
        repairTime: this.getCurrentDate(),
        repairRecord: '',
        repairPhotos: []
      },
      repairStatusOptions: [
        { value: 0, text: '未维修' },
        { value: 1, text: '已维修' }
      ]
    };
  },

  onLoad(options) {
    if (options.params) {
      const params = JSON.parse(decodeURIComponent(options.params));
      console.log(params);
      // 检查是否为查看模式
      this.isViewMode = params.viewMode || false;
      // 处理维修前图片
      if (params.reportPhotos) {
        params.beforeImages = params.reportPhotos.split(',');
      }
      this.formData = { ...this.formData, ...params };
      if (params.repairId) {
        this.isEdit = true;
        this.repairData = { ...this.repairData, ...params };
        // 处理维修后图片
        if (params.repairPhotos) {
          const repairPhotoUrls = params.repairPhotos.split(',');
          if (this.isViewMode) {
            // 查看模式：直接设置图片URL列表
            this.viewRepairImages = repairPhotoUrls;
          } else {
            // 编辑模式：设置文件选择器格式
            this.repairData.repairPhotos = repairPhotoUrls.map(url => ({
              url,
              status: 'success',
              name: url.substring(url.lastIndexOf('/') + 1)
            }));
          }
        }
      }
    }
  },

  methods: {
    previewImage(current, urls) {
      uni.previewImage({
        current,
        urls
      });
    },

    // 添加获取当前日期时间的方法
    getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    handleCancel() {
      uni.navigateBack();
    },

    handleBack() {
      uni.navigateBack();
    },

    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        // 显示加载提示
        uni.showLoading({
          title: '提交中...'
        });
        // 上传维修后照片
        const fileList = this.$refs.filePicker.files;
        if (!fileList || fileList.length === 0) {
          uni.showToast({
            title: '请至少上传一张图片',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        //上传图片
        const imageUrls = []
        try {
          for (const file of fileList) {
            // 如果是已有的图片URL，直接使用
            if (file.url.startsWith('https')) {
              imageUrls.push(file.url)
              continue
            }
            // 否则上传新图片
            const res = await uploadPicture(file.url)
            console.log('res', res)
            if (res.statusCode !== 200) {
              uni.showToast({
                title: '图片上传失败，请联系管理员',
                icon: 'error'
              })
              return
            }
            const data = JSON.parse(res.data)
            if (data.code === 200 && data.data) {
              imageUrls.push(data.data)
            } else {
              uni.showToast({
                title: '图片上传失败',
                icon: 'error'
              })
              return
            }
          }
        } catch (error) {
          uni.showToast({
            title: '图片上传失败',
            icon: 'error'
          })
          return
        }
        // 构造提交数据
        const submitData = {
          id: this.formData.repairId || null,  // 如果是修改，需要传入 id
          checkType: this.formData.type,
          checkId: this.formData.id,
          repairStatus: this.repairData.repairStatus,
          repairTime: this.repairData.repairTime,
          repairRecord: this.repairData.repairRecord,
          repairPhotos: imageUrls.join(',')
        };
        // 提交维修记录
        const response = this.isEdit 
          ? await updateRepairRecord(submitData)
          : await submitRepairRecord(submitData);
        if (response.code !== 200) {
          uni.hideLoading()
          uni.showToast({
            title: response.message,
            icon: 'error'
          })
          return
        }
        // 隐藏加载提示
        uni.hideLoading();
        // 显示成功提示
        uni.showToast({
          title: this.isEdit ? '修改成功' : '提交成功',
          icon: 'success'
        });
      } catch (e) {
        // 隐藏加载提示
        uni.hideLoading();
        // 显示错误提示
        uni.showToast({
          title: this.isEdit ? '修改失败' : '提交失败',
          icon: 'error'
        });
        console.error(e);
      }
    },

    // 获取风险等级中文
    getRiskLevelText(level) {
      const levelMap = {
        1: '一级',
        2: '二级',
        3: '三级',
        4: '四级'
      }
      return levelMap[level] || level
    }

  }

};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  margin-bottom: 30px;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .title-block {
      width: 6rpx;
      height: 32rpx;
      background-color: #3c96f3;
      margin-right: 16rpx;
      border-radius: 4rpx;
    }

    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #666;
    }
  }

  .section-content {
    .info-group {
      .info-row {
        display: flex;
        margin-bottom: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: flex-start;

          .label {
            width: 140rpx;
            flex-shrink: 0;
          }

          .label-long {
            width: 200rpx;
            flex-shrink: 0;
          }

          .value {
            flex: 1;
            color: #666;
          }
        }
      }
    }

    .image-group {
      margin-top: 20rpx;

      .image-title {
        font-size: 28rpx;
        margin-bottom: 16rpx;
      }

      .image-list {
        display: flex;
        gap: 20rpx;
        margin-top: 16rpx;

        .preview-image {
          width: 200rpx;
          height: 200rpx;
          border-radius: 8rpx;
        }
      }
    }

    :deep(.uni-forms-item) {
      margin-bottom: 16rpx;

      .uni-forms-item__label {
        font-size: 28rpx;
        color: #666;
      }

      .image-tip {
        display: block;
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
      }

      .uni-easyinput__content,
      .uni-select__input-box {
        background-color: #f5f7fa;
        border: none;
        border-radius: 8rpx;
        height: 72rpx;
        line-height: 72rpx;
      }

      .uni-select__input-box {
        padding: 0 20rpx;
      }
    }
  }
}

.info-item {
  flex: 1;
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .label {
    width: 160rpx;
    flex-shrink: 0;
    font-size: 28rpx;
  }
  
  .label-long {
    width: 200rpx;
    flex-shrink: 0;
    font-size: 28rpx;
  }
  
  .value {
    flex: 1;
    font-size: 28rpx;
    color: #666;
    word-wrap: break-word;
  }
}

// 合并后的 .image-list 样式
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 16rpx;
  
  .preview-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 8rpx;
    object-fit: cover;
  }
}

.no-images {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

// 合并后的 .footer 样式
.footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 20rpx 70rpx;
  margin-bottom: env(safe-area-inset-bottom);

  button {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 32rpx;
    border-radius: 8rpx;
    background-color: #3c96f3;
    color: #fff;
    
    &:active {
      opacity: 0.9;
    }

    &.cancel-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    &.submit-btn {
      background-color: #07c160;
      color: #fff;
    }
    
    &.back-btn {
      background-color: #3c96f3;
      color: #fff;
    }
  }
}
</style>
