<template>
  <view class="container bg-gray padding-bottom-xl">
    <!-- 功能菜单 -->
    <view class="menu-card">
      <view class="menu-title">
        <text class="title-bar"></text>
        <text class="title-text">信息报送</text>
      </view>
      <uni-grid :column="3" :highlight="false" :showBorder="false" square @change="gridItemClick">
        <uni-grid-item :index="0">
          <view class="grid-item-box">
            <image src="/static/images/home/<USER>" mode="aspectFit" class="grid-icon"></image>
            <text class="text">数据录入</text>
          </view>
        </uni-grid-item>
        <uni-grid-item :index="1">
          <view class="grid-item-box">
            <image src="/static/images/home/<USER>" mode="aspectFit" class="grid-icon"></image>
            <text class="text">数据列表</text>
          </view>
        </uni-grid-item>
      </uni-grid>
    </view>

    <view class="menu-card">
      <view class="menu-title">
        <text class="title-bar"></text>
        <text class="title-text">维修跟踪</text>
      </view>
      <uni-grid :column="3" :highlight="false" :showBorder="false" square @change="gridItemClick">
        <uni-grid-item :index="2">
          <view class="grid-item-box">
            <image src="/static/images/home/<USER>" mode="aspectFit" class="grid-icon"></image>
            <text class="text">维修跟踪</text>
          </view>
        </uni-grid-item>
      </uni-grid>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },

  methods: {
    /**
     * 网格菜单项点击事件处理函数
     * @param {Object} e - 点击事件对象
     * @param {number} e.detail.index - 被点击项的索引
     */
    gridItemClick(e) {
      console.log(e)
      const index = e.detail.index
      console.log('点击了第', index, '项')
      switch(index) {
        case 0:
          uni.navigateTo({ url: '/pages/home/<USER>/index' })
          break
        case 1:
          uni.navigateTo({ url: '/pages/home/<USER>/index' })
          break
        case 2:
          uni.navigateTo({ url: '/pages/home/<USER>/index' })
          break
      }
    },
  }
}
</script>

<style scoped lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30upx 20upx;
}

.menu-card {
  background-color: #fff;
  border-radius: 16upx;
  padding: 0 20upx 20upx;
  margin-bottom: 30upx;
  box-shadow: 0 2upx 12upx rgba(0, 0, 0, 0.04);
}

.menu-title {
  display: flex;
  align-items: center;
  padding: 24upx 0;
  margin-bottom: 10upx;
}

.title-bar {
  width: 6upx;
  height: 28upx;
  background-color: #3a79fe;
  margin-right: 16upx;
  border-radius: 3upx;
}

.title-text {
  font-size: 30upx;
  font-weight: 600;
  color: #333;
}

.grid-item-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30upx 0;
  background-color: #fff;
}

.grid-icon {
  width: 96upx;
  height: 96upx;
  margin-bottom: 12upx;
  padding: 16upx;
  background-color: #f5f7fa;
  border-radius: 12upx;
}

.text {
  font-size: 26upx;
  color: #333;
  text-align: center;
}
</style>
