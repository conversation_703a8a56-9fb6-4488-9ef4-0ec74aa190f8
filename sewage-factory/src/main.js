/**
 * 3D污水处理工艺流程系统 - 主程序入口
 * 基于Three.js构建的沉浸式3D可视化系统
 */

import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import * as TWEEN from '@tweenjs/tween.js';

import { SceneManager } from './scene/SceneManager.js';
import { LightingManager } from './scene/LightingManager.js';
import { CameraController } from './scene/CameraController.js';
import { EquipmentManager } from './models/EquipmentManager.js';
import { ParticleSystem } from './effects/ParticleSystem.js';
import { AnimationManager } from './effects/AnimationManager.js';
import { UIManager } from './ui/UIManager.js';
import { DataManager } from './utils/DataManager.js';
import { EventHandler } from './utils/EventHandler.js';
import { AudioManager } from './utils/AudioManager.js';

class SewageTreatmentApp {
    constructor() {
        this.container = document.getElementById('canvas-container');
        this.loadingScreen = document.getElementById('loadingScreen');
        
        // 核心组件
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        // 管理器
        this.sceneManager = null;
        this.lightingManager = null;
        this.cameraController = null;
        this.equipmentManager = null;
        this.particleSystem = null;
        this.animationManager = null;
        this.uiManager = null;
        this.dataManager = null;
        this.eventHandler = null;
        this.audioManager = null;
        
        // 系统状态
        this.isSystemRunning = false;
        this.isLoaded = false;
        this.clock = new THREE.Clock();
        
        // 性能监控
        this.stats = null;
        
        this.init();
    }

    async init() {
        try {
            this.updateLoadingProgress('初始化渲染器...', 10);
            await this.initRenderer();
            
            this.updateLoadingProgress('创建3D场景...', 20);
            await this.initScene();
            
            this.updateLoadingProgress('设置光照系统...', 30);
            await this.initLighting();
            
            this.updateLoadingProgress('配置相机控制...', 40);
            await this.initCamera();
            
            this.updateLoadingProgress('加载设备模型...', 50);
            await this.initEquipment();
            
            this.updateLoadingProgress('初始化粒子系统...', 70);
            await this.initEffects();
            
            this.updateLoadingProgress('设置用户界面...', 80);
            await this.initUI();
            
            this.updateLoadingProgress('配置事件处理...', 90);
            await this.initEventHandlers();
            
            this.updateLoadingProgress('启动渲染循环...', 95);
            await this.initAudio();
            
            this.updateLoadingProgress('完成加载!', 100);
            
            // 延迟隐藏加载界面
            setTimeout(() => {
                this.hideLoadingScreen();
                this.startRenderLoop();
                this.isLoaded = true;
            }, 500);
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('系统初始化失败，请刷新页面重试。');
        }
    }

    async initRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true,
            powerPreference: 'high-performance'
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        this.container.appendChild(this.renderer.domElement);
    }

    async initScene() {
        this.sceneManager = new SceneManager();
        this.scene = this.sceneManager.scene;
    }

    async initLighting() {
        this.lightingManager = new LightingManager(this.scene);
        this.lightingManager.setupLighting();
    }

    async initCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        
        this.cameraController = new CameraController(this.camera, this.renderer.domElement);
        this.controls = this.cameraController.controls;
        
        // 设置初始相机位置
        this.camera.position.set(30, 20, 30);
        this.camera.lookAt(0, 0, 0);
    }

    async initEquipment() {
        this.equipmentManager = new EquipmentManager(this.scene);
        await this.equipmentManager.loadAllEquipment();
    }

    async initEffects() {
        this.particleSystem = new ParticleSystem(this.scene);
        this.animationManager = new AnimationManager();
        
        // 注册设备到动画管理器
        if (this.equipmentManager) {
            this.animationManager.registerEquipment(this.equipmentManager.getAllEquipment());
        }
    }

    async initUI() {
        this.uiManager = new UIManager();
        this.dataManager = new DataManager();
        
        // 连接UI和数据管理器
        this.uiManager.setDataManager(this.dataManager);
        this.dataManager.startDataSimulation();
    }

    async initEventHandlers() {
        this.eventHandler = new EventHandler(
            this.camera,
            this.scene,
            this.renderer.domElement
        );
        
        // 注册设备点击事件
        this.eventHandler.onEquipmentClick = (equipment) => {
            this.uiManager.showEquipmentInfo(equipment);
        };
        
        // 注册UI事件
        this.setupUIEvents();
        
        // 窗口大小调整事件
        window.addEventListener('resize', () => this.onWindowResize());
    }

    async initAudio() {
        this.audioManager = new AudioManager();
        await this.audioManager.loadSounds();
    }

    setupUIEvents() {
        // 系统控制按钮
        document.getElementById('startSystem').addEventListener('click', () => {
            this.startSystem();
        });
        
        document.getElementById('stopSystem').addEventListener('click', () => {
            this.stopSystem();
        });
        
        document.getElementById('resetView').addEventListener('click', () => {
            this.resetView();
        });
        
        // 视角切换
        document.getElementById('viewMode').addEventListener('change', (e) => {
            this.changeViewMode(e.target.value);
        });
        
        // 显示选项
        document.getElementById('showParticles').addEventListener('change', (e) => {
            this.particleSystem.setVisible(e.target.checked);
        });
        
        document.getElementById('showPipeline').addEventListener('change', (e) => {
            this.equipmentManager.setPipelineVisible(e.target.checked);
        });
        
        document.getElementById('showLabels').addEventListener('change', (e) => {
            this.equipmentManager.setLabelsVisible(e.target.checked);
        });
        
        document.getElementById('showServiceArea').addEventListener('change', (e) => {
            this.sceneManager.setServiceAreaVisible(e.target.checked);
        });
        
        // 音效控制
        document.getElementById('enableSound').addEventListener('change', (e) => {
            this.audioManager.setEnabled(e.target.checked);
        });
        
        document.getElementById('volumeControl').addEventListener('input', (e) => {
            this.audioManager.setVolume(e.target.value / 100);
        });

        // 管道控制
        document.getElementById('pipeOpacity').addEventListener('input', (e) => {
            this.equipmentManager.setPipelineOpacity(parseFloat(e.target.value));
        });

        document.getElementById('waterOpacity').addEventListener('input', (e) => {
            this.equipmentManager.setWaterFlowOpacity(parseFloat(e.target.value));
        });

        document.getElementById('flowSpeed').addEventListener('input', (e) => {
            this.equipmentManager.setFlowSpeed(parseFloat(e.target.value));
        });

        document.getElementById('reverseFlow').addEventListener('change', (e) => {
            this.equipmentManager.setFlowDirection(e.target.checked ? -1 : 1);
        });

        document.getElementById('pipelineParticles').addEventListener('change', (e) => {
            this.equipmentManager.setPipelineParticlesEnabled(e.target.checked);
        });
    }

    startSystem() {
        if (this.isSystemRunning) return;
        
        this.isSystemRunning = true;
        this.animationManager.startAllAnimations();
        this.particleSystem.startAllEffects();
        this.audioManager.playAmbientSounds();
        
        console.log('污水处理系统已启动');
    }

    stopSystem() {
        if (!this.isSystemRunning) return;
        
        this.isSystemRunning = false;
        this.animationManager.stopAllAnimations();
        this.particleSystem.stopAllEffects();
        this.audioManager.stopAmbientSounds();
        
        console.log('污水处理系统已停止');
    }

    resetView() {
        this.cameraController.resetToOverview();
    }

    changeViewMode(mode) {
        this.cameraController.changeViewMode(mode);
    }

    startRenderLoop() {
        const animate = () => {
            requestAnimationFrame(animate);
            
            const deltaTime = this.clock.getDelta();
            
            // 更新各个系统
            if (this.isSystemRunning) {
                this.animationManager.update(deltaTime);
                this.particleSystem.update(deltaTime);
                // 更新设备管理器（包括所有设备和管道系统）
                if (this.equipmentManager) {
                    this.equipmentManager.update(deltaTime);
                }
            }
            
            // 更新场景管理器（包括服务区动态效果）
            if (this.sceneManager) {
                this.sceneManager.update(deltaTime);
            }
            
            this.controls.update();
            this.eventHandler.update();
            TWEEN.update();
            
            // 渲染场景
            this.renderer.render(this.scene, this.camera);
            
            // 更新性能统计
            if (this.stats) {
                this.stats.update();
            }
        };
        
        animate();
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    updateLoadingProgress(text, progress) {
        const progressElement = document.getElementById('loadingProgress');
        if (progressElement) {
            progressElement.textContent = `${text} (${progress}%)`;
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }

    showError(message) {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div style="color: #e74c3c; text-align: center;">
                    <h2>❌ 加载失败</h2>
                    <p>${message}</p>
                    <button onclick="location.reload()" style="
                        background: #e74c3c; 
                        color: white; 
                        border: none; 
                        padding: 10px 20px; 
                        border-radius: 5px; 
                        cursor: pointer;
                        margin-top: 20px;
                    ">重新加载</button>
                </div>
            `;
        }
    }

    // 公共API方法
    getSystemStatus() {
        return {
            isRunning: this.isSystemRunning,
            isLoaded: this.isLoaded,
            equipmentCount: this.equipmentManager ? this.equipmentManager.getEquipmentCount() : 0,
            particleCount: this.particleSystem ? this.particleSystem.getParticleCount() : 0
        };
    }

    dispose() {
        // 清理资源
        if (this.animationManager) this.animationManager.dispose();
        if (this.particleSystem) this.particleSystem.dispose();
        if (this.equipmentManager) this.equipmentManager.dispose();
        if (this.audioManager) this.audioManager.dispose();
        if (this.renderer) this.renderer.dispose();
    }
}

// 启动应用
const app = new SewageTreatmentApp();

// 全局访问
window.sewageApp = app;
