/**
 * 场景管理器 - 负责3D场景的创建和基础设置
 */

import * as THREE from 'three';
import { HighwayServiceAreaManager } from './HighwayServiceAreaManager.js';

export class SceneManager {
    constructor() {
        this.scene = new THREE.Scene();
        this.highwayServiceArea = null;
        this.setupScene();
        this.createEnvironment();
        this.createHighwayServiceArea();
    }

    setupScene() {
        // 设置场景背景
        this.createSkybox();
        
        // 设置雾效
        this.scene.fog = new THREE.Fog(0xcccccc, 100, 500);
        
        // 坐标轴辅助器已移除，避免产生白色细线
    }

    createSkybox() {
        // 创建天空盒
        const skyGeometry = new THREE.SphereGeometry(400, 32, 32);
        
        // 创建天空渐变材质
        const skyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                topColor: { value: new THREE.Color(0x0077ff) },
                bottomColor: { value: new THREE.Color(0xffffff) },
                offset: { value: 33 },
                exponent: { value: 0.6 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                void main() {
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 topColor;
                uniform vec3 bottomColor;
                uniform float offset;
                uniform float exponent;
                varying vec3 vWorldPosition;
                void main() {
                    float h = normalize(vWorldPosition + offset).y;
                    gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
                }
            `,
            side: THREE.BackSide
        });

        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
        
        this.sky = sky;
    }

    createEnvironment() {
        // 创建地面
        this.createGround();
        
        // 创建工厂建筑背景
        this.createFactoryBuildings();
        
        // 添加环境装饰
        this.addEnvironmentDetails();
    }

    createGround() {
        // 主地面 - 扩大宽度以容纳所有设备
        const groundGeometry = new THREE.PlaneGeometry(300, 120);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x7c7c7c,
            transparent: true,
            opacity: 0.8
        });

        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -1;
        ground.receiveShadow = true;
        this.scene.add(ground);

        // 添加地面网格线 - 扩大网格
        const gridHelper = new THREE.GridHelper(300, 50, 0x333333, 0x333333);
        gridHelper.position.y = -0.9;
        gridHelper.material.opacity = 0.2;
        gridHelper.material.transparent = true;
        this.scene.add(gridHelper);

        // 工艺区域地面 - 扩大以覆盖所有设备
        const processAreaGeometry = new THREE.PlaneGeometry(220, 30);
        const processAreaMaterial = new THREE.MeshLambertMaterial({
            color: 0x95a5a6,
            transparent: true,
            opacity: 0.9
        });

        const processArea = new THREE.Mesh(processAreaGeometry, processAreaMaterial);
        processArea.rotation.x = -Math.PI / 2;
        processArea.position.set(10, -0.8, 0);  // 调整位置以居中
        processArea.receiveShadow = true;
        this.scene.add(processArea);

        this.ground = ground;
        this.processArea = processArea;
    }

    createFactoryBuildings() {
        // 控制室建筑
        const controlRoomGeometry = new THREE.BoxGeometry(8, 6, 6);
        const controlRoomMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e,
            shininess: 30
        });
        
        const controlRoom = new THREE.Mesh(controlRoomGeometry, controlRoomMaterial);
        controlRoom.position.set(-40, 3, -15);
        controlRoom.castShadow = true;
        controlRoom.receiveShadow = true;
        this.scene.add(controlRoom);

        // 添加控制室窗户
        const windowGeometry = new THREE.PlaneGeometry(1.5, 1);
        const windowMaterial = new THREE.MeshPhongMaterial({
            color: 0x87ceeb,
            transparent: true,
            opacity: 0.7,
            emissive: 0x001122
        });

        for (let i = 0; i < 3; i++) {
            const window = new THREE.Mesh(windowGeometry, windowMaterial);
            window.position.set(-36, 4 + i * 0.5, -15);
            this.scene.add(window);
        }

        // 办公楼
        const officeGeometry = new THREE.BoxGeometry(12, 8, 8);
        const officeMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50
        });
        
        const office = new THREE.Mesh(officeGeometry, officeMaterial);
        office.position.set(-50, 4, 20);
        office.castShadow = true;
        office.receiveShadow = true;
        this.scene.add(office);

        // 储存仓库
        const warehouseGeometry = new THREE.BoxGeometry(15, 5, 10);
        const warehouseMaterial = new THREE.MeshPhongMaterial({
            color: 0x7f8c8d
        });
        
        const warehouse = new THREE.Mesh(warehouseGeometry, warehouseMaterial);
        warehouse.position.set(80, 2.5, -20);
        warehouse.castShadow = true;
        warehouse.receiveShadow = true;
        this.scene.add(warehouse);

        this.buildings = {
            controlRoom,
            office,
            warehouse
        };
    }

    addEnvironmentDetails() {
        // 添加围栏
        this.createFencing();
        
        // 添加道路
        this.createRoads();
        
        // 添加绿化
        this.createLandscaping();
        
        // 添加路灯
        this.createStreetLights();
    }

    createFencing() {
        const fenceGroup = new THREE.Group();
        
        const fencePostGeometry = new THREE.CylinderGeometry(0.1, 0.1, 3);
        const fencePostMaterial = new THREE.MeshPhongMaterial({ color: 0x654321 });
        
        const fenceRailGeometry = new THREE.BoxGeometry(5, 0.1, 0.1);
        const fenceRailMaterial = new THREE.MeshPhongMaterial({ color: 0x8b4513 });

        // 创建围栏段
        for (let x = -90; x <= 90; x += 5) {
            // 前围栏
            const post1 = new THREE.Mesh(fencePostGeometry, fencePostMaterial);
            post1.position.set(x, 1.5, -40);
            fenceGroup.add(post1);
            
            if (x < 90) {
                const rail1 = new THREE.Mesh(fenceRailGeometry, fenceRailMaterial);
                rail1.position.set(x + 2.5, 2, -40);
                fenceGroup.add(rail1);
            }
            
            // 后围栏
            const post2 = new THREE.Mesh(fencePostGeometry, fencePostMaterial);
            post2.position.set(x, 1.5, 40);
            fenceGroup.add(post2);
            
            if (x < 90) {
                const rail2 = new THREE.Mesh(fenceRailGeometry, fenceRailMaterial);
                rail2.position.set(x + 2.5, 2, 40);
                fenceGroup.add(rail2);
            }
        }

        this.scene.add(fenceGroup);
        this.fencing = fenceGroup;
    }

    createRoads() {
        // 主干道 - 扩大宽度
        const mainRoadGeometry = new THREE.PlaneGeometry(300, 6);
        const mainRoadMaterial = new THREE.MeshLambertMaterial({
            color: 0x2c2c2c
        });

        const mainRoad = new THREE.Mesh(mainRoadGeometry, mainRoadMaterial);
        mainRoad.rotation.x = -Math.PI / 2;
        mainRoad.position.set(0, -0.7, -25);
        this.scene.add(mainRoad);

        // 工艺区道路 - 扩大长度
        const processRoadGeometry = new THREE.PlaneGeometry(4, 100);
        const processRoadMaterial = new THREE.MeshLambertMaterial({
            color: 0x2c2c2c
        });

        const processRoad = new THREE.Mesh(processRoadGeometry, processRoadMaterial);
        processRoad.rotation.x = -Math.PI / 2;
        processRoad.position.set(20, -0.7, 15);
        this.scene.add(processRoad);

        // 添加道路标线 - 扩大宽度
        const lineGeometry = new THREE.PlaneGeometry(300, 0.2);
        const lineMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff
        });
        
        const centerLine = new THREE.Mesh(lineGeometry, lineMaterial);
        centerLine.rotation.x = -Math.PI / 2;
        centerLine.position.set(0, -0.65, -25);
        this.scene.add(centerLine);

        this.roads = {
            mainRoad,
            processRoad,
            centerLine
        };
    }

    createLandscaping() {
        const landscapeGroup = new THREE.Group();

        // 设备区域边界（居中布局后的新边界）
        const equipmentBounds = {
            minX: -55,
            maxX: 55,
            minZ: -22,
            maxZ: 18
        };

        // 规范化树木排列 - 创建网格化布局，避开设备区域
        const treeSpacing = 15; // 增加树木间距
        const treeRows = [
            // 前排树木（设备前方）- 减少数量
            { z: 25, count: 6, startX: -45 },
            { z: 35, count: 5, startX: -37.5 },

            // 后排树木（设备后方）- 减少数量
            { z: -40, count: 6, startX: -45 },
            { z: -50, count: 5, startX: -37.5 },

            // 左侧树木 - 移动到设备外边
            { x: -100, zStart: -15, zEnd: 15, count: 4 },

            // 右侧树木 - 移动到设备外边，避开三级产水池
            { x: 140, zStart: -15, zEnd: 15, count: 4 }
        ];

        // 创建横排树木
        treeRows.slice(0, 4).forEach(row => {
            for (let i = 0; i < row.count; i++) {
                const tree = this.createTree();
                tree.position.set(
                    row.startX + i * treeSpacing + (Math.random() - 0.5) * 2, // 添加小幅随机偏移
                    0,
                    row.z + (Math.random() - 0.5) * 3
                );
                landscapeGroup.add(tree);
            }
        });

        // 创建竖排树木
        treeRows.slice(4).forEach(row => {
            const zSpacing = (row.zEnd - row.zStart) / (row.count - 1);
            for (let i = 0; i < row.count; i++) {
                const tree = this.createTree();
                tree.position.set(
                    row.x + (Math.random() - 0.5) * 3,
                    0,
                    row.zStart + i * zSpacing + (Math.random() - 0.5) * 2
                );
                landscapeGroup.add(tree);
            }
        });

        // 添加一些装饰性灌木丛（在设备区域外围）
        this.createShrubs(landscapeGroup, equipmentBounds);

        this.scene.add(landscapeGroup);
        this.landscaping = landscapeGroup;
    }

    createShrubs(landscapeGroup, equipmentBounds) {
        // 移除所有灌木丛球体，保持场景清洁
        // 不再创建任何装饰性的绿色球体
        console.log('灌木丛球体已移除，保持场景清洁');
    }

    createShrub() {
        // 不再创建灌木球体
        return new THREE.Group();
    }

    createTree() {
        const treeGroup = new THREE.Group();
        
        // 树干
        const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4);
        const trunkMaterial = new THREE.MeshPhongMaterial({ color: 0x8b4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 2;
        treeGroup.add(trunk);
        
        // 树冠
        const crownGeometry = new THREE.SphereGeometry(2.5);
        const crownMaterial = new THREE.MeshPhongMaterial({ color: 0x228b22 });
        const crown = new THREE.Mesh(crownGeometry, crownMaterial);
        crown.position.y = 5;
        crown.scale.y = 0.8;
        treeGroup.add(crown);
        
        return treeGroup;
    }

    createStreetLights() {
        const lightGroup = new THREE.Group();
        
        for (let x = -80; x <= 80; x += 20) {
            const streetLight = this.createStreetLight();
            streetLight.position.set(x, 0, -30);
            lightGroup.add(streetLight);
        }

        this.scene.add(lightGroup);
        this.streetLights = lightGroup;
    }

    createStreetLight() {
        const lightGroup = new THREE.Group();
        
        // 灯杆
        const poleGeometry = new THREE.CylinderGeometry(0.15, 0.2, 8);
        const poleMaterial = new THREE.MeshPhongMaterial({ color: 0x444444 });
        const pole = new THREE.Mesh(poleGeometry, poleMaterial);
        pole.position.y = 4;
        lightGroup.add(pole);
        
        // 灯头
        const lampGeometry = new THREE.SphereGeometry(0.5);
        const lampMaterial = new THREE.MeshPhongMaterial({
            color: 0xffffcc,
            emissive: 0x222200
        });
        const lamp = new THREE.Mesh(lampGeometry, lampMaterial);
        lamp.position.y = 7.5;
        lightGroup.add(lamp);
        
        return lightGroup;
    }

    createHighwayServiceArea() {
        // 创建高速服务区场景
        this.highwayServiceArea = new HighwayServiceAreaManager(this.scene);
    }

    // 环境控制方法
    setTimeOfDay(hour) {
        // 根据时间调整天空颜色和光照
        const normalizedHour = hour / 24;
        
        if (this.sky) {
            const topColor = new THREE.Color();
            const bottomColor = new THREE.Color();
            
            if (hour >= 6 && hour <= 18) {
                // 白天
                topColor.setHex(0x0077ff);
                bottomColor.setHex(0xffffff);
            } else {
                // 夜晚
                topColor.setHex(0x000033);
                bottomColor.setHex(0x000066);
            }
            
            this.sky.material.uniforms.topColor.value = topColor;
            this.sky.material.uniforms.bottomColor.value = bottomColor;
        }

        // 同步更新服务区时间
        if (this.highwayServiceArea) {
            this.highwayServiceArea.setTimeOfDay(hour);
        }
    }

    setWeather(weather) {
        // 设置天气效果
        switch (weather) {
            case 'sunny':
                this.scene.fog.density = 0.0001;
                break;
            case 'cloudy':
                this.scene.fog.density = 0.0005;
                break;
            case 'rainy':
                this.scene.fog.density = 0.001;
                // TODO: 添加雨滴效果
                break;
            case 'foggy':
                this.scene.fog.density = 0.002;
                break;
        }
    }

    // 更新方法，用于动态效果
    update(deltaTime) {
        if (this.highwayServiceArea) {
            this.highwayServiceArea.update(deltaTime);
        }
    }

    // 服务区控制方法
    setServiceAreaVisible(visible) {
        if (this.highwayServiceArea) {
            this.highwayServiceArea.setVisible(visible);
        }
    }

    dispose() {
        // 清理服务区资源
        if (this.highwayServiceArea) {
            this.highwayServiceArea.dispose();
        }

        // 清理场景资源
        this.scene.traverse((child) => {
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
        });
    }
}
