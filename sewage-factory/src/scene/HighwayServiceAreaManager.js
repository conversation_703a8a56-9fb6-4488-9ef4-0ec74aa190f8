/**
 * 高速服务区场景管理器 - 负责创建高速服务区的3D场景
 */

import * as THREE from 'three';

export class HighwayServiceAreaManager {
    constructor(scene) {
        this.scene = scene;
        this.serviceAreaGroup = new THREE.Group();
        this.serviceAreaGroup.name = 'ServiceArea';
        
        // 服务区位置配置 - 位于污水处理设备前方
        this.serviceAreaPosition = {
            x: 0,
            y: 0,
            z: -95  // 向前移动，避免扩大后接近污水设备区域
        };
        
        this.buildings = {};
        this.vehicles = [];
        this.decorations = {};
        
        this.createServiceArea();
        this.scene.add(this.serviceAreaGroup);
    }

    // 使用 Canvas 生成中文标签的精灵
    createLabelSprite(text, options = {}) {
        const {
            font = 'bold 64px 微软雅黑, Microsoft YaHei, Arial',
            fillStyle = '#ffffff',
            strokeStyle = 'rgba(0,0,0,0.65)',
            strokeWidth = 10,
            padding = 20,
            scale = 8 // 控制世界单位缩放
        } = options;

        const canvas = document.createElement('canvas');
        canvas.width = 1024;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.font = font;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        // 边描边阴影效果，增强可读性
        ctx.lineWidth = strokeWidth;
        ctx.strokeStyle = strokeStyle;
        ctx.strokeText(text, centerX, centerY);

        // 填充文字
        ctx.fillStyle = fillStyle;
        ctx.fillText(text, centerX, centerY);

        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.anisotropy = 4;

        const material = new THREE.SpriteMaterial({ 
            map: texture, 
            transparent: true,
            depthTest: false,
            depthWrite: false,
            alphaTest: 0.02
        });
        const sprite = new THREE.Sprite(material);

        // 根据画布比例设置精灵缩放，保持长宽比
        const aspect = canvas.width / canvas.height; // 4:1
        sprite.scale.set(scale * aspect, scale, 1);
        // 保证渲染顺序靠前，避免被其它几何体遮挡
        sprite.renderOrder = 999;
        return sprite;
    }

    createServiceArea() {
        // 设置服务区组的位置
        this.serviceAreaGroup.position.set(
            this.serviceAreaPosition.x,
            this.serviceAreaPosition.y,
            this.serviceAreaPosition.z
        );

        // 创建服务区各个组件
        this.createServiceAreaGround();
        this.createMainBuildings();
        this.createParkingArea();
        this.createHighwayConnection();
        this.createLandscaping();
        this.createSignage();
        this.createVehicles();
        this.createLighting();
    }

    createServiceAreaGround() {
        // 服务区主地面
        // 与主场地地面(300x120)平齐
        const groundGeometry = new THREE.PlaneGeometry(300, 120);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x8c8c8c,
            transparent: true,
            opacity: 0.9
        });

        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -0.5;
        ground.receiveShadow = true;
        this.serviceAreaGroup.add(ground);

        // 服务区边界标线（以圆环近似，尺寸匹配300x120外接的短半径约60、长半径约150，这里仅做外圈视觉参考）
        const borderGeometry = new THREE.RingGeometry(148, 150, 48);
        const borderMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.8
        });

        const border = new THREE.Mesh(borderGeometry, borderMaterial);
        border.rotation.x = -Math.PI / 2;
        border.position.y = -0.4;
        this.serviceAreaGroup.add(border);
    }

    createMainBuildings() {
        // 仅保留并放大“红框建筑”风格：平顶+大檐篷（作为主建筑）
        const mainBaseGeometry = new THREE.BoxGeometry(40, 6, 20); // 主体盒
        const mainBaseMaterial = new THREE.MeshPhongMaterial({ color: 0xcfa64a, shininess: 30 });
        const mainBase = new THREE.Mesh(mainBaseGeometry, mainBaseMaterial);
        mainBase.position.set(0, 3, -8);
        mainBase.castShadow = true;
        mainBase.receiveShadow = true;
        this.serviceAreaGroup.add(mainBase);

        // 大檐篷（加大作为门头檐板）
        const mainCanopyGeometry = new THREE.BoxGeometry(48, 0.8, 26);
        const mainCanopyMaterial = new THREE.MeshPhongMaterial({ color: 0xdedede, transparent: true, opacity: 0.9, depthWrite: false });
        const mainCanopy = new THREE.Mesh(mainCanopyGeometry, mainCanopyMaterial);
        mainCanopy.position.set(0, 7, -8);
        mainCanopy.castShadow = true;
        this.serviceAreaGroup.add(mainCanopy);

        // 前立面玻璃（保留红框建筑的玻璃感）
        const facadeGeometry = new THREE.BoxGeometry(38, 6, 2);
        const facadeMaterial = new THREE.MeshPhongMaterial({ color: 0x7fb3d5, transparent: true, opacity: 0.5, shininess: 80 });
        const facade = new THREE.Mesh(facadeGeometry, facadeMaterial);
        facade.position.set(0, 4, -2);
        this.serviceAreaGroup.add(facade);

        // 标签组
        const labelsGroup = new THREE.Group();
        labelsGroup.name = 'ServiceAreaLabels';
        const mainLabel = this.createLabelSprite('潜江服务区', { scale: 14 });
        mainLabel.position.set(0, 8.2, -7.2); // 稍微向摄像机方向偏移，避免与檐篷共面
        labelsGroup.add(mainLabel);

        // 右侧：独立加油站（较小）
        const gasBaseGeometry = new THREE.BoxGeometry(16, 3, 8);
        const gasBaseMaterial = new THREE.MeshPhongMaterial({ color: 0xf39c12 });
        const gasBase = new THREE.Mesh(gasBaseGeometry, gasBaseMaterial);
        gasBase.position.set(40, 1.5, 10);
        gasBase.castShadow = true;
        this.serviceAreaGroup.add(gasBase);

        const gasCanopyGeometry = new THREE.BoxGeometry(22, 0.6, 14);
        const gasCanopyMaterial = new THREE.MeshPhongMaterial({ color: 0xffffff, transparent: true, opacity: 0.92, depthWrite: false });
        const gasCanopy = new THREE.Mesh(gasCanopyGeometry, gasCanopyMaterial);
        gasCanopy.position.set(40, 5, 10);
        this.serviceAreaGroup.add(gasCanopy);

        // 加油机
        for (let i = 0; i < 3; i++) {
            const pumpGeometry = new THREE.BoxGeometry(1.2, 2.2, 0.6);
            const pumpMaterial = new THREE.MeshPhongMaterial({ color: 0x2c3e50 });
            const pump = new THREE.Mesh(pumpGeometry, pumpMaterial);
            pump.position.set(34 + i * 6, 1.1, 10);
            this.serviceAreaGroup.add(pump);
        }
        const gasLabel = this.createLabelSprite('潜江加油站', { scale: 8 });
        gasLabel.position.set(40, 6, 10);
        labelsGroup.add(gasLabel);

        // 左侧：洗手间建筑
        const wcGeometry = new THREE.BoxGeometry(14, 5, 10);
        const wcMaterial = new THREE.MeshPhongMaterial({ color: 0x95a5a6, shininess: 20 });
        const wc = new THREE.Mesh(wcGeometry, wcMaterial);
        wc.position.set(-40, 2.5, 6);
        wc.castShadow = true;
        this.serviceAreaGroup.add(wc);
        const wcLabel = this.createLabelSprite('洗手间', { scale: 7 });
        wcLabel.position.set(-40, 6.5, 6);
        labelsGroup.add(wcLabel);

        // 添加标签组
        this.serviceAreaGroup.add(labelsGroup);
        this.decorations.labels = labelsGroup;

        // 记录对象
        this.buildings = {
            mainBuilding: mainBase,
            gasStation: gasBase,
            restroom: wc
        };
    }

    createParkingArea() {
        // 停车场地面
        // 匹配更大地面比例的停车区
        const parkingGeometry = new THREE.PlaneGeometry(240, 60);
        const parkingMaterial = new THREE.MeshLambertMaterial({
            color: 0x696969,
            transparent: true,
            opacity: 0.8
        });

        const parking = new THREE.Mesh(parkingGeometry, parkingMaterial);
        parking.rotation.x = -Math.PI / 2;
        parking.position.set(0, -0.3, 5);
        this.serviceAreaGroup.add(parking);

        // 停车位标线
        const lineGroup = new THREE.Group();
        
        // 横向停车位（加密与扩展）
        for (let i = 0; i < 20; i++) {
            for (let j = 0; j < 10; j++) {
                const lineGeometry = new THREE.PlaneGeometry(4, 0.1);
                const lineMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });
                
                // 停车位边框
                for (let side = 0; side < 4; side++) {
                    const line = new THREE.Mesh(lineGeometry, lineMaterial);
                    line.rotation.x = -Math.PI / 2;
                    
                    const x = -95 + i * 10;   // 更宽
                    const z = -25 + j * 6.5;  // 更深
                    
                    if (side === 0 || side === 2) {
                        line.position.set(x + (side === 0 ? -2 : 2), -0.25, z);
                    } else {
                        line.rotation.z = Math.PI / 2;
                        line.position.set(x, -0.25, z + (side === 1 ? -2.5 : 2.5));
                    }
                    
                    lineGroup.add(line);
                }
            }
        }
        
        this.serviceAreaGroup.add(lineGroup);
    }

    createHighwayConnection() {
        // 主干道（东西走向，沿X轴）- 拉满与地面宽度匹配
        const mainRoadGeometry = new THREE.PlaneGeometry(320, 14);
        const mainRoadMaterial = new THREE.MeshLambertMaterial({ color: 0x2c2c2c });
        const mainRoad = new THREE.Mesh(mainRoadGeometry, mainRoadMaterial);
        mainRoad.rotation.x = -Math.PI / 2;
        mainRoad.position.set(0, -0.4, 0);
        this.serviceAreaGroup.add(mainRoad);

        // 中央标线（东西向）
        const centerLineGeometry = new THREE.PlaneGeometry(320, 0.35);
        const centerLineMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });
        const centerLine = new THREE.Mesh(centerLineGeometry, centerLineMaterial);
        centerLine.rotation.x = -Math.PI / 2;
        centerLine.position.set(0, -0.35, 0);
        this.serviceAreaGroup.add(centerLine);

        // 与停车区连接的通道（南北短连线）
        const connectorGeometry = new THREE.PlaneGeometry(12, 8);
        const connectorMaterial = new THREE.MeshLambertMaterial({ color: 0x2c2c2c });
        const northConnector = new THREE.Mesh(connectorGeometry, connectorMaterial);
        northConnector.rotation.x = -Math.PI / 2;
        northConnector.position.set(0, -0.38, 12);
        this.serviceAreaGroup.add(northConnector);

        const southConnector = new THREE.Mesh(connectorGeometry, connectorMaterial);
        southConnector.rotation.x = -Math.PI / 2;
        southConnector.position.set(0, -0.38, -12);
        this.serviceAreaGroup.add(southConnector);
    }

    createLandscaping() {
        const landscapeGroup = new THREE.Group();

        // 在服务区周围添加绿化
        const positions = [
            { x: -110, z: -50, count: 6 },
            { x: 110, z: -50, count: 6 },
            { x: -110, z: 50, count: 6 },
            { x: 110, z: 50, count: 6 },
            { x: 0, z: -70, count: 8 },
            { x: 0, z: 70, count: 8 }
        ];

        positions.forEach(pos => {
            for (let i = 0; i < pos.count; i++) {
                const tree = this.createServiceAreaTree();
                tree.position.set(
                    pos.x + (i - pos.count/2) * 8 + (Math.random() - 0.5) * 3,
                    0,
                    pos.z + (Math.random() - 0.5) * 5
                );
                landscapeGroup.add(tree);
            }
        });

        // 添加花坛
        this.createFlowerBeds(landscapeGroup);

        this.serviceAreaGroup.add(landscapeGroup);
        this.decorations.landscaping = landscapeGroup;
    }

    createServiceAreaTree() {
        const treeGroup = new THREE.Group();
        
        // 树干
        const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.4, 3.5);
        const trunkMaterial = new THREE.MeshPhongMaterial({ color: 0x8b4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 1.75;
        treeGroup.add(trunk);
        
        // 树冠
        const crownGeometry = new THREE.SphereGeometry(2);
        const crownMaterial = new THREE.MeshPhongMaterial({ color: 0x228b22 });
        const crown = new THREE.Mesh(crownGeometry, crownMaterial);
        crown.position.y = 4.5;
        crown.scale.y = 0.9;
        treeGroup.add(crown);
        
        return treeGroup;
    }

    createFlowerBeds(landscapeGroup) {
        const flowerBedPositions = [
            { x: -12, z: -20 },
            { x: 12, z: -20 },
            { x: -30, z: 0 },
            { x: 30, z: 0 }
        ];

        flowerBedPositions.forEach(pos => {
            // 花坛底座
            const bedGeometry = new THREE.CylinderGeometry(3, 3, 0.5);
            const bedMaterial = new THREE.MeshPhongMaterial({ color: 0x8b4513 });
            const bed = new THREE.Mesh(bedGeometry, bedMaterial);
            bed.position.set(pos.x, 0.25, pos.z);
            landscapeGroup.add(bed);

            // 花朵
            for (let i = 0; i < 8; i++) {
                const flowerGeometry = new THREE.SphereGeometry(0.2);
                const flowerMaterial = new THREE.MeshPhongMaterial({ 
                    color: new THREE.Color().setHSL(Math.random(), 0.7, 0.6)
                });
                const flower = new THREE.Mesh(flowerGeometry, flowerMaterial);
                
                const angle = (i / 8) * Math.PI * 2;
                flower.position.set(
                    pos.x + Math.cos(angle) * (1 + Math.random()),
                    0.7,
                    pos.z + Math.sin(angle) * (1 + Math.random())
                );
                landscapeGroup.add(flower);
            }
        });
    }

    createSignage() {
        const signageGroup = new THREE.Group();

        // 主标识牌
        const mainSignGeometry = new THREE.BoxGeometry(8, 3, 0.2);
        const mainSignMaterial = new THREE.MeshPhongMaterial({ color: 0x2c3e50 });
        const mainSign = new THREE.Mesh(mainSignGeometry, mainSignMaterial);
        mainSign.position.set(0, 6, -25);
        signageGroup.add(mainSign);

        // 标识牌支柱
        const poleGeometry = new THREE.CylinderGeometry(0.2, 0.2, 6);
        const poleMaterial = new THREE.MeshPhongMaterial({ color: 0x7f8c8d });
        const pole = new THREE.Mesh(poleGeometry, poleMaterial);
        pole.position.set(0, 3, -25);
        signageGroup.add(pole);

        // 方向指示牌
        const directionSigns = [
            { text: '餐厅', x: -15, z: -15 },
            { text: '便利店', x: 15, z: -15 },
            { text: '加油站', x: 0, z: 5 },
            { text: '停车场', x: 0, z: -5 }
        ];

        directionSigns.forEach(sign => {
            const signGeometry = new THREE.BoxGeometry(3, 1, 0.1);
            const signMaterial = new THREE.MeshPhongMaterial({ color: 0x3498db });
            const dirSign = new THREE.Mesh(signGeometry, signMaterial);
            dirSign.position.set(sign.x, 2.5, sign.z);
            
            // 支柱
            const smallPoleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 2.5);
            const smallPole = new THREE.Mesh(smallPoleGeometry, poleMaterial);
            smallPole.position.set(sign.x, 1.25, sign.z);
            
            signageGroup.add(dirSign);
            signageGroup.add(smallPole);
        });

        this.serviceAreaGroup.add(signageGroup);
        this.decorations.signage = signageGroup;
    }

    createVehicles() {
        const vehicleGroup = new THREE.Group();

        // 在停车场添加一些车辆
        const vehiclePositions = [
            { x: -26, z: -3 }, { x: -18, z: -3 }, { x: -10, z: -3 },
            { x: -26, z: 2 }, { x: -18, z: 2 }, { x: -10, z: 2 },
            { x: 10, z: -3 }, { x: 18, z: -3 }, { x: 26, z: -3 },
            { x: 10, z: 2 }, { x: 18, z: 2 }, { x: 26, z: 2 }
        ];

        vehiclePositions.forEach((pos, index) => {
            if (Math.random() > 0.3) { // 70%概率有车
                const vehicle = this.createVehicle();
                vehicle.position.set(pos.x, 0.5, pos.z);
                vehicle.rotation.y = Math.random() * 0.2 - 0.1; // 轻微随机角度
                vehicleGroup.add(vehicle);
                this.vehicles.push(vehicle);
            }
        });

        this.serviceAreaGroup.add(vehicleGroup);
        this.decorations.vehicles = vehicleGroup;
    }

    createVehicle() {
        const vehicleGroup = new THREE.Group();
        
        // 车身
        const bodyGeometry = new THREE.BoxGeometry(4, 1.2, 1.8);
        const bodyColors = [0xff0000, 0x0000ff, 0xffffff, 0x000000, 0x808080, 0xffff00];
        const bodyMaterial = new THREE.MeshPhongMaterial({ 
            color: bodyColors[Math.floor(Math.random() * bodyColors.length)]
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.6;
        vehicleGroup.add(body);

        // 车顶
        const roofGeometry = new THREE.BoxGeometry(2.5, 0.8, 1.6);
        const roofMaterial = new THREE.MeshPhongMaterial({ color: bodyMaterial.color });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.y = 1.4;
        vehicleGroup.add(roof);

        // 车轮
        const wheelGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.2);
        const wheelMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
        
        const wheelPositions = [
            { x: 1.2, z: 0.8 }, { x: 1.2, z: -0.8 },
            { x: -1.2, z: 0.8 }, { x: -1.2, z: -0.8 }
        ];

        wheelPositions.forEach(pos => {
            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.position.set(pos.x, 0.3, pos.z);
            wheel.rotation.z = Math.PI / 2;
            vehicleGroup.add(wheel);
        });

        return vehicleGroup;
    }

    createLighting() {
        const lightingGroup = new THREE.Group();

        // 服务区路灯
        const lampPositions = [
            { x: -120, z: -30 }, { x: -120, z: -10 }, { x: -120, z: 10 }, { x: -120, z: 30 },
            { x: 120, z: -30 }, { x: 120, z: -10 }, { x: 120, z: 10 }, { x: 120, z: 30 },
            { x: 0, z: -60 }, { x: 0, z: -20 }, { x: 0, z: 20 }, { x: 0, z: 60 }
        ];

        lampPositions.forEach(pos => {
            const lamp = this.createServiceAreaLamp();
            lamp.position.set(pos.x, 0, pos.z);
            lightingGroup.add(lamp);
        });

        this.serviceAreaGroup.add(lightingGroup);
        this.decorations.lighting = lightingGroup;
    }

    createServiceAreaLamp() {
        const lampGroup = new THREE.Group();
        
        // 灯杆
        const poleGeometry = new THREE.CylinderGeometry(0.15, 0.2, 6);
        const poleMaterial = new THREE.MeshPhongMaterial({ color: 0x444444 });
        const pole = new THREE.Mesh(poleGeometry, poleMaterial);
        pole.position.y = 3;
        lampGroup.add(pole);
        
        // 灯头
        const lampGeometry = new THREE.SphereGeometry(0.4);
        const lampMaterial = new THREE.MeshPhongMaterial({
            color: 0xffffcc,
            emissive: 0x111100
        });
        const lamp = new THREE.Mesh(lampGeometry, lampMaterial);
        lamp.position.y = 5.8;
        lampGroup.add(lamp);
        
        return lampGroup;
    }

    // 动画和交互方法
    update(deltaTime) {
        // 可以添加一些动态效果，比如车辆移动、灯光闪烁等
        if (this.vehicles.length > 0) {
            this.vehicles.forEach((vehicle, index) => {
                // 轻微的车辆摇摆效果
                vehicle.rotation.z = Math.sin(Date.now() * 0.001 + index) * 0.005;
            });
        }
    }

    // 控制方法
    setVisible(visible) {
        this.serviceAreaGroup.visible = visible;
    }

    setTimeOfDay(hour) {
        // 根据时间调整服务区灯光
        const isNight = hour < 6 || hour > 18;
        
        if (this.decorations.lighting) {
            this.decorations.lighting.children.forEach(lampGroup => {
                const lamp = lampGroup.children[1]; // 灯头
                if (lamp && lamp.material) {
                    lamp.material.emissive.setHex(isNight ? 0x332200 : 0x111100);
                }
            });
        }
    }

    dispose() {
        // 清理资源
        this.serviceAreaGroup.traverse((child) => {
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
        });
        
        if (this.serviceAreaGroup.parent) {
            this.serviceAreaGroup.parent.remove(this.serviceAreaGroup);
        }
    }
}
