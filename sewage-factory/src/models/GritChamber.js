/**
 * 沉砂池 - 用于去除污水中的砂粒和重质无机物
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class GritChamber extends Equipment {
    constructor(position) {
        super(position);
        
        // 沉砂池特有参数
        this.parameters.set('retentionTime', 120); // 停留时间 (秒)
        this.parameters.set('waterLevel', 2.0); // 水位 (米)
        this.parameters.set('flowRate', 100); // 流量 (m³/h)
        this.parameters.set('sedimentLevel', 0.3); // 沉积物高度 (米)
        
        // 动画相关
        this.sedimentParticles = null;
        this.waterParticles = null;
        this.scraper = null;
        this.scraperRotation = 0;
        this.scraperSpeed = 0.005; // 刮砂器转速
    }

    async create() {
        this.createBasicMaterials();
        this.createBasicGeometries();
        
        // 创建主体结构
        this.createMainStructure();
        
        // 创建刮砂系统
        this.createScrapingSystem();
        
        // 创建沉积物效果
        this.createSedimentEffect();
        
        // 创建水流效果
        this.createWaterFlow();
        
        // 创建排砂系统
        this.createSandRemovalSystem();
        
        // 添加指示灯和标识
        this.createIndicatorLight(0x00ff00, new THREE.Vector3(0, 4, 0));
        this.createNamePlate('沉砂池', new THREE.Vector3(0, 4.5, 0));
        
        // 设置动画
        this.setupAnimations();
        
        this.updateBoundingBox();
    }

    createMainStructure() {
        // 圆形池体
        const poolGeometry = new THREE.CylinderGeometry(3, 3, 2.5);
        const poolMaterial = this.materials.get('concrete');
        
        const pool = this.addMesh(
            poolGeometry,
            poolMaterial,
            new THREE.Vector3(0, 1.25, 0)
        );
        pool.userData.isMainStructure = true;
        
        // 内壁（创建空心效果）
        const innerGeometry = new THREE.CylinderGeometry(2.8, 2.8, 2.3);
        const innerMaterial = new THREE.MeshPhongMaterial({
            color: 0x95a5a6,
            side: THREE.BackSide
        });
        
        const innerWall = this.addMesh(
            innerGeometry,
            innerMaterial,
            new THREE.Vector3(0, 1.25, 0)
        );
        
        // 进水口
        const inletGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.8);
        const inletMaterial = this.materials.get('metal');
        
        const inlet = this.addMesh(
            inletGeometry,
            inletMaterial,
            new THREE.Vector3(-2.5, 1.5, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        inlet.userData.isInlet = true;
        
        // 出水口
        const outlet = this.addMesh(
            inletGeometry,
            inletMaterial,
            new THREE.Vector3(2.5, 1.5, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        outlet.userData.isOutlet = true;
        
        // 水面
        const waterGeometry = new THREE.CircleGeometry(2.7, 32);
        const waterMaterial = this.materials.get('water');
        
        const water = this.addMesh(
            waterGeometry,
            waterMaterial,
            new THREE.Vector3(0, this.parameters.get('waterLevel'), 0),
            new THREE.Euler(-Math.PI / 2, 0, 0)
        );
        water.userData.isWater = true;
        
        this.waterSurface = water;
        
        // 底部锥形结构（便于排砂）
        const bottomConeGeometry = new THREE.ConeGeometry(1, 0.5, 8);
        const bottomCone = this.addMesh(
            bottomConeGeometry,
            poolMaterial,
            new THREE.Vector3(0, 0.25, 0)
        );
    }

    createScrapingSystem() {
        const scraperGroup = new THREE.Group();
        
        // 中心轴
        const shaftGeometry = new THREE.CylinderGeometry(0.1, 0.1, 3);
        const shaftMaterial = this.materials.get('metal');
        
        const shaft = new THREE.Mesh(shaftGeometry, shaftMaterial);
        shaft.position.set(0, 1.5, 0);
        shaft.castShadow = true;
        scraperGroup.add(shaft);
        
        // 刮砂臂
        const armGeometry = new THREE.BoxGeometry(2.5, 0.1, 0.2);
        const armMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50
        });
        
        const arm1 = new THREE.Mesh(armGeometry, armMaterial);
        arm1.position.set(0, 0.3, 0);
        arm1.castShadow = true;
        scraperGroup.add(arm1);
        
        const arm2 = new THREE.Mesh(armGeometry, armMaterial);
        arm2.position.set(0, 0.3, 0);
        arm2.rotation.y = Math.PI / 2;
        arm2.castShadow = true;
        scraperGroup.add(arm2);
        
        // 刮板
        const bladeGeometry = new THREE.BoxGeometry(0.05, 0.3, 2.4);
        const bladeMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        for (let i = 0; i < 4; i++) {
            const angle = (i * Math.PI) / 2;
            const x = Math.cos(angle) * 2.3;
            const z = Math.sin(angle) * 2.3;
            
            const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade.position.set(x, 0.15, z);
            blade.rotation.y = angle;
            blade.castShadow = true;
            scraperGroup.add(blade);
        }
        
        // 驱动电机
        const motorGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.8);
        const motorMaterial = new THREE.MeshPhongMaterial({
            color: 0x3498db
        });
        
        const motor = new THREE.Mesh(motorGeometry, motorMaterial);
        motor.position.set(0, 3.5, 0);
        motor.castShadow = true;
        scraperGroup.add(motor);
        
        // 支撑结构
        const supportGeometry = new THREE.CylinderGeometry(0.05, 0.05, 1);
        const supportMaterial = this.materials.get('metal');
        
        for (let i = 0; i < 3; i++) {
            const angle = (i * 2 * Math.PI) / 3;
            const x = Math.cos(angle) * 1.5;
            const z = Math.sin(angle) * 1.5;
            
            const support = new THREE.Mesh(supportGeometry, supportMaterial);
            support.position.set(x, 3, z);
            support.castShadow = true;
            scraperGroup.add(support);
        }
        
        this.group.add(scraperGroup);
        this.scraper = scraperGroup;
    }

    createSedimentEffect() {
        // 创建沉积砂粒效果
        const particleCount = 500;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const velocities = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 随机分布在池底
            const radius = Math.random() * 2.5;
            const angle = Math.random() * Math.PI * 2;
            
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 0.1 + Math.random() * this.parameters.get('sedimentLevel');
            positions[i3 + 2] = Math.sin(angle) * radius;
            
            // 沉降速度
            velocities[i3] = 0;
            velocities[i3 + 1] = -0.1 - Math.random() * 0.1;
            velocities[i3 + 2] = 0;
            
            // 砂粒颜色（棕黄色）
            const color = new THREE.Color();
            color.setHSL(0.1 + Math.random() * 0.1, 0.6, 0.4 + Math.random() * 0.2);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
            
            // 大小
            sizes[i] = 0.02 + Math.random() * 0.03;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.03,
            transparent: true,
            opacity: 0.8,
            vertexColors: true
        });
        
        this.sedimentParticles = new THREE.Points(geometry, material);
        this.group.add(this.sedimentParticles);
    }

    createWaterFlow() {
        // 创建水流粒子效果
        const particleCount = 300;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 初始位置（进水口附近）
            const radius = Math.random() * 0.5;
            const angle = Math.random() * Math.PI * 2;
            
            positions[i3] = -2 + Math.cos(angle) * radius;
            positions[i3 + 1] = 1 + Math.random() * 1;
            positions[i3 + 2] = Math.sin(angle) * radius;
            
            // 螺旋流动速度
            const flowAngle = Math.random() * Math.PI * 2;
            velocities[i3] = Math.cos(flowAngle) * 0.3;
            velocities[i3 + 1] = -0.05; // 缓慢下沉
            velocities[i3 + 2] = Math.sin(flowAngle) * 0.3;
            
            // 颜色（蓝色水流）
            const color = new THREE.Color(0x006994);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.04,
            transparent: true,
            opacity: 0.6,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.waterParticles = new THREE.Points(geometry, material);
        this.group.add(this.waterParticles);
    }

    createSandRemovalSystem() {
        // 排砂管道
        const pipeGeometry = new THREE.CylinderGeometry(0.15, 0.15, 1);
        const pipeMaterial = this.materials.get('metal');
        
        const sandPipe = this.addMesh(
            pipeGeometry,
            pipeMaterial,
            new THREE.Vector3(0, -0.5, 2.5),
            new THREE.Euler(Math.PI / 4, 0, 0)
        );
        sandPipe.userData.isSandPipe = true;
        
        // 排砂阀门
        const valveGeometry = new THREE.BoxGeometry(0.4, 0.4, 0.4);
        const valveMaterial = new THREE.MeshPhongMaterial({
            color: 0xe74c3c
        });
        
        const valve = this.addMesh(
            valveGeometry,
            valveMaterial,
            new THREE.Vector3(0, -0.2, 3)
        );
        valve.userData.isValve = true;
        
        // 砂斗
        const hopperGeometry = new THREE.ConeGeometry(0.8, 1.5, 8);
        const hopperMaterial = this.materials.get('metal');
        
        const hopper = this.addMesh(
            hopperGeometry,
            hopperMaterial,
            new THREE.Vector3(0, -1.5, 3.5)
        );
        hopper.userData.isHopper = true;
        
        this.sandRemovalSystem = {
            pipe: sandPipe,
            valve: valve,
            hopper: hopper
        };
    }

    setupAnimations() {
        // 刮砂器旋转动画
        this.addAnimation('scraping', (start) => {
            this.scrapingActive = start;
        });
        
        // 水流动画
        this.addAnimation('waterFlow', (start) => {
            this.waterFlowActive = start;
        });
        
        // 沉积动画
        this.addAnimation('sedimentation', (start) => {
            this.sedimentationActive = start;
        });
    }

    updateEquipment(deltaTime) {
        // 更新刮砂器旋转
        if (this.scrapingActive && this.scraper) {
            this.scraperRotation += this.scraperSpeed * deltaTime * 60;
            this.scraper.rotation.y = this.scraperRotation;
        }
        
        // 更新水流粒子
        if (this.waterFlowActive && this.waterParticles) {
            this.updateWaterFlow(deltaTime);
        }
        
        // 更新沉积粒子
        if (this.sedimentationActive && this.sedimentParticles) {
            this.updateSedimentation(deltaTime);
        }
        
        // 更新水位
        if (this.waterSurface) {
            const waterLevel = this.parameters.get('waterLevel');
            this.waterSurface.position.y = waterLevel;
        }
    }

    updateWaterFlow(deltaTime) {
        const positions = this.waterParticles.geometry.attributes.position.array;
        const velocities = this.waterParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            // 更新位置
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            // 检查边界并重置粒子
            const distance = Math.sqrt(positions[i] * positions[i] + positions[i + 2] * positions[i + 2]);
            
            if (distance > 2.5 || positions[i + 1] < 0.2) {
                // 重置到进水口
                const radius = Math.random() * 0.5;
                const angle = Math.random() * Math.PI * 2;
                
                positions[i] = -2 + Math.cos(angle) * radius;
                positions[i + 1] = 1 + Math.random() * 1;
                positions[i + 2] = Math.sin(angle) * radius;
                
                // 重新设置速度
                const flowAngle = Math.random() * Math.PI * 2;
                velocities[i] = Math.cos(flowAngle) * 0.3;
                velocities[i + 1] = -0.05;
                velocities[i + 2] = Math.sin(flowAngle) * 0.3;
            }
        }
        
        this.waterParticles.geometry.attributes.position.needsUpdate = true;
    }

    updateSedimentation(deltaTime) {
        const positions = this.sedimentParticles.geometry.attributes.position.array;
        const velocities = this.sedimentParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            // 沉降运动
            positions[i + 1] += velocities[i + 1] * deltaTime;
            
            // 到达底部时停止
            if (positions[i + 1] <= 0.1) {
                positions[i + 1] = 0.1;
                velocities[i + 1] = 0;
            }
            
            // 随机添加新的沉积物
            if (Math.random() < 0.001) {
                const radius = Math.random() * 2.5;
                const angle = Math.random() * Math.PI * 2;
                
                positions[i] = Math.cos(angle) * radius;
                positions[i + 1] = 1.5 + Math.random() * 0.5;
                positions[i + 2] = Math.sin(angle) * radius;
                
                velocities[i + 1] = -0.1 - Math.random() * 0.1;
            }
        }
        
        this.sedimentParticles.geometry.attributes.position.needsUpdate = true;
    }

    onParameterChanged(name, newValue, oldValue) {
        switch (name) {
            case 'waterLevel':
                if (this.waterSurface) {
                    this.waterSurface.position.y = newValue;
                }
                break;
            case 'scraperSpeed':
                this.scraperSpeed = newValue;
                break;
        }
    }

    // 排砂操作
    dischargeSand() {
        if (this.sandRemovalSystem) {
            // 模拟排砂动画
            console.log('开始排砂操作');
            
            // 可以添加排砂粒子效果
            this.createSandDischargeEffect();
        }
    }

    createSandDischargeEffect() {
        // 创建排砂粒子效果
        const particleCount = 100;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 从排砂口开始
            positions[i3] = 0;
            positions[i3 + 1] = -0.2;
            positions[i3 + 2] = 3;
            
            // 向下流动
            velocities[i3] = (Math.random() - 0.5) * 0.2;
            velocities[i3 + 1] = -1 - Math.random() * 0.5;
            velocities[i3 + 2] = 0.5 + Math.random() * 0.3;
            
            // 砂粒颜色
            const color = new THREE.Color(0x8b4513);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.05,
            transparent: true,
            opacity: 0.8,
            vertexColors: true
        });
        
        const dischargeParticles = new THREE.Points(geometry, material);
        this.group.add(dischargeParticles);
        
        // 5秒后移除效果
        setTimeout(() => {
            this.group.remove(dischargeParticles);
            geometry.dispose();
            material.dispose();
        }, 5000);
    }

    getStatus() {
        const baseStatus = super.getStatus();
        return {
            ...baseStatus,
            scraperRotation: this.scraperRotation,
            sedimentLevel: this.parameters.get('sedimentLevel'),
            removalEfficiency: this.getRemovalEfficiency(),
            sandRemoved: Math.floor(Math.random() * 50), // 模拟数据
            waterQuality: {
                turbidity: 120 + Math.random() * 30,
                suspendedSolids: 80 + Math.random() * 20,
                sandContent: Math.floor(Math.random() * 10)
            }
        };
    }

    getRemovalEfficiency() {
        const baseEfficiency = 0.90; // 基础效率90%
        const retentionFactor = Math.min(1.2, this.parameters.get('retentionTime') / 120);
        return baseEfficiency * retentionFactor * this.efficiency;
    }
}
