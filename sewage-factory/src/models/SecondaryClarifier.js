/**
 * 二沉池 - 二级沉淀池，用于分离活性污泥和处理后的清水
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class SecondaryClarifier extends Equipment {
    constructor(position) {
        super(position);
        
        // 二沉池特有参数
        this.parameters.set('retentionTime', 2.0); // 停留时间 (小时)
        this.parameters.set('surfaceLoading', 20); // 表面负荷 (m³/m²·d)
        this.parameters.set('waterLevel', 3.0); // 水位 (米)
        this.parameters.set('sludgeLevel', 0.8); // 污泥层厚度 (米)
        this.parameters.set('scraperSpeed', 0.3); // 刮泥器转速 (rpm)
        this.parameters.set('sludgeRecycleRatio', 0.5); // 污泥回流比
        
        // 刮泥系统
        this.scraper = null;
        this.scraperRotation = 0;
        
        // 污泥回流系统
        this.recycleSystem = null;
        
        // 粒子效果
        this.activeSludgeParticles = null;
        this.waterFlow = null;
        this.recycleFlow = null;
    }

    async create() {
        this.createBasicMaterials();
        this.createBasicGeometries();
        
        // 创建主体结构
        this.createMainStructure();
        
        // 创建刮泥系统
        this.createScrapingSystem();
        
        // 创建进出水系统
        this.createInletOutletSystem();
        
        // 创建污泥回流系统
        this.createSludgeRecycleSystem();
        
        // 创建活性污泥效果
        this.createActiveSludgeEffects();
        
        // 添加指示灯和标识
        this.createIndicatorLight(0x00ff00, new THREE.Vector3(0, 5, 0));
        this.createNamePlate('二沉池', new THREE.Vector3(0, 5.5, 0));
        
        // 设置动画
        this.setupAnimations();
        
        this.updateBoundingBox();
    }

    createMainStructure() {
        // 圆形池体（比初沉池稍大）
        const poolGeometry = new THREE.CylinderGeometry(6, 6, 4);
        const poolMaterial = this.materials.get('concrete');
        
        const pool = this.addMesh(
            poolGeometry,
            poolMaterial,
            new THREE.Vector3(0, 2, 0)
        );
        pool.userData.isMainStructure = true;
        
        // 内壁
        const innerGeometry = new THREE.CylinderGeometry(5.8, 5.8, 3.8);
        const innerMaterial = new THREE.MeshPhongMaterial({
            color: 0x95a5a6,
            side: THREE.BackSide
        });
        
        const innerWall = this.addMesh(
            innerGeometry,
            innerMaterial,
            new THREE.Vector3(0, 2, 0)
        );
        
        // 水面
        const waterGeometry = new THREE.CircleGeometry(5.7, 32);
        const waterMaterial = this.materials.get('water');
        
        const water = this.addMesh(
            waterGeometry,
            waterMaterial,
            new THREE.Vector3(0, this.parameters.get('waterLevel'), 0),
            new THREE.Euler(-Math.PI / 2, 0, 0)
        );
        water.userData.isWater = true;
        
        this.waterSurface = water;
        
        // 底部锥形结构（更深的锥形用于污泥收集）
        const bottomConeGeometry = new THREE.ConeGeometry(2.5, 1.5, 16);
        const bottomCone = this.addMesh(
            bottomConeGeometry,
            poolMaterial,
            new THREE.Vector3(0, 0.75, 0)
        );
        
        // 中心进水井
        const centerWellGeometry = new THREE.CylinderGeometry(1, 1, 2.5);
        const centerWell = this.addMesh(
            centerWellGeometry,
            poolMaterial,
            new THREE.Vector3(0, 1.75, 0)
        );
        centerWell.userData.isCenterWell = true;
        
        // 挡板（防止短流）
        const baffleGeometry = new THREE.CylinderGeometry(1.5, 1.5, 0.6);
        const baffleMaterial = new THREE.MeshPhongMaterial({
            color: 0x7f8c8d
        });
        
        const baffle = this.addMesh(
            baffleGeometry,
            baffleMaterial,
            new THREE.Vector3(0, 2.7, 0)
        );
        baffle.userData.isBaffle = true;
    }

    createScrapingSystem() {
        const scraperGroup = new THREE.Group();
        
        // 中心轴
        const shaftGeometry = new THREE.CylinderGeometry(0.2, 0.2, 5);
        const shaftMaterial = this.materials.get('metal');
        
        const shaft = new THREE.Mesh(shaftGeometry, shaftMaterial);
        shaft.position.set(0, 2.5, 0);
        shaft.castShadow = true;
        scraperGroup.add(shaft);
        
        // 刮泥臂（双臂设计）
        const armGeometry = new THREE.BoxGeometry(5.5, 0.2, 0.4);
        const armMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50
        });
        
        const arm1 = new THREE.Mesh(armGeometry, armMaterial);
        arm1.position.set(0, 0.4, 0);
        arm1.castShadow = true;
        scraperGroup.add(arm1);
        
        const arm2 = new THREE.Mesh(armGeometry, armMaterial);
        arm2.position.set(0, 0.4, 0);
        arm2.rotation.y = Math.PI / 2;
        arm2.castShadow = true;
        scraperGroup.add(arm2);
        
        // 刮板
        const bladeGeometry = new THREE.BoxGeometry(0.1, 0.5, 5.5);
        const bladeMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        for (let i = 0; i < 4; i++) {
            const angle = (i * Math.PI) / 2;
            const x = Math.cos(angle) * 5.2;
            const z = Math.sin(angle) * 5.2;
            
            const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
            blade.position.set(x, 0.25, z);
            blade.rotation.y = angle;
            blade.castShadow = true;
            scraperGroup.add(blade);
        }
        
        // 浮渣刮板
        const scumBladeGeometry = new THREE.BoxGeometry(0.1, 0.3, 5.5);
        for (let i = 0; i < 4; i++) {
            const angle = (i * Math.PI) / 2;
            const x = Math.cos(angle) * 5.2;
            const z = Math.sin(angle) * 5.2;
            
            const scumBlade = new THREE.Mesh(scumBladeGeometry, bladeMaterial);
            scumBlade.position.set(x, 2.8, z);
            scumBlade.rotation.y = angle;
            scraperGroup.add(scumBlade);
        }
        
        // 驱动装置
        const driveGeometry = new THREE.CylinderGeometry(0.6, 0.6, 1.2);
        const driveMaterial = new THREE.MeshPhongMaterial({
            color: 0x3498db
        });
        
        const drive = new THREE.Mesh(driveGeometry, driveMaterial);
        drive.position.set(0, 5.6, 0);
        drive.castShadow = true;
        scraperGroup.add(drive);
        
        // 支撑桥
        const bridgeGeometry = new THREE.BoxGeometry(12, 0.4, 0.6);
        const bridgeMaterial = this.materials.get('metal');
        
        const bridge = new THREE.Mesh(bridgeGeometry, bridgeMaterial);
        bridge.position.set(0, 5, 0);
        bridge.castShadow = true;
        scraperGroup.add(bridge);
        
        this.group.add(scraperGroup);
        this.scraper = scraperGroup;
    }

    createInletOutletSystem() {
        // 进水管（来自生化池）
        const inletPipeGeometry = new THREE.CylinderGeometry(0.5, 0.5, 2.5);
        const pipeMaterial = this.materials.get('metal');
        
        const inletPipe = this.addMesh(
            inletPipeGeometry,
            pipeMaterial,
            new THREE.Vector3(0, 0.75, 0)
        );
        inletPipe.userData.isInletPipe = true;
        
        // 出水堰（V型堰）
        const weirCount = 20;
        const weirGeometry = new THREE.BoxGeometry(0.5, 0.4, 0.1);
        const weirMaterial = new THREE.MeshPhongMaterial({
            color: 0x7f8c8d
        });
        
        for (let i = 0; i < weirCount; i++) {
            const angle = (i * 2 * Math.PI) / weirCount;
            const x = Math.cos(angle) * 5.5;
            const z = Math.sin(angle) * 5.5;
            
            const weir = new THREE.Mesh(weirGeometry, weirMaterial);
            weir.position.set(x, 2.9, z);
            weir.rotation.y = angle;
            weir.castShadow = true;
            this.group.add(weir);
        }
        
        // 出水槽
        const troughGeometry = new THREE.TorusGeometry(5.8, 0.25, 8, 32);
        const trough = this.addMesh(
            troughGeometry,
            pipeMaterial,
            new THREE.Vector3(0, 2.8, 0)
        );
        trough.userData.isOutletTrough = true;
        
        // 出水管
        const outletPipe = this.addMesh(
            inletPipeGeometry,
            pipeMaterial,
            new THREE.Vector3(6.5, 2, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        outletPipe.userData.isOutletPipe = true;
    }

    createSludgeRecycleSystem() {
        const recycleGroup = new THREE.Group();
        
        // 污泥回流管
        const recyclePipeGeometry = new THREE.CylinderGeometry(0.4, 0.4, 3);
        const pipeMaterial = this.materials.get('metal');
        
        const recyclePipe = new THREE.Mesh(recyclePipeGeometry, pipeMaterial);
        recyclePipe.position.set(-3, -0.5, 4);
        recyclePipe.rotation.z = Math.PI / 6;
        recyclePipe.castShadow = true;
        recycleGroup.add(recyclePipe);
        
        // 回流泵
        const pumpGeometry = new THREE.CylinderGeometry(0.8, 0.8, 1.2);
        const pumpMaterial = new THREE.MeshPhongMaterial({
            color: 0x2980b9
        });
        
        const recyclePump = new THREE.Mesh(pumpGeometry, pumpMaterial);
        recyclePump.position.set(-4, -1.5, 5);
        recyclePump.castShadow = true;
        recycleGroup.add(recyclePump);
        
        // 流量计
        const flowMeterGeometry = new THREE.CylinderGeometry(0.5, 0.5, 0.8);
        const flowMeterMaterial = new THREE.MeshPhongMaterial({
            color: 0xf39c12
        });
        
        const flowMeter = new THREE.Mesh(flowMeterGeometry, flowMeterMaterial);
        flowMeter.position.set(-3.5, -1, 4.5);
        flowMeter.rotation.z = Math.PI / 6;
        recycleGroup.add(flowMeter);
        
        // 剩余污泥排放管
        const wastePipeGeometry = new THREE.CylinderGeometry(0.3, 0.3, 2);
        const wastePipe = new THREE.Mesh(wastePipeGeometry, pipeMaterial);
        wastePipe.position.set(0, -1, 6);
        wastePipe.rotation.x = Math.PI / 4;
        wastePipe.castShadow = true;
        recycleGroup.add(wastePipe);
        
        // 剩余污泥阀
        const wasteValveGeometry = new THREE.SphereGeometry(0.4);
        const wasteValveMaterial = new THREE.MeshPhongMaterial({
            color: 0xe74c3c
        });
        
        const wasteValve = new THREE.Mesh(wasteValveGeometry, wasteValveMaterial);
        wasteValve.position.set(0, -0.5, 6.5);
        recycleGroup.add(wasteValve);
        
        this.group.add(recycleGroup);
        this.recycleSystem = recycleGroup;
    }

    createActiveSludgeEffects() {
        // 活性污泥粒子
        const sludgeCount = 800;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(sludgeCount * 3);
        const velocities = new Float32Array(sludgeCount * 3);
        const colors = new Float32Array(sludgeCount * 3);
        const sizes = new Float32Array(sludgeCount);
        
        for (let i = 0; i < sludgeCount; i++) {
            const i3 = i * 3;
            
            // 随机分布在池中
            const radius = Math.random() * 5.5;
            const angle = Math.random() * Math.PI * 2;
            
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 0.5 + Math.random() * 2.5;
            positions[i3 + 2] = Math.sin(angle) * radius;
            
            // 沉降速度（活性污泥沉降性能好）
            velocities[i3] = (Math.random() - 0.5) * 0.01;
            velocities[i3 + 1] = -0.15 - Math.random() * 0.1;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.01;
            
            // 颜色（深棕色活性污泥）
            const color = new THREE.Color();
            color.setHSL(0.06, 0.8, 0.2 + Math.random() * 0.2);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
            
            // 大小
            sizes[i] = 0.03 + Math.random() * 0.04;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        
        const material = new THREE.PointsMaterial({
            size: 0.05,
            transparent: true,
            opacity: 0.8,
            vertexColors: true
        });
        
        this.activeSludgeParticles = new THREE.Points(geometry, material);
        this.group.add(this.activeSludgeParticles);
        
        // 水流粒子
        this.createWaterFlowParticles();
        
        // 回流污泥粒子
        this.createRecycleFlowParticles();
    }

    createWaterFlowParticles() {
        const flowCount = 400;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(flowCount * 3);
        const velocities = new Float32Array(flowCount * 3);
        const colors = new Float32Array(flowCount * 3);
        
        for (let i = 0; i < flowCount; i++) {
            const i3 = i * 3;
            
            // 从中心进水井开始
            const radius = Math.random() * 0.9;
            const angle = Math.random() * Math.PI * 2;
            
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 1.5 + Math.random() * 1;
            positions[i3 + 2] = Math.sin(angle) * radius;
            
            // 径向流动
            const flowAngle = Math.random() * Math.PI * 2;
            velocities[i3] = Math.cos(flowAngle) * 0.15;
            velocities[i3 + 1] = -0.01; // 轻微下沉
            velocities[i3 + 2] = Math.sin(flowAngle) * 0.15;
            
            // 较清澈的水（经过生化处理）
            const color = new THREE.Color(0x4a90e2);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.04,
            transparent: true,
            opacity: 0.6,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.waterFlow = new THREE.Points(geometry, material);
        this.group.add(this.waterFlow);
    }

    createRecycleFlowParticles() {
        const recycleCount = 200;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(recycleCount * 3);
        const velocities = new Float32Array(recycleCount * 3);
        const colors = new Float32Array(recycleCount * 3);
        
        for (let i = 0; i < recycleCount; i++) {
            const i3 = i * 3;
            
            // 从回流管开始
            positions[i3] = -3 + Math.random() * 0.5;
            positions[i3 + 1] = -0.5 + Math.random() * 0.5;
            positions[i3 + 2] = 4 + Math.random() * 0.5;
            
            // 向生化池方向流动
            velocities[i3] = -0.8 + Math.random() * 0.2;
            velocities[i3 + 1] = 0.1 + Math.random() * 0.1;
            velocities[i3 + 2] = -0.3 + Math.random() * 0.2;
            
            // 含污泥的回流水
            const color = new THREE.Color(0x8b4513);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.06,
            transparent: true,
            opacity: 0.7,
            vertexColors: true
        });
        
        this.recycleFlow = new THREE.Points(geometry, material);
        this.group.add(this.recycleFlow);
    }

    setupAnimations() {
        // 刮泥器旋转动画
        this.addAnimation('scraping', (start) => {
            this.scrapingActive = start;
        });
        
        // 沉淀动画
        this.addAnimation('sedimentation', (start) => {
            this.sedimentationActive = start;
        });
        
        // 水流动画
        this.addAnimation('waterFlow', (start) => {
            this.waterFlowActive = start;
        });
        
        // 回流动画
        this.addAnimation('recycling', (start) => {
            this.recyclingActive = start;
        });
    }

    updateEquipment(deltaTime) {
        // 更新刮泥器旋转
        if (this.scrapingActive && this.scraper) {
            const rpm = this.parameters.get('scraperSpeed');
            const rotationSpeed = (rpm / 60) * 2 * Math.PI;
            this.scraperRotation += rotationSpeed * deltaTime;
            this.scraper.rotation.y = this.scraperRotation;
        }
        
        // 更新活性污泥沉淀
        if (this.sedimentationActive && this.activeSludgeParticles) {
            this.updateActiveSludgeSedimentation(deltaTime);
        }
        
        // 更新水流粒子
        if (this.waterFlowActive && this.waterFlow) {
            this.updateWaterFlow(deltaTime);
        }
        
        // 更新回流粒子
        if (this.recyclingActive && this.recycleFlow) {
            this.updateRecycleFlow(deltaTime);
        }
        
        // 更新水位
        if (this.waterSurface) {
            const waterLevel = this.parameters.get('waterLevel');
            this.waterSurface.position.y = waterLevel;
        }
    }

    updateActiveSludgeSedimentation(deltaTime) {
        const positions = this.activeSludgeParticles.geometry.attributes.position.array;
        const velocities = this.activeSludgeParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            const distance = Math.sqrt(positions[i] * positions[i] + positions[i + 2] * positions[i + 2]);
            
            if (distance > 5.5 || positions[i + 1] <= 0.2) {
                // 重置粒子
                const radius = Math.random() * 0.9;
                const angle = Math.random() * Math.PI * 2;
                
                positions[i] = Math.cos(angle) * radius;
                positions[i + 1] = 2.5 + Math.random() * 0.5;
                positions[i + 2] = Math.sin(angle) * radius;
                
                velocities[i] = (Math.random() - 0.5) * 0.01;
                velocities[i + 1] = -0.15 - Math.random() * 0.1;
                velocities[i + 2] = (Math.random() - 0.5) * 0.01;
            }
        }
        
        this.activeSludgeParticles.geometry.attributes.position.needsUpdate = true;
    }

    updateWaterFlow(deltaTime) {
        const positions = this.waterFlow.geometry.attributes.position.array;
        const velocities = this.waterFlow.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            const distance = Math.sqrt(positions[i] * positions[i] + positions[i + 2] * positions[i + 2]);
            
            if (distance > 5.5) {
                const radius = Math.random() * 0.9;
                const angle = Math.random() * Math.PI * 2;
                
                positions[i] = Math.cos(angle) * radius;
                positions[i + 1] = 1.5 + Math.random() * 1;
                positions[i + 2] = Math.sin(angle) * radius;
                
                const flowAngle = Math.random() * Math.PI * 2;
                velocities[i] = Math.cos(flowAngle) * 0.15;
                velocities[i + 1] = -0.01;
                velocities[i + 2] = Math.sin(flowAngle) * 0.15;
            }
        }
        
        this.waterFlow.geometry.attributes.position.needsUpdate = true;
    }

    updateRecycleFlow(deltaTime) {
        const positions = this.recycleFlow.geometry.attributes.position.array;
        const velocities = this.recycleFlow.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            if (positions[i] < -8) {
                positions[i] = -3 + Math.random() * 0.5;
                positions[i + 1] = -0.5 + Math.random() * 0.5;
                positions[i + 2] = 4 + Math.random() * 0.5;
                
                velocities[i] = -0.8 + Math.random() * 0.2;
                velocities[i + 1] = 0.1 + Math.random() * 0.1;
                velocities[i + 2] = -0.3 + Math.random() * 0.2;
            }
        }
        
        this.recycleFlow.geometry.attributes.position.needsUpdate = true;
    }

    getStatus() {
        const baseStatus = super.getStatus();
        return {
            ...baseStatus,
            retentionTime: this.parameters.get('retentionTime'),
            surfaceLoading: this.parameters.get('surfaceLoading'),
            waterLevel: this.parameters.get('waterLevel'),
            sludgeLevel: this.parameters.get('sludgeLevel'),
            scraperRotation: this.scraperRotation,
            sludgeRecycleRatio: this.parameters.get('sludgeRecycleRatio'),
            clarificationEfficiency: this.getClarificationEfficiency(),
            ssRemoval: 95 + Math.random() * 4, // 模拟SS去除率
            sludgeConcentration: 8000 + Math.random() * 2000, // 模拟污泥浓度
            svi: 80 + Math.random() * 40, // 模拟污泥体积指数
            recycleFlowRate: 50 + Math.random() * 20 // 模拟回流量
        };
    }

    getClarificationEfficiency() {
        const baseEfficiency = 0.95; // 基础效率95%
        const retentionFactor = Math.min(1.1, this.parameters.get('retentionTime') / 2.0);
        const loadingFactor = Math.max(0.9, Math.min(1.1, 20 / this.parameters.get('surfaceLoading')));
        
        return baseEfficiency * retentionFactor * loadingFactor * this.efficiency;
    }
}
