/**
 * 通用水池/罐体设备类
 * 用于调节池、选择池、碳源罐、加氯罐、清水池、污泥池等
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class Tank extends Equipment {
    constructor(position, config = {}) {
        super(position);
        
        this.config = {
            width: config.width || 8,
            height: config.height || 4,
            depth: config.depth || 6,
            color: config.color || 0x4a90e2,
            opacity: config.opacity || 0.7,
            name: config.name || '水池',
            id: config.id || 'TANK',
            sensors: config.sensors || [],
            equipment: config.equipment || [],
            valves: config.valves || []
        };
        
        this.waterLevel = 0.8; // 水位百分比
        this.isRunning = false;
        this.waterMesh = null;
        this.sensorMeshes = [];
        this.equipmentMeshes = [];
    }

    async create() {
        this.group = new THREE.Group();
        
        // 创建主体结构
        await this.createMainStructure();
        
        // 创建水体
        await this.createWater();
        
        // 创建传感器
        await this.createSensors();
        
        // 创建附属设备
        await this.createEquipment();
        
        // 设置位置
        this.group.position.copy(this.position);
        
        return this.group;
    }

    async createMainStructure() {
        // 外壳
        const shellGeometry = new THREE.BoxGeometry(
            this.config.width, 
            this.config.height, 
            this.config.depth
        );
        const shellMaterial = new THREE.MeshLambertMaterial({
            color: 0x888888,
            transparent: true,
            opacity: 0.3
        });
        const shellMesh = new THREE.Mesh(shellGeometry, shellMaterial);
        shellMesh.position.y = this.config.height / 2;
        shellMesh.userData = { name: this.config.name, type: 'tank', id: this.config.id };
        this.group.add(shellMesh);

        // 底部
        const bottomGeometry = new THREE.BoxGeometry(
            this.config.width, 
            0.2, 
            this.config.depth
        );
        const bottomMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const bottomMesh = new THREE.Mesh(bottomGeometry, bottomMaterial);
        bottomMesh.position.y = 0.1;
        bottomMesh.userData = { name: this.config.name, type: 'tank', id: this.config.id };
        this.group.add(bottomMesh);

        // 边框
        const edges = new THREE.EdgesGeometry(shellGeometry);
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0x333333 });
        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.position.y = this.config.height / 2;
        this.group.add(wireframe);
    }

    async createWater() {
        const waterGeometry = new THREE.BoxGeometry(
            this.config.width - 0.2,
            this.config.height * this.waterLevel,
            this.config.depth - 0.2
        );
        
        const waterMaterial = new THREE.MeshLambertMaterial({
            color: this.config.color,
            transparent: true,
            opacity: this.config.opacity
        });
        
        this.waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
        this.waterMesh.position.y = (this.config.height * this.waterLevel) / 2 + 0.2;
        this.waterMesh.userData = { name: this.config.name, type: 'tank', id: this.config.id };
        this.group.add(this.waterMesh);
    }

    async createSensors() {
        // 创建液位计等传感器
        this.config.sensors.forEach((sensorId, index) => {
            const sensorGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.5);
            const sensorMaterial = new THREE.MeshLambertMaterial({ color: 0xffff00 });
            const sensorMesh = new THREE.Mesh(sensorGeometry, sensorMaterial);
            
            // 传感器位置
            sensorMesh.position.set(
                this.config.width / 2 + 0.5,
                this.config.height / 2,
                -this.config.depth / 2 + (index + 1) * (this.config.depth / (this.config.sensors.length + 1))
            );
            
            this.group.add(sensorMesh);
            this.sensorMeshes.push(sensorMesh);
            
            // 添加传感器标签
            this.createSensorLabel(sensorId, sensorMesh.position);
        });
    }

    async createEquipment() {
        // 创建附属设备（如泵、阀门等）
        this.config.equipment.forEach((equipmentId, index) => {
            const equipmentGeometry = new THREE.BoxGeometry(1, 1, 1);
            const equipmentMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
            const equipmentMesh = new THREE.Mesh(equipmentGeometry, equipmentMaterial);
            
            // 设备位置
            equipmentMesh.position.set(
                -this.config.width / 2 - 1.5,
                0.5,
                -this.config.depth / 2 + (index + 1) * (this.config.depth / (this.config.equipment.length + 1))
            );
            
            this.group.add(equipmentMesh);
            this.equipmentMeshes.push(equipmentMesh);
            
            // 添加设备标签
            this.createEquipmentLabel(equipmentId, equipmentMesh.position);
        });
    }

    createSensorLabel(text, position) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 32;
        
        context.font = 'Bold 16px Arial';
        context.fillStyle = 'yellow';
        context.textAlign = 'center';
        context.fillText(text, canvas.width / 2, canvas.height / 2 + 6);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        sprite.scale.set(2, 0.5, 1);
        sprite.position.copy(position);
        sprite.position.y += 1;
        
        this.group.add(sprite);
    }

    createEquipmentLabel(text, position) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 32;
        
        context.font = 'Bold 16px Arial';
        context.fillStyle = 'lime';
        context.textAlign = 'center';
        context.fillText(text, canvas.width / 2, canvas.height / 2 + 6);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        sprite.scale.set(2, 0.5, 1);
        sprite.position.copy(position);
        sprite.position.y += 1;
        
        this.group.add(sprite);
    }

    // 设备控制方法
    start() {
        this.isRunning = true;
        console.log(`${this.config.name} 开始运行`);
    }

    stop() {
        this.isRunning = false;
        console.log(`${this.config.name} 停止运行`);
    }

    setWaterLevel(level) {
        this.waterLevel = Math.max(0, Math.min(1, level));
        if (this.waterMesh) {
            this.waterMesh.scale.y = this.waterLevel;
            this.waterMesh.position.y = (this.config.height * this.waterLevel) / 2 + 0.2;
        }
    }

    getStatus() {
        return {
            name: this.config.name,
            id: this.config.id,
            isRunning: this.isRunning,
            waterLevel: this.waterLevel,
            sensors: this.config.sensors,
            equipment: this.config.equipment
        };
    }

    update(deltaTime) {
        if (this.isRunning && this.waterMesh) {
            // 水面波动效果
            this.waterMesh.position.y += Math.sin(Date.now() * 0.001) * 0.01;
        }
    }

    getBoundingBox() {
        const box = new THREE.Box3();
        box.setFromObject(this.group);
        return box;
    }

    dispose() {
        // 清理资源
        this.group.traverse((child) => {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (child.material.map) child.material.map.dispose();
                child.material.dispose();
            }
        });
    }
}
