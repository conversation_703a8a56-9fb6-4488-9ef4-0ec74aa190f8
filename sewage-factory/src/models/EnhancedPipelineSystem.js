/**
 * 增强管道系统 - 带有半透明管道和水流动画效果
 * 基于真实污水处理工艺流程设计
 */

import * as THREE from 'three';

export class EnhancedPipelineSystem {
    constructor() {
        this.group = new THREE.Group();
        this.pipes = [];
        this.waterFlows = [];
        this.valves = [];
        this.flowParticles = null;
        this.waterBalls = []; // 新增：水流小球数组
        this.isVisible = true;
        this.particlesEnabled = true;
        
        // 管道参数
        this.pipeRadius = 0.2;
        this.mainPipeRadius = 0.3;
        this.pipeColor = 0x34495e;
        this.waterColor = 0x4A90E2;
        this.valveColor = 0xe74c3c;
        this.flowSpeed = 1.0;
        
        // 动画参数
        this.time = 0;
        this.flowDirection = 1; // 1 为正向，-1 为反向
        
        // 水流小球参数
        this.ballCount = 30; // 每段管道的小球数量
        this.ballSize = 0.15; // 小球大小
        this.ballSpeed = 2.0; // 小球流动速度
        
        // 工艺流程路径定义
        this.processFlow = [
            // 主流程：原水设备 → 调节池 → 细格栅 → 选择池 → 反应池 → 二沉池 → 二级产水池 → 砂滤罐 → 加氯罐 → 三级产水池 → 总出水设备
            { from: 'rawWaterTank', to: 'adjustmentTank', type: 'main' },
            { from: 'adjustmentTank', to: 'fineGrating', type: 'main' },
            { from: 'fineGrating', to: 'selectionTank', type: 'main' },
            { from: 'selectionTank', to: 'reactionTanks', type: 'main' },
            { from: 'reactionTanks', to: 'secondaryClarifier', type: 'main' },
            { from: 'secondaryClarifier', to: 'clearWaterTank', type: 'main' },
            { from: 'clearWaterTank', to: 'sandFilter', type: 'main' },
            { from: 'sandFilter', to: 'chlorinationTank', type: 'main' },
            { from: 'chlorinationTank', to: 'tertiaryClarifier', type: 'main' },
            { from: 'tertiaryClarifier', to: 'finalOutletTank', type: 'main' },

            // 附属关系
            { from: 'carbonSourceTank', to: 'selectionTank', type: 'branch' },
            { from: 'secondaryClarifier', to: 'sludgeTank', type: 'sludge' }
        ];
    }

    async create(equipmentManager) {
        this.equipmentManager = equipmentManager;
        
        // 创建主流程管道
        this.createMainFlowPipes();
        
        // 创建支管
        this.createBranchPipes();
        
        // 创建污泥管道
        this.createSludgePipes();
        
        // 创建阀门
        this.createValves();
        
        // 创建流向指示
        this.createFlowIndicators();
        
        // 创建水流粒子效果
        this.createFlowParticles();
        
        // 创建水流小球效果
        this.createWaterBalls();
        
        console.log('增强管道系统创建完成');
    }

    // 获取设备位置的辅助方法
    getEquipmentPosition(equipment) {
        if (!equipment) return null;

        // 尝试不同的位置获取方式
        if (equipment.position) {
            return equipment.position.clone();
        } else if (equipment.group && equipment.group.position) {
            return equipment.group.position.clone();
        } else if (equipment.mesh && equipment.mesh.position) {
            return equipment.mesh.position.clone();
        }

        console.warn('无法获取设备位置', equipment);
        return null;
    }

    createMainFlowPipes() {
        // 主流程管道路径
        const mainFlowConnections = this.processFlow.filter(conn => conn.type === 'main');
        
        mainFlowConnections.forEach(connection => {
            this.createEnhancedPipeConnection(connection, this.mainPipeRadius, 0x2c3e50);
        });
    }

    createBranchPipes() {
        // 支管连接（如碳源投加）
        const branchConnections = this.processFlow.filter(conn => conn.type === 'branch');
        
        branchConnections.forEach(connection => {
            this.createEnhancedPipeConnection(connection, this.pipeRadius, 0x8B4513);
        });
    }

    createSludgePipes() {
        // 污泥管道
        const sludgeConnections = this.processFlow.filter(conn => conn.type === 'sludge');
        
        sludgeConnections.forEach(connection => {
            this.createEnhancedPipeConnection(connection, this.pipeRadius, 0x8B4513);
        });
    }

    createEnhancedPipeConnection(connection, radius, color) {
        const fromEquipment = this.equipmentManager.getEquipment(connection.from);
        const toEquipment = this.equipmentManager.getEquipment(connection.to);

        if (!fromEquipment || !toEquipment) {
            console.warn(`设备未找到: ${connection.from} -> ${connection.to}`);
            return;
        }

        // 获取设备位置，支持不同的设备结构
        const fromPos = this.getEquipmentPosition(fromEquipment);
        const toPos = this.getEquipmentPosition(toEquipment);

        if (!fromPos || !toPos) {
            console.warn(`无法获取设备位置: ${connection.from} -> ${connection.to}`);
            return;
        }
        
        // 调整连接点高度
        fromPos.y += 1;
        toPos.y += 1;
        
        // 创建管道几何体
        const direction = new THREE.Vector3().subVectors(toPos, fromPos);
        const length = direction.length();
        
        // 直管段
        if (Math.abs(fromPos.y - toPos.y) < 0.5 && Math.abs(fromPos.z - toPos.z) < 0.5) {
            this.createEnhancedStraightPipe(fromPos, toPos, radius, color, connection.type);
        } else {
            // 弯管段
            this.createEnhancedBentPipe(fromPos, toPos, radius, color, connection.type);
        }
    }

    createEnhancedStraightPipe(fromPos, toPos, radius, color, pipeType) {
        const direction = new THREE.Vector3().subVectors(toPos, fromPos);
        const length = direction.length();
        
        // 创建管道组
        const pipeGroup = new THREE.Group();
        
        // 1. 外层半透明管道
        const outerGeometry = new THREE.CylinderGeometry(radius, radius, length, 16);
        const outerMaterial = new THREE.MeshPhongMaterial({
            color: color,
            transparent: true,
            opacity: 0.3,
            side: THREE.DoubleSide,
            depthWrite: false
        });
        const outerPipe = new THREE.Mesh(outerGeometry, outerMaterial);
        
        // 2. 内层水流
        const innerRadius = radius * 0.8;
        const innerGeometry = new THREE.CylinderGeometry(innerRadius, innerRadius, length, 16);
        
        // 创建水流材质（带动画纹理）
        const waterMaterial = this.createWaterMaterial(pipeType);
        const waterFlow = new THREE.Mesh(innerGeometry, waterMaterial);
        
        // 定位和旋转
        const center = new THREE.Vector3().addVectors(fromPos, toPos).multiplyScalar(0.5);
        pipeGroup.position.copy(center);
        
        // 设置旋转
        const axis = new THREE.Vector3(0, 1, 0);
        const quaternion = new THREE.Quaternion().setFromUnitVectors(axis, direction.normalize());
        pipeGroup.quaternion.copy(quaternion);
        
        // 添加到组
        pipeGroup.add(outerPipe);
        pipeGroup.add(waterFlow);
        
        // 设置阴影
        outerPipe.castShadow = true;
        outerPipe.receiveShadow = true;
        waterFlow.castShadow = false;
        waterFlow.receiveShadow = false;
        
        // 存储引用
        pipeGroup.userData = {
            type: pipeType,
            outerPipe: outerPipe,
            waterFlow: waterFlow,
            length: length,
            direction: direction.normalize()
        };
        
        this.group.add(pipeGroup);
        this.pipes.push(pipeGroup);
        this.waterFlows.push(waterFlow);
    }

    createEnhancedBentPipe(fromPos, toPos, radius, color, pipeType) {
        // 创建L型弯管
        const midPoint = new THREE.Vector3(
            toPos.x,
            fromPos.y,
            fromPos.z
        );
        
        // 第一段：水平段
        this.createEnhancedStraightPipe(fromPos, midPoint, radius, color, pipeType);
        
        // 第二段：垂直或倾斜段
        this.createEnhancedStraightPipe(midPoint, toPos, radius, color, pipeType);
        
        // 在转弯处添加弯头
        this.createEnhancedElbow(midPoint, radius, color, pipeType);
    }

    createEnhancedElbow(position, radius, color, pipeType) {
        // 创建半透明弯头
        const elbowGeometry = new THREE.SphereGeometry(radius * 1.2, 16, 16);
        const elbowMaterial = new THREE.MeshPhongMaterial({
            color: color,
            transparent: true,
            opacity: 0.3,
            side: THREE.DoubleSide,
            depthWrite: false
        });

        const elbow = new THREE.Mesh(elbowGeometry, elbowMaterial);
        elbow.position.copy(position);
        elbow.castShadow = true;
        elbow.receiveShadow = true;

        this.group.add(elbow);
    }

    createWaterMaterial(pipeType) {
        // 根据管道类型选择水流颜色
        let waterColor = this.waterColor;
        if (pipeType === 'sludge') {
            waterColor = 0x8B4513; // 污泥颜色
        } else if (pipeType === 'branch') {
            waterColor = 0x32CD32; // 支管颜色（如碳源）
        }

        // 创建增强的水流材质，带有动画纹理
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;
        
        // 创建流动纹理
        const gradient = context.createLinearGradient(0, 0, canvas.width, 0);
        gradient.addColorStop(0, 'rgba(74,144,226,0.3)');
        gradient.addColorStop(0.3, 'rgba(74,144,226,0.8)');
        gradient.addColorStop(0.7, 'rgba(74,144,226,0.8)');
        gradient.addColorStop(1, 'rgba(74,144,226,0.3)');
        
        context.fillStyle = gradient;
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 1);

        // 创建水流材质
        const material = new THREE.MeshPhongMaterial({
            color: waterColor,
            transparent: true,
            opacity: 0.8,
            emissive: new THREE.Color(waterColor).multiplyScalar(0.15),
            shininess: 100,
            map: texture
        });

        // 存储纹理引用以便后续动画
        material.userData = {
            texture: texture,
            originalColor: waterColor,
            animationOffset: Math.random() * Math.PI * 2
        };

        return material;
    }

    createValves() {
        // 在关键位置创建阀门
        const valvePositions = [
            { equipment: 'adjustmentTank', offset: new THREE.Vector3(2, 1, 0) },
            { equipment: 'reactionTanks', offset: new THREE.Vector3(-2, 1, 0) },
            { equipment: 'secondaryClarifier', offset: new THREE.Vector3(2, 1, 0) },
            { equipment: 'sandFilter', offset: new THREE.Vector3(-2, 1, 0) },
            { equipment: 'chlorinationTank', offset: new THREE.Vector3(-2, 1, 0) }
        ];

        valvePositions.forEach(valvePos => {
            const equipment = this.equipmentManager.getEquipment(valvePos.equipment);
            if (equipment) {
                const equipmentPos = this.getEquipmentPosition(equipment);
                if (equipmentPos) {
                    const position = equipmentPos.add(valvePos.offset);
                    this.createValve(position);
                }
            }
        });
    }

    createValve(position) {
        const valveGroup = new THREE.Group();
        
        // 阀体
        const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.6);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: this.valveColor });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        valveGroup.add(body);
        
        // 手轮
        const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.1);
        const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0xff9900 });
        const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
        wheel.position.y = 0.35;
        valveGroup.add(wheel);
        
        valveGroup.position.copy(position);
        this.group.add(valveGroup);
        this.valves.push(valveGroup);
    }

    createFlowIndicators() {
        // 移除绿色箭头指示器，使用更自然的水流粒子效果来表示流向
        console.log('流向指示器已移除，使用水流粒子效果替代');
    }

    createFlowArrow(position) {
        // 不再创建绿色箭头，保持场景简洁
        // 流向将通过水流粒子的移动方向来表示
    }

    createFlowParticles() {
        // 创建水流粒子效果 - 严格按照设备连接路径
        if (!this.equipmentManager) {
            console.warn('设备管理器未初始化，无法创建精确的水流路径');
            return;
        }

        // 获取主流程设备连接路径
        const devicePath = this.getDeviceFlowPath();
        if (devicePath.length < 2) {
            console.warn('设备路径不足，无法创建水流效果');
            return;
        }

        const particleCount = 300; // 减少粒子数量以提高性能
        const geometry = new THREE.BufferGeometry();

        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const pathIndices = new Float32Array(particleCount); // 记录粒子所在的路径段

        // 为每个粒子分配到路径段上
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // 随机选择一个路径段
            const pathIndex = Math.floor(Math.random() * (devicePath.length - 1));
            const startDevice = devicePath[pathIndex];
            const endDevice = devicePath[pathIndex + 1];

            if (startDevice && endDevice) {
                // 在路径段上随机分布粒子
                const t = Math.random();
                const startPos = startDevice.position;
                const endPos = endDevice.position;

                positions[i3] = startPos.x + (endPos.x - startPos.x) * t;
                positions[i3 + 1] = startPos.y + (endPos.y - startPos.y) * t + 1; // 提升到管道高度
                positions[i3 + 2] = startPos.z + (endPos.z - startPos.z) * t;

                // 设置流动方向
                const direction = new THREE.Vector3()
                    .subVectors(endPos, startPos)
                    .normalize();

                velocities[i3] = direction.x * this.flowSpeed * (0.8 + Math.random() * 0.4);
                velocities[i3 + 1] = direction.y * this.flowSpeed * (0.8 + Math.random() * 0.4);
                velocities[i3 + 2] = direction.z * this.flowSpeed * (0.8 + Math.random() * 0.4);

                pathIndices[i] = pathIndex;
            } else {
                // 隐藏无效粒子
                positions[i3] = -1000;
                positions[i3 + 1] = -1000;
                positions[i3 + 2] = -1000;
                velocities[i3] = 0;
                velocities[i3 + 1] = 0;
                velocities[i3 + 2] = 0;
                pathIndices[i] = -1;
            }

            // 设置颜色（蓝色水流）
            colors[i3] = 0.3 + Math.random() * 0.2;     // R
            colors[i3 + 1] = 0.6 + Math.random() * 0.2; // G
            colors[i3 + 2] = 0.9 + Math.random() * 0.1; // B

            // 设置大小
            sizes[i] = 0.03 + Math.random() * 0.02;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        geometry.setAttribute('pathIndex', new THREE.BufferAttribute(pathIndices, 1));

        const material = new THREE.PointsMaterial({
            size: 0.04,
            transparent: true,
            opacity: 0.7,
            vertexColors: true,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true
        });

        this.flowParticles = new THREE.Points(geometry, material);
        this.group.add(this.flowParticles);

        console.log(`创建了沿设备路径的水流粒子，路径包含 ${devicePath.length} 个设备`);
    }

    // 获取设备流动路径
    getDeviceFlowPath() {
        const deviceNames = [
            'rawWaterTank',      // 原水
            'adjustmentTank',    // 调节池
            'fineGrating',       // 细格栅
            'selectionTank',     // 选择池
            'reactionTanks',     // 反应池
            'secondaryClarifier',// 二沉池
            'clearWaterTank',    // 二级产水池
            'sandFilter',        // 砂滤罐
            'chlorinationTank',  // 加氯罐
            'tertiaryClarifier', // 三级产水池
            'finalOutletTank'    // 总出水
        ];

        const devicePath = [];
        deviceNames.forEach(deviceName => {
            const device = this.equipmentManager.getEquipment(deviceName);
            if (device) {
                devicePath.push({
                    name: deviceName,
                    position: device.position || device.group.position,
                    device: device
                });
            }
        });

        return devicePath;
    }

    // 创建水流小球效果（严格按照设备路径）
    createWaterBalls() {
        if (!this.equipmentManager) {
            console.warn('设备管理器未初始化，无法创建精确的水流小球路径');
            return;
        }

        // 获取设备路径
        const devicePath = this.getDeviceFlowPath();
        if (devicePath.length < 2) {
            console.warn('设备路径不足，无法创建水流小球');
            return;
        }

        const ballsPerSegment = 5; // 每段路径的小球数量

        // 为每个路径段创建小球
        for (let pathIndex = 0; pathIndex < devicePath.length - 1; pathIndex++) {
            const startDevice = devicePath[pathIndex];
            const endDevice = devicePath[pathIndex + 1];

            for (let i = 0; i < ballsPerSegment; i++) {
                const ballGeometry = new THREE.SphereGeometry(this.ballSize, 12, 12);
                const ballMaterial = new THREE.MeshPhongMaterial({
                    color: 0x2288ff,
                    transparent: true,
                    opacity: 0.8,
                    shininess: 100,
                    emissive: 0x001144,
                    emissiveIntensity: 0.2
                });

                const ball = new THREE.Mesh(ballGeometry, ballMaterial);
                
                // 在路径段上分布小球
                const t = i / ballsPerSegment;
                const startPos = startDevice.position;
                const endPos = endDevice.position;

                ball.position.x = startPos.x + (endPos.x - startPos.x) * t;
                ball.position.y = startPos.y + (endPos.y - startPos.y) * t + 1; // 提升到管道高度
                ball.position.z = startPos.z + (endPos.z - startPos.z) * t;
                
                // 计算流动方向
                const direction = new THREE.Vector3()
                    .subVectors(endPos, startPos)
                    .normalize();
                
                // 存储小球的运动参数
                ball.userData = {
                    pathIndex: pathIndex,
                    segmentProgress: t,
                    speed: this.ballSpeed * (0.8 + Math.random() * 0.4),
                    startDevice: startDevice,
                    endDevice: endDevice,
                    direction: direction.clone(),
                    phase: Math.random() * Math.PI * 2, // 波动相位
                    crossSectionOffset: {
                        y: (Math.random() - 0.5) * 0.1,
                        z: (Math.random() - 0.5) * 0.1
                    }
                };
                
                this.waterBalls.push(ball);
                this.group.add(ball);
            }
        }
        
        console.log(`创建了 ${this.waterBalls.length} 个沿设备路径的水流小球`);
    }

    // 更新方法
    update(deltaTime) {
        this.time += deltaTime;

        // 更新水流动画
        this.updateWaterFlowAnimation(deltaTime);

        // 更新粒子系统
        this.updateFlowParticles(deltaTime);
        
        // 更新水流小球
        this.updateWaterBalls(deltaTime);
    }

    updateWaterFlowAnimation(deltaTime) {
        // 更新水流材质的动画效果
        this.waterFlows.forEach((waterFlow, index) => {
            if (waterFlow.material) {
                const material = waterFlow.material;
                
                // 纹理滚动动画 - 创建流动效果
                if (material.userData && material.userData.texture) {
                    const texture = material.userData.texture;
                    const animationOffset = material.userData.animationOffset || 0;
                    
                    // 根据流向和速度滚动纹理
                    texture.offset.x += this.flowSpeed * deltaTime * this.flowDirection * 0.5;
                    
                    // 防止纹理偏移值过大
                    if (texture.offset.x > 1) texture.offset.x -= 1;
                    if (texture.offset.x < 0) texture.offset.x += 1;
                }
                
                // 发光效果动画
                if (material.emissive) {
                    const baseIntensity = 0.15;
                    const pulseIntensity = 0.05 * Math.sin(this.time * 4 + index * 0.5);
                    const flowIntensity = 0.03 * Math.sin(this.time * 6 + waterFlow.position.x * 0.1);
                    
                    const totalIntensity = baseIntensity + pulseIntensity + flowIntensity;
                    material.emissive.copy(new THREE.Color(material.userData.originalColor || this.waterColor).multiplyScalar(totalIntensity));
                }

                // 动态透明度变化 - 模拟水流的波动
                const baseOpacity = 0.8;
                const waveOpacity = 0.1 * Math.sin(this.time * 3 + waterFlow.position.x * 0.08 + index * 0.3);
                material.opacity = Math.max(0.4, baseOpacity + waveOpacity);
                
                // 轻微的颜色变化 - 模拟水流中的杂质和气泡
                if (material.userData.originalColor) {
                    const colorVariation = 0.05 * Math.sin(this.time * 2 + index * 0.7);
                    const baseColor = new THREE.Color(material.userData.originalColor);
                    baseColor.multiplyScalar(1 + colorVariation);
                    material.color.copy(baseColor);
                }
            }
        });
    }

    updateFlowParticles(deltaTime) {
        if (!this.flowParticles || !this.particlesEnabled) return;

        const positions = this.flowParticles.geometry.attributes.position.array;
        const velocities = this.flowParticles.geometry.attributes.velocity.array;
        const colors = this.flowParticles.geometry.attributes.color.array;
        const pathIndices = this.flowParticles.geometry.attributes.pathIndex?.array;
        
        // 获取设备路径
        const devicePath = this.getDeviceFlowPath();
        if (devicePath.length < 2) return;

        for (let i = 0; i < positions.length; i += 3) {
            const particleIndex = i / 3;
            
            // 更新位置
            positions[i] += velocities[i] * deltaTime * this.flowDirection;
            positions[i + 1] += velocities[i + 1] * deltaTime * this.flowDirection;
            positions[i + 2] += velocities[i + 2] * deltaTime * this.flowDirection;

            // 检查粒子是否需要重置到下一个路径段或回到起点
            if (pathIndices && pathIndices[particleIndex] >= 0) {
                const currentPathIndex = Math.floor(pathIndices[particleIndex]);
                const startDevice = devicePath[currentPathIndex];
                const endDevice = devicePath[currentPathIndex + 1];

                if (startDevice && endDevice) {
                    const currentPos = new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]);
                    const endPos = endDevice.position;
                    
                    // 检查是否到达终点设备
                    if (currentPos.distanceTo(endPos) < 2) {
                        if (this.flowDirection > 0) {
                            // 正向流动：移动到下一个路径段或回到起点
                            if (currentPathIndex + 2 < devicePath.length) {
                                // 移动到下一个路径段
                                const nextStart = devicePath[currentPathIndex + 1];
                                const nextEnd = devicePath[currentPathIndex + 2];
                                
                                positions[i] = nextStart.position.x;
                                positions[i + 1] = nextStart.position.y + 1;
                                positions[i + 2] = nextStart.position.z;
                                
                                const direction = new THREE.Vector3()
                                    .subVectors(nextEnd.position, nextStart.position)
                                    .normalize();
                                
                                velocities[i] = direction.x * this.flowSpeed * (0.8 + Math.random() * 0.4);
                                velocities[i + 1] = direction.y * this.flowSpeed * (0.8 + Math.random() * 0.4);
                                velocities[i + 2] = direction.z * this.flowSpeed * (0.8 + Math.random() * 0.4);
                                
                                pathIndices[particleIndex] = currentPathIndex + 1;
                            } else {
                                // 回到起点（完成一个循环）
                                this.resetParticleToStart(i, devicePath, pathIndices, positions, velocities);
                            }
                        } else {
                            // 反向流动：移动到上一个路径段或回到终点
                            if (currentPathIndex > 0) {
                                const prevEnd = devicePath[currentPathIndex - 1];
                                const prevStart = devicePath[currentPathIndex];
                                
                                positions[i] = prevStart.position.x;
                                positions[i + 1] = prevStart.position.y + 1;
                                positions[i + 2] = prevStart.position.z;
                                
                                const direction = new THREE.Vector3()
                                    .subVectors(prevEnd.position, prevStart.position)
                                    .normalize();
                                
                                velocities[i] = direction.x * this.flowSpeed * (0.8 + Math.random() * 0.4);
                                velocities[i + 1] = direction.y * this.flowSpeed * (0.8 + Math.random() * 0.4);
                                velocities[i + 2] = direction.z * this.flowSpeed * (0.8 + Math.random() * 0.4);
                                
                                pathIndices[particleIndex] = currentPathIndex - 1;
                            } else {
                                // 回到终点
                                this.resetParticleToEnd(i, devicePath, pathIndices, positions, velocities);
                            }
                        }
                    }
                }
            }

            // 更新颜色闪烁效果
            const colorIndex = Math.floor(i / 3);
            const flickerIntensity = 0.8 + 0.2 * Math.sin(this.time * 3 + colorIndex * 0.1);
            colors[i] = (0.3 + Math.random() * 0.2) * flickerIntensity;
            colors[i + 1] = (0.6 + Math.random() * 0.2) * flickerIntensity;
            colors[i + 2] = (0.9 + Math.random() * 0.1) * flickerIntensity;
        }

        this.flowParticles.geometry.attributes.position.needsUpdate = true;
        this.flowParticles.geometry.attributes.color.needsUpdate = true;
        if (pathIndices) {
            this.flowParticles.geometry.attributes.pathIndex.needsUpdate = true;
        }
    }

    // 重置粒子到起点
    resetParticleToStart(i, devicePath, pathIndices, positions, velocities) {
        const startDevice = devicePath[0];
        const endDevice = devicePath[1];
        
        if (startDevice && endDevice) {
            positions[i] = startDevice.position.x;
            positions[i + 1] = startDevice.position.y + 1;
            positions[i + 2] = startDevice.position.z;
            
            const direction = new THREE.Vector3()
                .subVectors(endDevice.position, startDevice.position)
                .normalize();
            
            velocities[i] = direction.x * this.flowSpeed * (0.8 + Math.random() * 0.4);
            velocities[i + 1] = direction.y * this.flowSpeed * (0.8 + Math.random() * 0.4);
            velocities[i + 2] = direction.z * this.flowSpeed * (0.8 + Math.random() * 0.4);
            
            if (pathIndices) {
                pathIndices[i / 3] = 0;
            }
        }
    }

    // 重置粒子到终点
    resetParticleToEnd(i, devicePath, pathIndices, positions, velocities) {
        const endDevice = devicePath[devicePath.length - 1];
        const startDevice = devicePath[devicePath.length - 2];
        
        if (startDevice && endDevice) {
            positions[i] = endDevice.position.x;
            positions[i + 1] = endDevice.position.y + 1;
            positions[i + 2] = endDevice.position.z;
            
            const direction = new THREE.Vector3()
                .subVectors(startDevice.position, endDevice.position)
                .normalize();
            
            velocities[i] = direction.x * this.flowSpeed * (0.8 + Math.random() * 0.4);
            velocities[i + 1] = direction.y * this.flowSpeed * (0.8 + Math.random() * 0.4);
            velocities[i + 2] = direction.z * this.flowSpeed * (0.8 + Math.random() * 0.4);
            
            if (pathIndices) {
                pathIndices[i / 3] = devicePath.length - 2;
            }
        }
    }

    // 更新水流小球动画（严格按照设备路径）
    updateWaterBalls(deltaTime) {
        const devicePath = this.getDeviceFlowPath();
        if (devicePath.length < 2) return;

        this.waterBalls.forEach((ball, index) => {
            const userData = ball.userData;
            
            // 沿设备路径方向移动
            const moveDistance = userData.speed * deltaTime * this.flowDirection;
            const movement = userData.direction.clone().multiplyScalar(moveDistance);
            ball.position.add(movement);
            
            // 添加轻微的波浪运动效果
            const waveAmplitude = 0.05;
            const waveFrequency = 2;
            ball.position.y += Math.sin(this.time * waveFrequency + userData.phase) * waveAmplitude * deltaTime;
            ball.position.z += Math.cos(this.time * waveFrequency * 1.5 + userData.phase) * waveAmplitude * 0.5 * deltaTime;
            
            // 检查是否到达路径段终点
            const currentPos = ball.position;
            const endPos = userData.endDevice.position;
            const startPos = userData.startDevice.position;
            
            if (this.flowDirection > 0) {
                // 正向流动
                if (currentPos.distanceTo(endPos) < 1) {
                    // 移动到下一个路径段或回到起点
                    const currentPathIndex = userData.pathIndex;
                    if (currentPathIndex + 2 < devicePath.length) {
                        // 移动到下一个路径段
                        const nextStart = devicePath[currentPathIndex + 1];
                        const nextEnd = devicePath[currentPathIndex + 2];
                        
                        ball.position.copy(nextStart.position);
                        ball.position.y += 1;
                        
                        userData.pathIndex = currentPathIndex + 1;
                        userData.startDevice = nextStart;
                        userData.endDevice = nextEnd;
                        userData.direction = new THREE.Vector3()
                            .subVectors(nextEnd.position, nextStart.position)
                            .normalize();
                    } else {
                        // 回到起点（完成一个循环）
                        const firstDevice = devicePath[0];
                        const secondDevice = devicePath[1];
                        
                        ball.position.copy(firstDevice.position);
                        ball.position.y += 1;
                        
                        userData.pathIndex = 0;
                        userData.startDevice = firstDevice;
                        userData.endDevice = secondDevice;
                        userData.direction = new THREE.Vector3()
                            .subVectors(secondDevice.position, firstDevice.position)
                            .normalize();
                    }
                }
            } else {
                // 反向流动
                if (currentPos.distanceTo(startPos) < 1) {
                    // 移动到上一个路径段或回到终点
                    const currentPathIndex = userData.pathIndex;
                    if (currentPathIndex > 0) {
                        // 移动到上一个路径段
                        const prevEnd = devicePath[currentPathIndex - 1];
                        const prevStart = devicePath[currentPathIndex];
                        
                        ball.position.copy(prevStart.position);
                        ball.position.y += 1;
                        
                        userData.pathIndex = currentPathIndex - 1;
                        userData.startDevice = prevStart;
                        userData.endDevice = prevEnd;
                        userData.direction = new THREE.Vector3()
                            .subVectors(prevEnd.position, prevStart.position)
                            .normalize();
                    } else {
                        // 回到终点
                        const lastDevice = devicePath[devicePath.length - 1];
                        const secondLastDevice = devicePath[devicePath.length - 2];
                        
                        ball.position.copy(lastDevice.position);
                        ball.position.y += 1;
                        
                        userData.pathIndex = devicePath.length - 2;
                        userData.startDevice = lastDevice;
                        userData.endDevice = secondLastDevice;
                        userData.direction = new THREE.Vector3()
                            .subVectors(secondLastDevice.position, lastDevice.position)
                            .normalize();
                    }
                }
            }
            
            // 添加发光效果动画
            const material = ball.material;
            const baseEmissiveIntensity = 0.2;
            const pulseIntensity = 0.1 * Math.sin(this.time * 4 + index * 0.5);
            material.emissiveIntensity = baseEmissiveIntensity + pulseIntensity;
            
            // 根据流速调整透明度
            const speedFactor = Math.abs(userData.speed) / this.ballSpeed;
            material.opacity = 0.7 + 0.2 * speedFactor;
        });
    }

    // 控制方法
    setVisible(visible) {
        this.isVisible = visible;
        this.group.visible = visible;
    }

    setFlowSpeed(speed) {
        this.flowSpeed = speed;
        // 同时更新水流小球速度
        this.ballSpeed = speed * 2.0;
        this.waterBalls.forEach(ball => {
            ball.userData.speed = this.ballSpeed * (0.8 + Math.random() * 0.4);
        });
    }

    setFlowDirection(direction) {
        this.flowDirection = direction; // 1 为正向，-1 为反向
        console.log(`水流方向设置为: ${direction > 0 ? '正向' : '反向'}`);
    }

    // 设置粒子效果开关
    setParticlesEnabled(enabled) {
        this.particlesEnabled = enabled;
        if (this.flowParticles) {
            this.flowParticles.visible = enabled;
        }
        // 同时控制水流小球的显示
        this.waterBalls.forEach(ball => {
            ball.visible = enabled;
        });
    }

    // 设置管道透明度
    setPipeOpacity(opacity) {
        this.pipes.forEach(pipeGroup => {
            if (pipeGroup.userData.outerPipe) {
                pipeGroup.userData.outerPipe.material.opacity = opacity;
            }
        });
    }

    // 设置水流透明度
    setWaterOpacity(opacity) {
        this.waterFlows.forEach(waterFlow => {
            waterFlow.material.opacity = opacity;
        });
    }

    // 清理资源
    dispose() {
        // 清理几何体和材质
        this.pipes.forEach(pipeGroup => {
            if (pipeGroup.userData.outerPipe) {
                pipeGroup.userData.outerPipe.geometry.dispose();
                pipeGroup.userData.outerPipe.material.dispose();
            }
            if (pipeGroup.userData.waterFlow) {
                pipeGroup.userData.waterFlow.geometry.dispose();
                pipeGroup.userData.waterFlow.material.dispose();
            }
        });

        if (this.flowParticles) {
            this.flowParticles.geometry.dispose();
            this.flowParticles.material.dispose();
        }

        this.valves.forEach(valve => {
            valve.children.forEach(child => {
                child.geometry.dispose();
                child.material.dispose();
            });
        });

        // 清理水流小球
        this.waterBalls.forEach(ball => {
            if (ball.geometry) ball.geometry.dispose();
            if (ball.material) ball.material.dispose();
        });

        // 清理数组
        this.pipes = [];
        this.waterFlows = [];
        this.valves = [];
        this.flowParticles = null;
        this.waterBalls = [];
    }
}
