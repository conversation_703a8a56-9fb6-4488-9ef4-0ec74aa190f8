/**
 * 设备基类 - 所有污水处理设备的基础类
 */

import * as THREE from 'three';

export class Equipment {
    constructor(position = new THREE.Vector3(0, 0, 0)) {
        this.position = position.clone();
        this.group = new THREE.Group();
        this.group.position.copy(this.position);
        
        // 设备状态
        this.isRunning = false;
        this.isEnabled = true;
        this.efficiency = 1.0;
        this.powerConsumption = 0;
        
        // 设备参数
        this.parameters = new Map();
        
        // 动画相关
        this.animations = new Map();
        this.animationMixers = [];
        
        // 材质和几何体缓存
        this.materials = new Map();
        this.geometries = new Map();
        
        // 边界盒
        this.boundingBox = new THREE.Box3();
        
        // 事件回调
        this.onStart = null;
        this.onStop = null;
        this.onParameterChange = null;
    }

    // 抽象方法，子类必须实现
    async create() {
        throw new Error('create() method must be implemented by subclass');
    }

    // 启动设备
    start() {
        if (this.isRunning || !this.isEnabled) return false;
        
        this.isRunning = true;
        this.startAnimations();
        
        if (this.onStart) {
            this.onStart();
        }
        
        console.log(`${this.constructor.name} started`);
        return true;
    }

    // 停止设备
    stop() {
        if (!this.isRunning) return false;
        
        this.isRunning = false;
        this.stopAnimations();
        
        if (this.onStop) {
            this.onStop();
        }
        
        console.log(`${this.constructor.name} stopped`);
        return true;
    }

    // 启用/禁用设备
    setEnabled(enabled) {
        this.isEnabled = enabled;
        if (!enabled && this.isRunning) {
            this.stop();
        }
    }

    // 设置参数
    setParameter(name, value) {
        const oldValue = this.parameters.get(name);
        this.parameters.set(name, value);
        
        this.onParameterChanged(name, value, oldValue);
        
        if (this.onParameterChange) {
            this.onParameterChange(name, value, oldValue);
        }
    }

    // 获取参数
    getParameter(name) {
        return this.parameters.get(name);
    }

    // 参数变化处理（子类可重写）
    onParameterChanged(name, newValue, oldValue) {
        // 子类实现具体的参数处理逻辑
    }

    // 创建基础材质
    createBasicMaterials() {
        // 金属材质
        this.materials.set('metal', new THREE.MeshPhongMaterial({
            color: 0x888888,
            shininess: 100,
            specular: 0x222222
        }));

        // 混凝土材质
        this.materials.set('concrete', new THREE.MeshLambertMaterial({
            color: 0x95a5a6
        }));

        // 水材质
        this.materials.set('water', new THREE.MeshPhongMaterial({
            color: 0x006994,
            transparent: true,
            opacity: 0.8,
            shininess: 100
        }));

        // 玻璃材质
        this.materials.set('glass', new THREE.MeshPhongMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.3,
            shininess: 100
        }));

        // 塑料材质
        this.materials.set('plastic', new THREE.MeshPhongMaterial({
            color: 0x3498db,
            shininess: 30
        }));
    }

    // 创建基础几何体
    createBasicGeometries() {
        // 常用几何体
        this.geometries.set('box_1x1x1', new THREE.BoxGeometry(1, 1, 1));
        this.geometries.set('cylinder_1x1', new THREE.CylinderGeometry(0.5, 0.5, 1));
        this.geometries.set('sphere_1', new THREE.SphereGeometry(0.5));
        this.geometries.set('plane_1x1', new THREE.PlaneGeometry(1, 1));
    }

    // 添加网格到组
    addMesh(geometry, material, position = new THREE.Vector3(0, 0, 0), rotation = new THREE.Euler(0, 0, 0), scale = new THREE.Vector3(1, 1, 1)) {
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(position);
        mesh.rotation.copy(rotation);
        mesh.scale.copy(scale);

        // 启用阴影
        mesh.castShadow = true;
        mesh.receiveShadow = true;

        // 设置userData用于交互
        mesh.userData = {
            name: this.config?.name || this.name || '设备',
            type: this.config?.type || this.type || 'equipment',
            id: this.config?.id || this.id || 'unknown'
        };

        this.group.add(mesh);
        return mesh;
    }

    // 添加动画
    addAnimation(name, animationFunction) {
        this.animations.set(name, animationFunction);
    }

    // 启动所有动画
    startAnimations() {
        this.animations.forEach((animation, name) => {
            if (typeof animation === 'function') {
                animation.call(this, true); // 启动动画
            }
        });
    }

    // 停止所有动画
    stopAnimations() {
        this.animations.forEach((animation, name) => {
            if (typeof animation === 'function') {
                animation.call(this, false); // 停止动画
            }
        });
    }

    // 更新边界盒
    updateBoundingBox() {
        this.boundingBox.setFromObject(this.group);
    }

    // 获取边界盒
    getBoundingBox() {
        this.updateBoundingBox();
        return this.boundingBox;
    }

    // 获取设备状态
    getStatus() {
        return {
            isRunning: this.isRunning,
            isEnabled: this.isEnabled,
            efficiency: this.efficiency,
            powerConsumption: this.powerConsumption,
            parameters: Object.fromEntries(this.parameters),
            position: this.position.clone(),
            boundingBox: this.getBoundingBox()
        };
    }

    // 设置效率
    setEfficiency(efficiency) {
        this.efficiency = Math.max(0, Math.min(1, efficiency));
    }

    // 设置功耗
    setPowerConsumption(power) {
        this.powerConsumption = Math.max(0, power);
    }

    // 更新方法（每帧调用）
    update(deltaTime) {
        // 更新动画混合器
        this.animationMixers.forEach(mixer => {
            mixer.update(deltaTime);
        });
        
        // 子类可重写此方法添加特定更新逻辑
        this.updateEquipment(deltaTime);
    }

    // 设备特定更新逻辑（子类重写）
    updateEquipment(deltaTime) {
        // 子类实现
    }

    // 创建指示灯
    createIndicatorLight(color = 0x00ff00, position = new THREE.Vector3(0, 2, 0)) {
        const lightGeometry = new THREE.SphereGeometry(0.1);
        const lightMaterial = new THREE.MeshBasicMaterial({
            color: color,
            emissive: color,
            emissiveIntensity: 0.5
        });
        
        const indicator = new THREE.Mesh(lightGeometry, lightMaterial);
        indicator.position.copy(position);
        this.group.add(indicator);
        
        return indicator;
    }

    // 创建标识牌
    createNamePlate(text, position = new THREE.Vector3(0, 1, 0)) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        canvas.width = 256;
        canvas.height = 64;
        
        context.font = 'Bold 20px Arial';
        context.fillStyle = 'white';
        context.strokeStyle = 'black';
        context.lineWidth = 2;
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        
        // 背景
        context.fillStyle = 'rgba(0, 0, 0, 0.8)';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        // 文字
        context.fillStyle = 'white';
        context.strokeText(text, canvas.width / 2, canvas.height / 2);
        context.fillText(text, canvas.width / 2, canvas.height / 2);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        sprite.scale.set(4, 1, 1);
        sprite.position.copy(position);
        
        this.group.add(sprite);
        return sprite;
    }

    // 创建粒子效果
    createParticleEffect(config) {
        const particleCount = config.count || 100;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 位置
            positions[i3] = (Math.random() - 0.5) * config.spread.x;
            positions[i3 + 1] = (Math.random() - 0.5) * config.spread.y;
            positions[i3 + 2] = (Math.random() - 0.5) * config.spread.z;
            
            // 颜色
            const color = new THREE.Color(config.color || 0xffffff);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
            
            // 大小
            sizes[i] = config.size || 1.0;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        
        const material = new THREE.PointsMaterial({
            size: config.size || 1.0,
            transparent: true,
            opacity: config.opacity || 0.8,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        const particles = new THREE.Points(geometry, material);
        particles.position.copy(config.position || new THREE.Vector3(0, 0, 0));
        
        this.group.add(particles);
        return particles;
    }

    // 资源清理
    dispose() {
        // 清理几何体
        this.geometries.forEach(geometry => {
            geometry.dispose();
        });
        
        // 清理材质
        this.materials.forEach(material => {
            material.dispose();
        });
        
        // 清理动画混合器
        this.animationMixers.forEach(mixer => {
            mixer.stopAllAction();
        });
        
        // 清理组中的所有对象
        this.group.traverse((child) => {
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
        });
        
        // 清理集合
        this.geometries.clear();
        this.materials.clear();
        this.animations.clear();
        this.parameters.clear();
        this.animationMixers.length = 0;
    }
}
