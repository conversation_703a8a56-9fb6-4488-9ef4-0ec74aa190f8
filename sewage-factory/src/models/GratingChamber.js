/**
 * 格栅池 - 污水处理第一道工序，用于去除大颗粒杂物
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class GratingChamber extends Equipment {
    constructor(position) {
        super(position);
        
        // 格栅池特有参数
        this.parameters.set('gratingSpacing', 20); // 格栅间距 (mm)
        this.parameters.set('cleaningInterval', 300); // 清理间隔 (秒)
        this.parameters.set('waterLevel', 1.5); // 水位 (米)
        this.parameters.set('flowRate', 100); // 流量 (m³/h)
        
        // 动画相关
        this.gratingBars = [];
        this.cleaningMechanism = null;
        this.isCleaningActive = false;
        this.cleaningTimer = 0;
        
        // 水流粒子
        this.waterParticles = null;
        this.debrisParticles = null;
    }

    async create() {
        this.createBasicMaterials();
        this.createBasicGeometries();
        
        // 创建主体结构
        this.createMainStructure();
        
        // 创建格栅系统
        this.createGratingSystem();
        
        // 创建清理机构
        this.createCleaningMechanism();
        
        // 创建水流效果
        this.createWaterFlow();
        
        // 创建杂物效果
        this.createDebrisEffect();
        
        // 创建控制面板
        this.createControlPanel();
        
        // 添加指示灯
        this.createIndicatorLight(0x00ff00, new THREE.Vector3(0, 3, 2));
        
        // 添加标识牌
        this.createNamePlate('格栅池', new THREE.Vector3(0, 3.5, 0));
        
        // 设置动画
        this.setupAnimations();
        
        this.updateBoundingBox();
    }

    createMainStructure() {
        // 主池体
        const poolGeometry = new THREE.BoxGeometry(4, 2, 6);
        const poolMaterial = this.materials.get('concrete');
        
        const pool = this.addMesh(
            poolGeometry,
            poolMaterial,
            new THREE.Vector3(0, 1, 0)
        );
        
        // 池壁（内部空心）
        const wallThickness = 0.2;
        const innerGeometry = new THREE.BoxGeometry(
            4 - wallThickness * 2,
            2 - wallThickness,
            6 - wallThickness * 2
        );
        
        // 使用CSG创建空心效果（简化版本）
        pool.userData.isMainStructure = true;
        
        // 进水口
        const inletGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.5);
        const inletMaterial = this.materials.get('metal');
        
        const inlet = this.addMesh(
            inletGeometry,
            inletMaterial,
            new THREE.Vector3(-2.5, 1.5, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        inlet.userData.isInlet = true;
        
        // 出水口
        const outlet = this.addMesh(
            inletGeometry,
            inletMaterial,
            new THREE.Vector3(2.5, 1.5, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        outlet.userData.isOutlet = true;
        
        // 水面
        const waterGeometry = new THREE.PlaneGeometry(3.6, 5.6);
        const waterMaterial = this.materials.get('water');
        
        const water = this.addMesh(
            waterGeometry,
            waterMaterial,
            new THREE.Vector3(0, this.parameters.get('waterLevel'), 0),
            new THREE.Euler(-Math.PI / 2, 0, 0)
        );
        water.userData.isWater = true;
        
        this.waterSurface = water;
    }

    createGratingSystem() {
        const gratingGroup = new THREE.Group();
        
        // 格栅条参数
        const barWidth = 0.05;
        const barHeight = 2.5;
        const barDepth = 5.5;
        const spacing = this.parameters.get('gratingSpacing') / 1000; // 转换为米
        const barCount = Math.floor(3.5 / spacing);
        
        const barGeometry = new THREE.BoxGeometry(barWidth, barHeight, barDepth);
        const barMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e,
            shininess: 50
        });
        
        // 创建格栅条
        for (let i = 0; i < barCount; i++) {
            const x = (i - barCount / 2) * spacing;
            const bar = new THREE.Mesh(barGeometry, barMaterial);
            bar.position.set(x, 1.25, 0);
            bar.castShadow = true;
            bar.receiveShadow = true;
            
            gratingGroup.add(bar);
            this.gratingBars.push(bar);
        }
        
        // 支撑框架
        const frameGeometry = new THREE.BoxGeometry(4, 0.2, 0.2);
        const frameMaterial = this.materials.get('metal');
        
        // 上框架
        const topFrame = new THREE.Mesh(frameGeometry, frameMaterial);
        topFrame.position.set(0, 2.4, -2.7);
        topFrame.castShadow = true;
        gratingGroup.add(topFrame);
        
        // 下框架
        const bottomFrame = new THREE.Mesh(frameGeometry, frameMaterial);
        bottomFrame.position.set(0, 0.1, -2.7);
        bottomFrame.castShadow = true;
        gratingGroup.add(bottomFrame);
        
        // 侧框架
        const sideFrameGeometry = new THREE.BoxGeometry(0.2, 2.5, 5.5);
        const leftFrame = new THREE.Mesh(sideFrameGeometry, frameMaterial);
        leftFrame.position.set(-1.9, 1.25, 0);
        leftFrame.castShadow = true;
        gratingGroup.add(leftFrame);
        
        const rightFrame = new THREE.Mesh(sideFrameGeometry, frameMaterial);
        rightFrame.position.set(1.9, 1.25, 0);
        rightFrame.castShadow = true;
        gratingGroup.add(rightFrame);
        
        this.group.add(gratingGroup);
        this.gratingGroup = gratingGroup;
    }

    createCleaningMechanism() {
        const cleaningGroup = new THREE.Group();
        
        // 清理臂主体
        const armGeometry = new THREE.BoxGeometry(0.3, 0.3, 4);
        const armMaterial = new THREE.MeshPhongMaterial({
            color: 0xe74c3c,
            shininess: 30
        });
        
        const cleaningArm = new THREE.Mesh(armGeometry, armMaterial);
        cleaningArm.position.set(0, 2.8, 0);
        cleaningArm.castShadow = true;
        cleaningGroup.add(cleaningArm);
        
        // 清理刷
        const brushCount = 8;
        const brushGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.5);
        const brushMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50
        });
        
        for (let i = 0; i < brushCount; i++) {
            const z = (i - brushCount / 2) * 0.5;
            const brush = new THREE.Mesh(brushGeometry, brushMaterial);
            brush.position.set(0, 2.5, z);
            brush.rotation.x = Math.PI / 2;
            cleaningGroup.add(brush);
        }
        
        // 驱动电机
        const motorGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.8);
        const motorMaterial = new THREE.MeshPhongMaterial({
            color: 0x3498db
        });
        
        const motor = new THREE.Mesh(motorGeometry, motorMaterial);
        motor.position.set(2.5, 3.2, 0);
        motor.castShadow = true;
        cleaningGroup.add(motor);
        
        // 导轨
        const railGeometry = new THREE.BoxGeometry(5, 0.1, 0.2);
        const railMaterial = this.materials.get('metal');
        
        const rail = new THREE.Mesh(railGeometry, railMaterial);
        rail.position.set(0, 3.5, 0);
        cleaningGroup.add(rail);
        
        this.group.add(cleaningGroup);
        this.cleaningMechanism = cleaningGroup;
    }

    createWaterFlow() {
        // 创建水流粒子效果
        const particleCount = 200;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // 初始位置（进水口附近）
            positions[i3] = -2 + Math.random() * 0.5;
            positions[i3 + 1] = 0.5 + Math.random() * 1;
            positions[i3 + 2] = (Math.random() - 0.5) * 2;
            
            // 速度
            velocities[i3] = 0.5 + Math.random() * 0.5; // X方向流动
            velocities[i3 + 1] = (Math.random() - 0.5) * 0.1; // 轻微上下波动
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.1; // 轻微左右波动
            
            // 颜色（蓝色水流）
            const color = new THREE.Color(0x006994);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.05,
            transparent: true,
            opacity: 0.6,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.waterParticles = new THREE.Points(geometry, material);
        this.group.add(this.waterParticles);
    }

    createDebrisEffect() {
        // 创建杂物粒子（被格栅拦截的杂物）
        const debrisCount = 50;
        const geometry = new THREE.BufferGeometry();
        
        const positions = new Float32Array(debrisCount * 3);
        const colors = new Float32Array(debrisCount * 3);
        const sizes = new Float32Array(debrisCount);
        
        for (let i = 0; i < debrisCount; i++) {
            const i3 = i * 3;
            
            // 位置（格栅前方）
            positions[i3] = -0.5 + Math.random() * 1;
            positions[i3 + 1] = 0.5 + Math.random() * 1;
            positions[i3 + 2] = (Math.random() - 0.5) * 2;
            
            // 颜色（棕色杂物）
            const color = new THREE.Color(0x8b4513);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
            
            // 大小
            sizes[i] = 0.1 + Math.random() * 0.1;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        
        const material = new THREE.PointsMaterial({
            size: 0.1,
            transparent: true,
            opacity: 0.8,
            vertexColors: true
        });
        
        this.debrisParticles = new THREE.Points(geometry, material);
        this.group.add(this.debrisParticles);
    }

    createControlPanel() {
        // 控制柜
        const panelGeometry = new THREE.BoxGeometry(1, 1.5, 0.3);
        const panelMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        const panel = this.addMesh(
            panelGeometry,
            panelMaterial,
            new THREE.Vector3(3, 1.75, -3)
        );
        
        // 控制按钮
        const buttonGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.02);
        const buttonMaterial = new THREE.MeshPhongMaterial({
            color: 0xe74c3c,
            emissive: 0x220000
        });
        
        const button = new THREE.Mesh(buttonGeometry, buttonMaterial);
        button.position.set(3, 2.2, -2.85);
        button.rotation.x = Math.PI / 2;
        this.group.add(button);
        
        this.controlPanel = panel;
        this.controlButton = button;
    }

    setupAnimations() {
        // 清理动画
        this.addAnimation('cleaning', (start) => {
            if (start && !this.isCleaningActive) {
                this.startCleaningCycle();
            } else if (!start) {
                this.stopCleaningCycle();
            }
        });
        
        // 水流动画
        this.addAnimation('waterFlow', (start) => {
            this.waterFlowActive = start;
        });
    }

    startCleaningCycle() {
        this.isCleaningActive = true;
        this.cleaningTimer = 0;
        
        // 清理臂移动动画
        const cleaningArm = this.cleaningMechanism.children[0];
        const startX = cleaningArm.position.x;
        const endX = startX - 4;
        
        const animateCleaningArm = () => {
            if (!this.isCleaningActive) return;
            
            this.cleaningTimer += 0.016; // 假设60fps
            const progress = (this.cleaningTimer % 10) / 10; // 10秒一个周期
            
            if (progress < 0.5) {
                // 向左移动
                cleaningArm.position.x = startX + (endX - startX) * (progress * 2);
            } else {
                // 向右移动
                cleaningArm.position.x = endX + (startX - endX) * ((progress - 0.5) * 2);
            }
            
            requestAnimationFrame(animateCleaningArm);
        };
        
        animateCleaningArm();
    }

    stopCleaningCycle() {
        this.isCleaningActive = false;
    }

    updateEquipment(deltaTime) {
        // 更新水流粒子
        if (this.waterFlowActive && this.waterParticles) {
            this.updateWaterFlow(deltaTime);
        }
        
        // 更新水位
        if (this.waterSurface) {
            const waterLevel = this.parameters.get('waterLevel');
            this.waterSurface.position.y = waterLevel;
        }
        
        // 自动清理检查
        if (this.isRunning && !this.isCleaningActive) {
            const interval = this.parameters.get('cleaningInterval');
            if (this.cleaningTimer >= interval) {
                this.startCleaningCycle();
            }
        }
    }

    updateWaterFlow(deltaTime) {
        const positions = this.waterParticles.geometry.attributes.position.array;
        const velocities = this.waterParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            // 更新位置
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;
            
            // 重置超出范围的粒子
            if (positions[i] > 2.5) {
                positions[i] = -2.5;
                positions[i + 1] = 0.5 + Math.random() * 1;
                positions[i + 2] = (Math.random() - 0.5) * 2;
            }
        }
        
        this.waterParticles.geometry.attributes.position.needsUpdate = true;
    }

    onParameterChanged(name, newValue, oldValue) {
        switch (name) {
            case 'waterLevel':
                if (this.waterSurface) {
                    this.waterSurface.position.y = newValue;
                }
                break;
            case 'gratingSpacing':
                // 重新创建格栅系统
                this.recreateGratingSystem();
                break;
            case 'cleaningInterval':
                this.cleaningTimer = 0; // 重置清理计时器
                break;
        }
    }

    recreateGratingSystem() {
        // 移除旧的格栅系统
        if (this.gratingGroup) {
            this.group.remove(this.gratingGroup);
        }
        
        // 创建新的格栅系统
        this.gratingBars = [];
        this.createGratingSystem();
    }

    // 获取处理效率
    getProcessingEfficiency() {
        const baseEfficiency = 0.85; // 基础效率85%
        const spacingFactor = Math.max(0.5, Math.min(1.2, 20 / this.parameters.get('gratingSpacing')));
        return baseEfficiency * spacingFactor * this.efficiency;
    }

    getStatus() {
        const baseStatus = super.getStatus();
        return {
            ...baseStatus,
            isCleaningActive: this.isCleaningActive,
            processingEfficiency: this.getProcessingEfficiency(),
            debrisRemoved: Math.floor(Math.random() * 100), // 模拟数据
            waterQuality: {
                turbidity: 150 + Math.random() * 50,
                largeParticles: Math.floor(Math.random() * 20)
            }
        };
    }
}
