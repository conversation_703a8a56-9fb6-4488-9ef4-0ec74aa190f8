/**
 * 管道系统 - 连接所有处理设备的管道网络
 */

import * as THREE from 'three';

export class PipelineSystem {
    constructor(equipmentPositions) {
        this.equipmentPositions = equipmentPositions;
        this.group = new THREE.Group();
        this.pipes = [];
        this.valves = [];
        this.flowParticles = null;
        this.isVisible = true;
        
        // 管道参数
        this.pipeRadius = 0.3;
        this.pipeColor = 0x34495e;
        this.valveColor = 0xe74c3c;
        this.flowSpeed = 1.0;
    }

    async create() {
        // 创建主管道
        this.createMainPipeline();
        
        // 创建支管
        this.createBranchPipes();
        
        // 创建阀门
        this.createValves();
        
        // 创建管道支撑
        this.createSupports();
        
        // 创建流向指示
        this.createFlowIndicators();
        
        // 创建水流粒子效果
        this.createFlowParticles();
    }

    createMainPipeline() {
        // 主管道路径点
        const mainPath = [
            new THREE.Vector3(-20, 1, 0),  // 格栅池
            new THREE.Vector3(-10, 1, 0),  // 沉砂池
            new THREE.Vector3(0, 1, 0),    // 提升泵站
            new THREE.Vector3(10, 1, 0),   // 初沉池
            new THREE.Vector3(25, 1, 0),   // 生化池
            new THREE.Vector3(40, 1, 0),   // 二沉池
            new THREE.Vector3(55, 1, 0)    // 深度处理
        ];

        // 创建管道段
        for (let i = 0; i < mainPath.length - 1; i++) {
            const start = mainPath[i];
            const end = mainPath[i + 1];
            
            const pipe = this.createPipeSegment(start, end, this.pipeRadius);
            this.pipes.push(pipe);
            this.group.add(pipe);
        }

        // 创建弯头连接
        for (let i = 1; i < mainPath.length - 1; i++) {
            const elbow = this.createElbow(mainPath[i], this.pipeRadius);
            this.group.add(elbow);
        }
    }

    createBranchPipes() {
        // 污泥回流管道（二沉池到生化池）
        const recycleStart = new THREE.Vector3(40, 0.5, 3);
        const recycleEnd = new THREE.Vector3(25, 0.5, 3);
        const recyclePipe = this.createPipeSegment(recycleStart, recycleEnd, 0.2);
        recyclePipe.userData.isPipeType = 'sludgeRecycle';
        this.pipes.push(recyclePipe);
        this.group.add(recyclePipe);

        // 剩余污泥排放管道
        const wasteStart = new THREE.Vector3(40, 0.2, 5);
        const wasteEnd = new THREE.Vector3(45, -1, 8);
        const wastePipe = this.createPipeSegment(wasteStart, wasteEnd, 0.15);
        wastePipe.userData.isPipeType = 'wasteSludge';
        this.pipes.push(wastePipe);
        this.group.add(wastePipe);

        // 曝气管道（生化池）
        const aerationStart = new THREE.Vector3(30, 0.5, 0);
        const aerationEnd = new THREE.Vector3(25, 0.5, 4);
        const aerationPipe = this.createPipeSegment(aerationStart, aerationEnd, 0.25);
        aerationPipe.userData.isPipeType = 'aeration';
        this.pipes.push(aerationPipe);
        this.group.add(aerationPipe);

        // 加药管道
        const chemicalStart = new THREE.Vector3(55, 2, -2);
        const chemicalEnd = new THREE.Vector3(55, 1, 2);
        const chemicalPipe = this.createPipeSegment(chemicalStart, chemicalEnd, 0.1);
        chemicalPipe.userData.isPipeType = 'chemical';
        this.pipes.push(chemicalPipe);
        this.group.add(chemicalPipe);
    }

    createPipeSegment(start, end, radius) {
        const direction = new THREE.Vector3().subVectors(end, start);
        const length = direction.length();
        const center = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.5);

        // 创建管道几何体
        const geometry = new THREE.CylinderGeometry(radius, radius, length);
        const material = new THREE.MeshPhongMaterial({
            color: this.pipeColor,
            shininess: 50
        });

        const pipe = new THREE.Mesh(geometry, material);
        pipe.position.copy(center);
        
        // 设置管道方向
        const axis = new THREE.Vector3(0, 1, 0);
        pipe.quaternion.setFromUnitVectors(axis, direction.normalize());
        
        pipe.castShadow = true;
        pipe.receiveShadow = true;
        
        return pipe;
    }

    createElbow(position, radius) {
        // 创建弯头连接
        const elbowGeometry = new THREE.TorusGeometry(radius * 1.5, radius, 8, 16, Math.PI / 2);
        const elbowMaterial = new THREE.MeshPhongMaterial({
            color: this.pipeColor,
            shininess: 50
        });

        const elbow = new THREE.Mesh(elbowGeometry, elbowMaterial);
        elbow.position.copy(position);
        elbow.castShadow = true;
        elbow.receiveShadow = true;

        return elbow;
    }

    createValves() {
        // 在关键位置创建阀门
        const valvePositions = [
            { pos: new THREE.Vector3(-15, 1, 0), type: 'gate' },
            { pos: new THREE.Vector3(-5, 1, 0), type: 'gate' },
            { pos: new THREE.Vector3(5, 1, 0), type: 'gate' },
            { pos: new THREE.Vector3(17.5, 1, 0), type: 'gate' },
            { pos: new THREE.Vector3(32.5, 1, 0), type: 'gate' },
            { pos: new THREE.Vector3(47.5, 1, 0), type: 'gate' },
            { pos: new THREE.Vector3(32.5, 0.5, 3), type: 'control' }, // 回流阀
            { pos: new THREE.Vector3(42.5, -0.5, 6.5), type: 'control' } // 排泥阀
        ];

        valvePositions.forEach(valveData => {
            const valve = this.createValve(valveData.pos, valveData.type);
            this.valves.push(valve);
            this.group.add(valve);
        });
    }

    createValve(position, type) {
        const valveGroup = new THREE.Group();

        if (type === 'gate') {
            // 闸阀
            const bodyGeometry = new THREE.BoxGeometry(0.8, 0.6, 0.6);
            const bodyMaterial = new THREE.MeshPhongMaterial({
                color: this.valveColor
            });

            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.castShadow = true;
            valveGroup.add(body);

            // 手轮
            const wheelGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.1);
            const wheelMaterial = new THREE.MeshPhongMaterial({
                color: 0x2c3e50
            });

            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.position.y = 0.5;
            valveGroup.add(wheel);

            // 阀杆
            const stemGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.4);
            const stemMaterial = new THREE.MeshPhongMaterial({
                color: 0x7f8c8d
            });

            const stem = new THREE.Mesh(stemGeometry, stemMaterial);
            stem.position.y = 0.2;
            valveGroup.add(stem);

        } else if (type === 'control') {
            // 调节阀
            const bodyGeometry = new THREE.SphereGeometry(0.4);
            const bodyMaterial = new THREE.MeshPhongMaterial({
                color: this.valveColor
            });

            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.castShadow = true;
            valveGroup.add(body);

            // 执行器
            const actuatorGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.6);
            const actuatorMaterial = new THREE.MeshPhongMaterial({
                color: 0x3498db
            });

            const actuator = new THREE.Mesh(actuatorGeometry, actuatorMaterial);
            actuator.position.y = 0.5;
            valveGroup.add(actuator);
        }

        valveGroup.position.copy(position);
        valveGroup.userData.valveType = type;
        valveGroup.userData.isOpen = true;

        return valveGroup;
    }

    createSupports() {
        // 管道支撑架
        const supportPositions = [
            new THREE.Vector3(-15, 0, 0),
            new THREE.Vector3(-5, 0, 0),
            new THREE.Vector3(5, 0, 0),
            new THREE.Vector3(17.5, 0, 0),
            new THREE.Vector3(32.5, 0, 0),
            new THREE.Vector3(47.5, 0, 0)
        ];

        supportPositions.forEach(pos => {
            const support = this.createSupport(pos);
            this.group.add(support);
        });
    }

    createSupport(position) {
        const supportGroup = new THREE.Group();

        // 支撑柱
        const columnGeometry = new THREE.CylinderGeometry(0.1, 0.1, 1);
        const columnMaterial = new THREE.MeshPhongMaterial({
            color: 0x7f8c8d
        });

        const column = new THREE.Mesh(columnGeometry, columnMaterial);
        column.position.y = 0.5;
        column.castShadow = true;
        supportGroup.add(column);

        // 支撑托架
        const bracketGeometry = new THREE.BoxGeometry(0.8, 0.2, 0.8);
        const bracketMaterial = new THREE.MeshPhongMaterial({
            color: 0x95a5a6
        });

        const bracket = new THREE.Mesh(bracketGeometry, bracketMaterial);
        bracket.position.y = 1;
        supportGroup.add(bracket);

        // 基础
        const baseGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.2);
        const base = new THREE.Mesh(baseGeometry, columnMaterial);
        base.position.y = 0.1;
        supportGroup.add(base);

        supportGroup.position.copy(position);
        return supportGroup;
    }

    createFlowIndicators() {
        // 流向箭头指示器
        const arrowPositions = [
            new THREE.Vector3(-15, 1.5, 0),
            new THREE.Vector3(-5, 1.5, 0),
            new THREE.Vector3(5, 1.5, 0),
            new THREE.Vector3(17.5, 1.5, 0),
            new THREE.Vector3(32.5, 1.5, 0),
            new THREE.Vector3(47.5, 1.5, 0)
        ];

        arrowPositions.forEach(pos => {
            const arrow = this.createFlowArrow(pos);
            this.group.add(arrow);
        });
    }

    createFlowArrow(position) {
        // 箭头几何体
        const arrowGeometry = new THREE.ConeGeometry(0.2, 0.6, 8);
        const arrowMaterial = new THREE.MeshPhongMaterial({
            color: 0x27ae60,
            emissive: 0x001100
        });

        const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
        arrow.position.copy(position);
        arrow.rotation.z = -Math.PI / 2; // 指向右方
        
        return arrow;
    }

    createFlowParticles() {
        // 水流粒子效果
        const particleCount = 500;
        const geometry = new THREE.BufferGeometry();

        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // 沿主管道分布
            const progress = Math.random();
            const x = -20 + progress * 75; // 从-20到55
            
            positions[i3] = x;
            positions[i3 + 1] = 1 + (Math.random() - 0.5) * 0.4;
            positions[i3 + 2] = (Math.random() - 0.5) * 0.4;

            // 流动速度
            velocities[i3] = this.flowSpeed + Math.random() * 0.5;
            velocities[i3 + 1] = (Math.random() - 0.5) * 0.1;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.1;

            // 颜色（蓝色水流）
            const color = new THREE.Color(0x006994);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const material = new THREE.PointsMaterial({
            size: 0.05,
            transparent: true,
            opacity: 0.6,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });

        this.flowParticles = new THREE.Points(geometry, material);
        this.group.add(this.flowParticles);
    }

    // 控制方法
    setVisible(visible) {
        this.isVisible = visible;
        this.group.visible = visible;
    }

    setFlowSpeed(speed) {
        this.flowSpeed = speed;
    }

    openValve(valveIndex) {
        if (this.valves[valveIndex]) {
            this.valves[valveIndex].userData.isOpen = true;
            // 可以添加阀门开启动画
        }
    }

    closeValve(valveIndex) {
        if (this.valves[valveIndex]) {
            this.valves[valveIndex].userData.isOpen = false;
            // 可以添加阀门关闭动画
        }
    }

    // 更新方法
    update(deltaTime) {
        if (this.flowParticles) {
            this.updateFlowParticles(deltaTime);
        }
        
        this.updateFlowArrows(deltaTime);
    }

    updateFlowParticles(deltaTime) {
        const positions = this.flowParticles.geometry.attributes.position.array;
        const velocities = this.flowParticles.geometry.attributes.velocity.array;

        for (let i = 0; i < positions.length; i += 3) {
            // 更新位置
            positions[i] += velocities[i] * deltaTime;
            positions[i + 1] += velocities[i + 1] * deltaTime;
            positions[i + 2] += velocities[i + 2] * deltaTime;

            // 重置超出范围的粒子
            if (positions[i] > 60) {
                positions[i] = -25;
                positions[i + 1] = 1 + (Math.random() - 0.5) * 0.4;
                positions[i + 2] = (Math.random() - 0.5) * 0.4;

                velocities[i] = this.flowSpeed + Math.random() * 0.5;
                velocities[i + 1] = (Math.random() - 0.5) * 0.1;
                velocities[i + 2] = (Math.random() - 0.5) * 0.1;
            }
        }

        this.flowParticles.geometry.attributes.position.needsUpdate = true;
    }

    updateFlowArrows(deltaTime) {
        // 箭头闪烁效果
        const time = Date.now() * 0.001;
        
        this.group.children.forEach(child => {
            if (child.geometry && child.geometry.type === 'ConeGeometry') {
                const intensity = 0.5 + 0.5 * Math.sin(time * 3);
                child.material.emissiveIntensity = intensity * 0.2;
            }
        });
    }

    // 获取管道信息
    getPipelineInfo() {
        return {
            totalPipes: this.pipes.length,
            totalValves: this.valves.length,
            flowSpeed: this.flowSpeed,
            isVisible: this.isVisible,
            openValves: this.valves.filter(valve => valve.userData.isOpen).length,
            closedValves: this.valves.filter(valve => !valve.userData.isOpen).length
        };
    }

    // 资源清理
    dispose() {
        this.group.traverse((child) => {
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
        });

        this.pipes.length = 0;
        this.valves.length = 0;
    }
}
