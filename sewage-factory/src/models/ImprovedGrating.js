/**
 * 改进的格栅设备类
 * 支持粗格栅和细格栅，包含自动/手动模式
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class ImprovedGrating extends Equipment {
    constructor(position, config = {}) {
        super(position);
        
        this.config = {
            width: config.width || 6,
            height: config.height || 4,
            depth: config.depth || 2,
            gratingType: config.gratingType || 'coarse', // 'coarse' or 'fine'
            mode: config.mode || 'auto', // 'auto' or 'manual'
            name: config.name || '格栅',
            id: config.id || 'GRATING'
        };
        
        this.isRunning = false;
        this.isAutoMode = this.config.mode === 'auto';
        this.gratingBars = [];
        this.debris = [];
        this.cleaningMechanism = null;
    }

    async create() {
        this.group = new THREE.Group();
        
        // 创建主体结构
        await this.createMainStructure();
        
        // 创建格栅条
        await this.createGratingBars();
        
        // 创建清渣机构
        await this.createCleaningMechanism();
        
        // 创建控制面板
        await this.createControlPanel();
        
        // 创建进出水口
        await this.createInletOutlet();
        
        // 设置位置
        this.group.position.copy(this.position);
        
        return this.group;
    }

    async createMainStructure() {
        // 主体框架
        const frameGeometry = new THREE.BoxGeometry(
            this.config.width,
            this.config.height,
            this.config.depth
        );
        const frameMaterial = new THREE.MeshLambertMaterial({
            color: 0x666666,
            transparent: true,
            opacity: 0.3
        });
        const frameMesh = new THREE.Mesh(frameGeometry, frameMaterial);
        frameMesh.position.y = this.config.height / 2;
        frameMesh.userData = { name: this.config.name, type: 'grating', id: this.config.id };
        this.group.add(frameMesh);

        // 边框
        const edges = new THREE.EdgesGeometry(frameGeometry);
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0x333333 });
        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.position.y = this.config.height / 2;
        this.group.add(wireframe);

        // 底座
        const baseGeometry = new THREE.BoxGeometry(
            this.config.width + 1,
            0.3,
            this.config.depth + 1
        );
        const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const baseMesh = new THREE.Mesh(baseGeometry, baseMaterial);
        baseMesh.position.y = 0.15;
        this.group.add(baseMesh);
    }

    async createGratingBars() {
        // 根据格栅类型确定间距
        const spacing = this.config.gratingType === 'coarse' ? 0.8 : 0.3;
        const barCount = Math.floor(this.config.width / spacing);
        
        for (let i = 0; i < barCount; i++) {
            const barGeometry = new THREE.BoxGeometry(0.1, this.config.height - 0.5, 0.05);
            const barMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
            const barMesh = new THREE.Mesh(barGeometry, barMaterial);
            
            barMesh.position.set(
                -this.config.width / 2 + (i + 1) * spacing,
                this.config.height / 2,
                0
            );
            
            this.group.add(barMesh);
            this.gratingBars.push(barMesh);
        }
    }

    async createCleaningMechanism() {
        // 清渣机构
        const mechanismGeometry = new THREE.BoxGeometry(0.5, 0.3, this.config.depth + 0.5);
        const mechanismMaterial = new THREE.MeshLambertMaterial({ color: 0xff6600 });
        this.cleaningMechanism = new THREE.Mesh(mechanismGeometry, mechanismMaterial);
        
        this.cleaningMechanism.position.set(
            0,
            this.config.height + 0.5,
            0
        );
        
        this.group.add(this.cleaningMechanism);

        // 清渣齿
        for (let i = 0; i < 5; i++) {
            const toothGeometry = new THREE.BoxGeometry(0.05, 0.8, 0.05);
            const toothMaterial = new THREE.MeshLambertMaterial({ color: 0xffaa00 });
            const toothMesh = new THREE.Mesh(toothGeometry, toothMaterial);
            
            toothMesh.position.set(
                -0.2 + i * 0.1,
                -0.5,
                0
            );
            
            this.cleaningMechanism.add(toothMesh);
        }
    }

    async createControlPanel() {
        // 控制面板
        const panelGeometry = new THREE.BoxGeometry(1, 1.5, 0.2);
        const panelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const panelMesh = new THREE.Mesh(panelGeometry, panelMaterial);
        
        panelMesh.position.set(
            this.config.width / 2 + 1,
            1.5,
            0
        );
        
        this.group.add(panelMesh);

        // 指示灯
        const lightGeometry = new THREE.SphereGeometry(0.1);
        
        // 运行指示灯
        const runLightMaterial = new THREE.MeshLambertMaterial({ 
            color: this.isRunning ? 0x00ff00 : 0x666666 
        });
        const runLight = new THREE.Mesh(lightGeometry, runLightMaterial);
        runLight.position.set(0, 0.3, 0.15);
        panelMesh.add(runLight);

        // 模式指示灯
        const modeLightMaterial = new THREE.MeshLambertMaterial({ 
            color: this.isAutoMode ? 0x0000ff : 0xffff00 
        });
        const modeLight = new THREE.Mesh(lightGeometry, modeLightMaterial);
        modeLight.position.set(0, -0.3, 0.15);
        panelMesh.add(modeLight);

        // 添加标签
        this.createControlLabel(panelMesh.position);
    }

    async createInletOutlet() {
        // 进水口
        const inletGeometry = new THREE.CylinderGeometry(0.5, 0.5, 1);
        const pipeMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const inletPipe = new THREE.Mesh(inletGeometry, pipeMaterial);
        inletPipe.position.set(-this.config.width / 2 - 1, this.config.height / 2, 0);
        inletPipe.rotation.z = Math.PI / 2;
        this.group.add(inletPipe);

        // 出水口
        const outletPipe = new THREE.Mesh(inletGeometry, pipeMaterial);
        outletPipe.position.set(this.config.width / 2 + 1, this.config.height / 2, 0);
        outletPipe.rotation.z = Math.PI / 2;
        this.group.add(outletPipe);
    }

    createControlLabel(position) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 64;
        
        context.font = 'Bold 12px Arial';
        context.fillStyle = 'white';
        context.textAlign = 'center';
        
        context.fillText(this.config.name, canvas.width / 2, 20);
        context.fillText(`模式: ${this.isAutoMode ? '自动' : '手动'}`, canvas.width / 2, 40);
        context.fillText(`状态: ${this.isRunning ? '运行' : '停止'}`, canvas.width / 2, 55);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        sprite.scale.set(3, 1.5, 1);
        sprite.position.copy(position);
        sprite.position.y += 2;
        
        this.group.add(sprite);
    }

    // 设备控制方法
    start() {
        this.isRunning = true;
        console.log(`${this.config.name} 开始运行`);
    }

    stop() {
        this.isRunning = false;
        console.log(`${this.config.name} 停止运行`);
    }

    setMode(mode) {
        this.isAutoMode = mode === 'auto';
        console.log(`${this.config.name} 切换到${this.isAutoMode ? '自动' : '手动'}模式`);
    }

    clean() {
        console.log(`${this.config.name} 执行清渣操作`);
        // 清渣动画
        if (this.cleaningMechanism) {
            this.cleaningMechanism.position.y = this.config.height / 2;
            setTimeout(() => {
                this.cleaningMechanism.position.y = this.config.height + 0.5;
            }, 2000);
        }
    }

    getStatus() {
        return {
            name: this.config.name,
            id: this.config.id,
            type: this.config.gratingType,
            isRunning: this.isRunning,
            mode: this.isAutoMode ? 'auto' : 'manual',
            debrisLevel: this.debris.length
        };
    }

    update(deltaTime) {
        if (this.isRunning) {
            // 清渣机构运动
            if (this.cleaningMechanism && this.isAutoMode) {
                this.cleaningMechanism.rotation.x += deltaTime * 0.5;
            }
            
            // 模拟杂物积累
            if (Math.random() < 0.01) {
                this.addDebris();
            }
        }
    }

    addDebris() {
        if (this.debris.length < 10) {
            const debrisGeometry = new THREE.SphereGeometry(0.1);
            const debrisMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const debrisMesh = new THREE.Mesh(debrisGeometry, debrisMaterial);
            
            debrisMesh.position.set(
                (Math.random() - 0.5) * this.config.width,
                Math.random() * this.config.height,
                (Math.random() - 0.5) * this.config.depth
            );
            
            this.group.add(debrisMesh);
            this.debris.push(debrisMesh);
        }
    }

    getBoundingBox() {
        const box = new THREE.Box3();
        box.setFromObject(this.group);
        return box;
    }

    dispose() {
        this.group.traverse((child) => {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
                if (child.material.map) child.material.map.dispose();
                child.material.dispose();
            }
        });
    }
}
