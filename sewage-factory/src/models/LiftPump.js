/**
 * 提升泵站 - 用于提升污水到后续处理单元
 */

import * as THREE from 'three';
import { Equipment } from './Equipment.js';

export class LiftPump extends Equipment {
    constructor(position) {
        super(position);
        
        // 泵站特有参数
        this.parameters.set('pumpSpeed', 1450); // 转速 (rpm)
        this.parameters.set('flowRate', 120); // 流量 (m³/h)
        this.parameters.set('headPressure', 15); // 扬程 (m)
        this.parameters.set('motorPower', 15); // 电机功率 (kW)
        this.parameters.set('efficiency', 0.85); // 泵效率
        
        // 动画相关
        this.impeller = null;
        this.impellerRotation = 0;
        this.motorVibration = 0;
        this.isVibrating = false;
        
        // 粒子效果
        this.waterFlow = null;
        this.bubbles = null;
    }

    async create() {
        this.createBasicMaterials();
        this.createBasicGeometries();
        
        // 创建泵站主体
        this.createPumpHousing();
        
        // 创建叶轮系统
        this.createImpellerSystem();
        
        // 创建电机
        this.createMotor();
        
        // 创建管道连接
        this.createPiping();
        
        // 创建控制系统
        this.createControlSystem();
        
        // 创建水流效果
        this.createWaterFlowEffect();
        
        // 添加指示灯和标识
        this.createIndicatorLight(0x00ff00, new THREE.Vector3(0, 4, 0));
        this.createNamePlate('提升泵站', new THREE.Vector3(0, 4.5, 0));
        
        // 设置动画
        this.setupAnimations();
        
        this.updateBoundingBox();
    }

    createPumpHousing() {
        // 泵体主壳
        const housingGeometry = new THREE.CylinderGeometry(1.5, 1.5, 3);
        const housingMaterial = new THREE.MeshPhongMaterial({
            color: 0x3498db,
            shininess: 80,
            metalness: 0.3
        });
        
        const housing = this.addMesh(
            housingGeometry,
            housingMaterial,
            new THREE.Vector3(0, 1.5, 0)
        );
        housing.userData.isHousing = true;
        
        // 泵体法兰
        const flangeGeometry = new THREE.CylinderGeometry(1.8, 1.8, 0.2);
        const flangeMaterial = this.materials.get('metal');
        
        const topFlange = this.addMesh(
            flangeGeometry,
            flangeMaterial,
            new THREE.Vector3(0, 2.9, 0)
        );
        
        const bottomFlange = this.addMesh(
            flangeGeometry,
            flangeMaterial,
            new THREE.Vector3(0, 0.1, 0)
        );
        
        // 泵体窗口（检查口）
        const windowGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.1);
        const windowMaterial = this.materials.get('glass');
        
        const window = this.addMesh(
            windowGeometry,
            windowMaterial,
            new THREE.Vector3(1.4, 1.5, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        
        this.pumpHousing = housing;
    }

    createImpellerSystem() {
        const impellerGroup = new THREE.Group();
        
        // 叶轮轮毂
        const hubGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.4);
        const hubMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50,
            shininess: 100
        });
        
        const hub = new THREE.Mesh(hubGeometry, hubMaterial);
        hub.position.set(0, 1.5, 0);
        hub.castShadow = true;
        impellerGroup.add(hub);
        
        // 叶轮叶片
        const bladeCount = 6;
        const bladeGeometry = new THREE.BoxGeometry(0.8, 0.05, 0.3);
        const bladeMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e,
            shininess: 80
        });
        
        for (let i = 0; i < bladeCount; i++) {
            const angle = (i * 2 * Math.PI) / bladeCount;
            const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
            
            blade.position.set(
                Math.cos(angle) * 0.6,
                1.5,
                Math.sin(angle) * 0.6
            );
            blade.rotation.y = angle + Math.PI / 2;
            blade.rotation.z = Math.PI / 6; // 叶片角度
            blade.castShadow = true;
            
            impellerGroup.add(blade);
        }
        
        // 主轴
        const shaftGeometry = new THREE.CylinderGeometry(0.08, 0.08, 4);
        const shaftMaterial = this.materials.get('metal');
        
        const shaft = new THREE.Mesh(shaftGeometry, shaftMaterial);
        shaft.position.set(0, 2, 0);
        shaft.castShadow = true;
        impellerGroup.add(shaft);
        
        // 轴承
        const bearingGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.3);
        const bearingMaterial = new THREE.MeshPhongMaterial({
            color: 0x7f8c8d
        });
        
        const topBearing = new THREE.Mesh(bearingGeometry, bearingMaterial);
        topBearing.position.set(0, 3.5, 0);
        impellerGroup.add(topBearing);
        
        const bottomBearing = new THREE.Mesh(bearingGeometry, bearingMaterial);
        bottomBearing.position.set(0, 0.5, 0);
        impellerGroup.add(bottomBearing);
        
        this.group.add(impellerGroup);
        this.impeller = impellerGroup;
    }

    createMotor() {
        const motorGroup = new THREE.Group();
        
        // 电机主体
        const motorGeometry = new THREE.CylinderGeometry(0.8, 0.8, 1.2);
        const motorMaterial = new THREE.MeshPhongMaterial({
            color: 0x2980b9,
            shininess: 60
        });
        
        const motor = new THREE.Mesh(motorGeometry, motorMaterial);
        motor.position.set(0, 4.6, 0);
        motor.castShadow = true;
        motorGroup.add(motor);
        
        // 电机风扇罩
        const fanCoverGeometry = new THREE.CylinderGeometry(0.9, 0.9, 0.3);
        const fanCoverMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        const fanCover = new THREE.Mesh(fanCoverGeometry, fanCoverMaterial);
        fanCover.position.set(0, 5.4, 0);
        motorGroup.add(fanCover);
        
        // 散热片
        for (let i = 0; i < 8; i++) {
            const finGeometry = new THREE.BoxGeometry(0.05, 1, 0.1);
            const finMaterial = this.materials.get('metal');
            
            const angle = (i * 2 * Math.PI) / 8;
            const fin = new THREE.Mesh(finGeometry, finMaterial);
            fin.position.set(
                Math.cos(angle) * 0.85,
                4.6,
                Math.sin(angle) * 0.85
            );
            fin.rotation.y = angle;
            motorGroup.add(fin);
        }
        
        // 接线盒
        const junctionBoxGeometry = new THREE.BoxGeometry(0.4, 0.3, 0.6);
        const junctionBoxMaterial = new THREE.MeshPhongMaterial({
            color: 0x95a5a6
        });
        
        const junctionBox = new THREE.Mesh(junctionBoxGeometry, junctionBoxMaterial);
        junctionBox.position.set(1, 4.6, 0);
        motorGroup.add(junctionBox);
        
        // 电缆
        const cableGeometry = new THREE.CylinderGeometry(0.03, 0.03, 2);
        const cableMaterial = new THREE.MeshPhongMaterial({
            color: 0x2c3e50
        });
        
        const cable = new THREE.Mesh(cableGeometry, cableMaterial);
        cable.position.set(1.5, 4, 0);
        cable.rotation.z = Math.PI / 4;
        motorGroup.add(cable);
        
        this.group.add(motorGroup);
        this.motor = motorGroup;
    }

    createPiping() {
        // 进水管
        const inletPipeGeometry = new THREE.CylinderGeometry(0.4, 0.4, 2);
        const pipeMaterial = this.materials.get('metal');
        
        const inletPipe = this.addMesh(
            inletPipeGeometry,
            pipeMaterial,
            new THREE.Vector3(-2, 0.5, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        inletPipe.userData.isInletPipe = true;
        
        // 出水管
        const outletPipe = this.addMesh(
            inletPipeGeometry,
            pipeMaterial,
            new THREE.Vector3(2, 2.5, 0),
            new THREE.Euler(0, 0, Math.PI / 2)
        );
        outletPipe.userData.isOutletPipe = true;
        
        // 弯头连接
        const elbowGeometry = new THREE.TorusGeometry(0.6, 0.4, 8, 16, Math.PI / 2);
        
        const inletElbow = this.addMesh(
            elbowGeometry,
            pipeMaterial,
            new THREE.Vector3(-1, 0.5, 0),
            new THREE.Euler(0, 0, Math.PI)
        );
        
        const outletElbow = this.addMesh(
            elbowGeometry,
            pipeMaterial,
            new THREE.Vector3(1, 2.5, 0),
            new THREE.Euler(0, 0, 0)
        );
        
        // 阀门
        const valveGeometry = new THREE.SphereGeometry(0.5);
        const valveMaterial = new THREE.MeshPhongMaterial({
            color: 0xe74c3c
        });
        
        const inletValve = this.addMesh(
            valveGeometry,
            valveMaterial,
            new THREE.Vector3(-3, 0.5, 0)
        );
        
        const outletValve = this.addMesh(
            valveGeometry,
            valveMaterial,
            new THREE.Vector3(3, 2.5, 0)
        );
        
        this.piping = {
            inletPipe,
            outletPipe,
            inletValve,
            outletValve
        };
    }

    createControlSystem() {
        // 控制柜
        const controlPanelGeometry = new THREE.BoxGeometry(1.2, 1.8, 0.4);
        const controlPanelMaterial = new THREE.MeshPhongMaterial({
            color: 0x34495e
        });
        
        const controlPanel = this.addMesh(
            controlPanelGeometry,
            controlPanelMaterial,
            new THREE.Vector3(3, 1.9, -2)
        );
        
        // 显示屏
        const screenGeometry = new THREE.PlaneGeometry(0.6, 0.4);
        const screenMaterial = new THREE.MeshPhongMaterial({
            color: 0x000000,
            emissive: 0x001100
        });
        
        const screen = new THREE.Mesh(screenGeometry, screenMaterial);
        screen.position.set(3, 2.3, -1.8);
        this.group.add(screen);
        
        // 控制按钮
        const buttonGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.02);
        const buttonColors = [0xe74c3c, 0x27ae60, 0xf39c12];
        
        for (let i = 0; i < 3; i++) {
            const buttonMaterial = new THREE.MeshPhongMaterial({
                color: buttonColors[i],
                emissive: buttonColors[i],
                emissiveIntensity: 0.2
            });
            
            const button = new THREE.Mesh(buttonGeometry, buttonMaterial);
            button.position.set(3, 1.5 + i * 0.2, -1.8);
            button.rotation.x = Math.PI / 2;
            this.group.add(button);
        }
        
        // 指示灯
        const indicatorGeometry = new THREE.SphereGeometry(0.03);
        const indicatorMaterial = new THREE.MeshPhongMaterial({
            color: 0x00ff00,
            emissive: 0x00ff00,
            emissiveIntensity: 0.5
        });
        
        const runIndicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
        runIndicator.position.set(3, 2.8, -1.8);
        this.group.add(runIndicator);
        
        this.controlSystem = {
            panel: controlPanel,
            screen: screen,
            runIndicator: runIndicator
        };
    }

    createWaterFlowEffect() {
        // 进水流效果
        const inletParticleCount = 150;
        const inletGeometry = new THREE.BufferGeometry();
        
        const inletPositions = new Float32Array(inletParticleCount * 3);
        const inletVelocities = new Float32Array(inletParticleCount * 3);
        const inletColors = new Float32Array(inletParticleCount * 3);
        
        for (let i = 0; i < inletParticleCount; i++) {
            const i3 = i * 3;
            
            // 从进水管开始
            inletPositions[i3] = -3 + Math.random() * 0.5;
            inletPositions[i3 + 1] = 0.3 + Math.random() * 0.4;
            inletPositions[i3 + 2] = (Math.random() - 0.5) * 0.6;
            
            // 向泵体流动
            inletVelocities[i3] = 1 + Math.random() * 0.5;
            inletVelocities[i3 + 1] = 0.2 + Math.random() * 0.3;
            inletVelocities[i3 + 2] = (Math.random() - 0.5) * 0.2;
            
            // 蓝色水流
            const color = new THREE.Color(0x006994);
            inletColors[i3] = color.r;
            inletColors[i3 + 1] = color.g;
            inletColors[i3 + 2] = color.b;
        }
        
        inletGeometry.setAttribute('position', new THREE.BufferAttribute(inletPositions, 3));
        inletGeometry.setAttribute('velocity', new THREE.BufferAttribute(inletVelocities, 3));
        inletGeometry.setAttribute('color', new THREE.BufferAttribute(inletColors, 3));
        
        const inletMaterial = new THREE.PointsMaterial({
            size: 0.06,
            transparent: true,
            opacity: 0.7,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.inletFlow = new THREE.Points(inletGeometry, inletMaterial);
        this.group.add(this.inletFlow);
        
        // 出水流效果
        const outletParticleCount = 200;
        const outletGeometry = new THREE.BufferGeometry();
        
        const outletPositions = new Float32Array(outletParticleCount * 3);
        const outletVelocities = new Float32Array(outletParticleCount * 3);
        const outletColors = new Float32Array(outletParticleCount * 3);
        
        for (let i = 0; i < outletParticleCount; i++) {
            const i3 = i * 3;
            
            // 从泵体出口开始
            outletPositions[i3] = 1 + Math.random() * 0.5;
            outletPositions[i3 + 1] = 2.3 + Math.random() * 0.4;
            outletPositions[i3 + 2] = (Math.random() - 0.5) * 0.6;
            
            // 向出水管流动
            outletVelocities[i3] = 1.5 + Math.random() * 0.8;
            outletVelocities[i3 + 1] = 0.1 + Math.random() * 0.2;
            outletVelocities[i3 + 2] = (Math.random() - 0.5) * 0.2;
            
            // 更亮的蓝色（表示加压后）
            const color = new THREE.Color(0x0088cc);
            outletColors[i3] = color.r;
            outletColors[i3 + 1] = color.g;
            outletColors[i3 + 2] = color.b;
        }
        
        outletGeometry.setAttribute('position', new THREE.BufferAttribute(outletPositions, 3));
        outletGeometry.setAttribute('velocity', new THREE.BufferAttribute(outletVelocities, 3));
        outletGeometry.setAttribute('color', new THREE.BufferAttribute(outletColors, 3));
        
        const outletMaterial = new THREE.PointsMaterial({
            size: 0.08,
            transparent: true,
            opacity: 0.8,
            vertexColors: true,
            blending: THREE.AdditiveBlending
        });
        
        this.outletFlow = new THREE.Points(outletGeometry, outletMaterial);
        this.group.add(this.outletFlow);
    }

    setupAnimations() {
        // 叶轮旋转动画
        this.addAnimation('impellerRotation', (start) => {
            this.impellerActive = start;
        });
        
        // 电机振动动画
        this.addAnimation('motorVibration', (start) => {
            this.isVibrating = start;
        });
        
        // 水流动画
        this.addAnimation('waterFlow', (start) => {
            this.waterFlowActive = start;
        });
    }

    updateEquipment(deltaTime) {
        // 更新叶轮旋转
        if (this.impellerActive && this.impeller) {
            const rpm = this.parameters.get('pumpSpeed');
            const rotationSpeed = (rpm / 60) * 2 * Math.PI; // 转换为弧度/秒
            this.impellerRotation += rotationSpeed * deltaTime;
            this.impeller.rotation.y = this.impellerRotation;
        }
        
        // 更新电机振动
        if (this.isVibrating && this.motor) {
            this.motorVibration += deltaTime * 50; // 振动频率
            const vibrationAmplitude = 0.02;
            this.motor.position.y = 4.6 + Math.sin(this.motorVibration) * vibrationAmplitude;
        }
        
        // 更新水流粒子
        if (this.waterFlowActive) {
            this.updateWaterFlow(deltaTime);
        }
        
        // 更新功耗
        this.updatePowerConsumption();
    }

    updateWaterFlow(deltaTime) {
        // 更新进水流
        if (this.inletFlow) {
            const positions = this.inletFlow.geometry.attributes.position.array;
            const velocities = this.inletFlow.geometry.attributes.velocity.array;
            
            for (let i = 0; i < positions.length; i += 3) {
                positions[i] += velocities[i] * deltaTime;
                positions[i + 1] += velocities[i + 1] * deltaTime;
                positions[i + 2] += velocities[i + 2] * deltaTime;
                
                // 重置超出范围的粒子
                if (positions[i] > 1) {
                    positions[i] = -3 + Math.random() * 0.5;
                    positions[i + 1] = 0.3 + Math.random() * 0.4;
                    positions[i + 2] = (Math.random() - 0.5) * 0.6;
                }
            }
            
            this.inletFlow.geometry.attributes.position.needsUpdate = true;
        }
        
        // 更新出水流
        if (this.outletFlow) {
            const positions = this.outletFlow.geometry.attributes.position.array;
            const velocities = this.outletFlow.geometry.attributes.velocity.array;
            
            for (let i = 0; i < positions.length; i += 3) {
                positions[i] += velocities[i] * deltaTime;
                positions[i + 1] += velocities[i + 1] * deltaTime;
                positions[i + 2] += velocities[i + 2] * deltaTime;
                
                // 重置超出范围的粒子
                if (positions[i] > 4) {
                    positions[i] = 1 + Math.random() * 0.5;
                    positions[i + 1] = 2.3 + Math.random() * 0.4;
                    positions[i + 2] = (Math.random() - 0.5) * 0.6;
                }
            }
            
            this.outletFlow.geometry.attributes.position.needsUpdate = true;
        }
    }

    updatePowerConsumption() {
        if (this.isRunning) {
            const motorPower = this.parameters.get('motorPower');
            const efficiency = this.parameters.get('efficiency');
            const loadFactor = this.parameters.get('flowRate') / 150; // 假设额定流量150m³/h
            
            this.powerConsumption = motorPower * loadFactor / efficiency;
        } else {
            this.powerConsumption = 0;
        }
    }

    onParameterChanged(name, newValue, oldValue) {
        switch (name) {
            case 'pumpSpeed':
                // 转速变化影响流量和功耗
                const speedRatio = newValue / 1450; // 额定转速1450rpm
                this.parameters.set('flowRate', 120 * speedRatio);
                break;
            case 'flowRate':
                // 流量变化影响出水粒子密度
                this.updateFlowParticles();
                break;
        }
    }

    updateFlowParticles() {
        // 根据流量调整粒子数量和速度
        const flowRate = this.parameters.get('flowRate');
        const flowRatio = flowRate / 120; // 额定流量120m³/h
        
        if (this.outletFlow) {
            this.outletFlow.material.opacity = 0.5 + flowRatio * 0.3;
        }
    }

    getStatus() {
        const baseStatus = super.getStatus();
        return {
            ...baseStatus,
            impellerSpeed: this.parameters.get('pumpSpeed'),
            actualFlowRate: this.parameters.get('flowRate'),
            headPressure: this.parameters.get('headPressure'),
            motorTemperature: 45 + Math.random() * 20, // 模拟温度
            vibrationLevel: this.isVibrating ? Math.random() * 5 : 0,
            pumpEfficiency: this.parameters.get('efficiency'),
            powerConsumption: this.powerConsumption
        };
    }
}
