/**
 * 设备管理器 - 负责所有污水处理设备的创建和管理
 */

import * as THREE from 'three';
import { GratingChamber } from './GratingChamber.js';
import { GritChamber } from './GritChamber.js';
import { LiftPump } from './LiftPump.js';
import { PrimaryClarifier } from './PrimaryClarifier.js';
import { BiologicalTank } from './BiologicalTank.js';
import { SecondaryClarifier } from './SecondaryClarifier.js';
import { AdvancedTreatment } from './AdvancedTreatment.js';
import { PipelineSystem } from './PipelineSystem.js';
import { RealPipelineSystem } from './RealPipelineSystem.js';
import { EnhancedPipelineSystem } from './EnhancedPipelineSystem.js';
import { Tank } from './Tank.js';
import { SandFilter } from './SandFilter.js';
import { ImprovedGrating } from './ImprovedGrating.js';

export class EquipmentManager {
    constructor(scene) {
        this.scene = scene;
        this.equipment = new Map();
        this.pipelineSystem = null;
        this.labels = new Map();
        this.isLabelsVisible = true;
        this.isPipelineVisible = true;
        
        // 设备配置 - 居中两排布局设计
        this.equipmentConfig = {
            // === 主流程设备（按工艺顺序从左到右排列）===
            // 0. 原水 - 进水源
            rawWaterTank: {
                position: new THREE.Vector3(-90, 0, 0),
                name: '原水',
                type: 'source',
                id: 'RAW-WATER'
            },
            // 1. 调节池 (T-00) - 第一个处理单元
            adjustmentTank: {
                position: new THREE.Vector3(-60, 0, 0),
                name: '调节池 (T-00)',
                type: 'tank',
                id: 'T-00',
                sensors: ['LS-001', 'LS-002', 'LS-003']
            },
            // 2. 细格栅 (F-01) - 第二个处理单元
            fineGrating: {
                position: new THREE.Vector3(-40, 0, 0),
                name: '细格栅 (F-01)',
                type: 'pretreatment',
                id: 'F-01'
            },
            // 3. 选择池 (T-01) - 第三个处理单元
            selectionTank: {
                position: new THREE.Vector3(-20, 0, 0),
                name: '选择池 (T-01)',
                type: 'tank',
                id: 'T-01'
            },
            // 4. 反应池 (R-01、R-02) - 第四个处理单元
            reactionTanks: {
                position: new THREE.Vector3(0, 0, 0),
                name: '反应池 (R-01/R-02)',
                type: 'biological',
                id: 'R-01/R-02',
                equipment: ['B-02', 'B-03', 'TT201'],
                valves: ['XV201A', 'XV201B', 'XV201C', 'XV201D', 'XV202A', 'XV202B', 'XV202C', 'XV202D']
            },
            // 5. 二沉池 (S-01) - 第五个处理单元
            secondaryClarifier: {
                position: new THREE.Vector3(20, 0, 0),
                name: '二沉池 (S-01)',
                type: 'clarifier',
                id: 'S-01',
                equipment: ['FIT201', 'P-01'],
                valves: ['XV203', 'XV204']
            },
            // 6. 二级产水池 (T-02) - 第六个处理单元
            clearWaterTank: {
                position: new THREE.Vector3(40, 0, 0),
                name: '二级产水池 (T-02)',
                type: 'tank',
                id: 'T-02',
                sensors: ['LS-301', 'LS-302', 'LS-303'],
                equipment: ['P-02']
            },
            // 7. 砂滤罐 (F-03) - 第七个处理单元
            sandFilter: {
                position: new THREE.Vector3(60, 0, 0),
                name: '砂滤罐 (F-03)',
                type: 'filter',
                id: 'F-03',
                sensors: ['DPIS301'],
                valves: ['XV301', 'XV302', 'XV303']
            },
            // 8. 加氯罐 (T-07) - 第八个处理单元
            chlorinationTank: {
                position: new THREE.Vector3(80, 0, 0),
                name: '加氯罐 (T-07)',
                type: 'tank',
                id: 'T-07',
                equipment: ['LS503']
            },
            // 9. 三级产水池 (T-03) - 第九个处理单元
            tertiaryClarifier: {
                position: new THREE.Vector3(100, 0, 0),
                name: '三级产水池 (T-03)',
                type: 'tank',
                id: 'T-03',
                sensors: ['LS-304']
            },
            // 10. 总出水 - 最终出水
            finalOutletTank: {
                position: new THREE.Vector3(130, 0, 0),
                name: '总出水',
                type: 'outlet',
                id: 'FINAL-OUTLET'
            },

            // === 附属设备 ===
            // 碳源罐 (T-05) - 连接到选择池
            carbonSourceTank: {
                position: new THREE.Vector3(-20, 0, -15),
                name: '碳源罐 (T-05)',
                type: 'tank',
                id: 'T-05'
            },
            // 污泥池 (T-04) - 接收二沉池污泥
            sludgeTank: {
                position: new THREE.Vector3(20, 0, -15),
                name: '污泥池 (T-04)',
                type: 'tank',
                id: 'T-04'
            },


        };
    }

    async loadAllEquipment() {
        try {
            // 按真实工艺流程顺序加载设备
            console.log('开始加载设备...');

            // 按工艺流程顺序加载设备
            await this.loadRawWaterTank();
            console.log('原水设备加载完成');

            await this.loadAdjustmentTank();
            console.log('调节池加载完成');

            await this.loadFineGrating();
            console.log('细格栅加载完成');

            await this.loadSelectionTank();
            console.log('选择池加载完成');

            await this.loadReactionTanks();
            console.log('反应池加载完成');

            await this.loadSecondaryClarifier();
            console.log('二沉池加载完成');

            await this.loadClearWaterTank();
            console.log('二级产水池加载完成');

            await this.loadSandFilter();
            console.log('砂滤罐加载完成');

            await this.loadTertiaryClarifier();
            console.log('三级产水池加载完成');

            await this.loadFinalOutletTank();
            console.log('总出水设备加载完成');

            // 加载剩余设备
            await this.loadCarbonSourceTank();
            console.log('碳源罐加载完成');

            await this.loadChlorinationTank();
            console.log('加氯罐加载完成');

            await this.loadSludgeTank();
            console.log('污泥池加载完成');

            // 创建管道系统
            await this.createPipelineSystem();
            console.log('管道系统创建完成');

            // 创建标签
            this.createLabels();
            console.log('标签创建完成');

            console.log('设备加载完成 - 真实污水处理工艺流程（简化版）');

        } catch (error) {
            console.error('设备加载失败:', error);
            throw error;
        }
    }

    // 1. 调节池 (T-00)
    async loadAdjustmentTank() {
        const config = this.equipmentConfig.adjustmentTank;
        const tank = new Tank(config.position, {
            width: 10, height: 5, depth: 8,
            color: 0x8B4513, opacity: 0.6,
            name: config.name, id: config.id,
            sensors: config.sensors
        });
        await tank.create();

        this.scene.add(tank.group);
        this.equipment.set('adjustmentTank', tank);

        tank.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: tank
        };
    }

    // 2. 粗格栅
    async loadCoarseGrating() {
        const config = this.equipmentConfig.coarseGrating;
        const grating = new ImprovedGrating(config.position, {
            gratingType: 'coarse',
            mode: config.mode,
            name: config.name,
            id: config.id
        });
        await grating.create();

        this.scene.add(grating.group);
        this.equipment.set('coarseGrating', grating);

        grating.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: grating
        };
    }

    // 3. 提升泵 (P-00A、P-00B)
    async loadLiftPumps() {
        const config = this.equipmentConfig.liftPumps;
        const liftPump = new LiftPump(config.position);
        await liftPump.create();

        this.scene.add(liftPump.group);
        this.equipment.set('liftPumps', liftPump);

        liftPump.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: liftPump
        };
    }

    // 4. 细格栅 (F-01)
    async loadFineGrating() {
        const config = this.equipmentConfig.fineGrating;
        const grating = new ImprovedGrating(config.position, {
            gratingType: 'fine',
            mode: 'auto',
            name: config.name,
            id: config.id
        });
        await grating.create();

        this.scene.add(grating.group);
        this.equipment.set('fineGrating', grating);

        grating.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: grating
        };
    }

    // 5. 选择池 (T-01)
    async loadSelectionTank() {
        const config = this.equipmentConfig.selectionTank;
        const tank = new Tank(config.position, {
            width: 8, height: 4, depth: 6,
            color: 0x4169E1, opacity: 0.7,
            name: config.name, id: config.id
        });
        await tank.create();

        this.scene.add(tank.group);
        this.equipment.set('selectionTank', tank);

        tank.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: tank
        };
    }

    // 6. 碳源罐 (T-05)
    async loadCarbonSourceTank() {
        const config = this.equipmentConfig.carbonSourceTank;
        const tank = new Tank(config.position, {
            width: 4, height: 6, depth: 4,
            color: 0x8B4513, opacity: 0.8,
            name: config.name, id: config.id
        });
        await tank.create();

        this.scene.add(tank.group);
        this.equipment.set('carbonSourceTank', tank);

        tank.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: tank
        };
    }

    // 7. 反应池 (R-01、R-02)
    async loadReactionTanks() {
        const config = this.equipmentConfig.reactionTanks;
        const biologicalTank = new BiologicalTank(config.position);
        await biologicalTank.create();

        this.scene.add(biologicalTank.group);
        this.equipment.set('reactionTanks', biologicalTank);

        biologicalTank.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: biologicalTank
        };
    }

    // 8. 二沉池 (S-01) - 更新现有
    async loadSecondaryClarifier() {
        const config = this.equipmentConfig.secondaryClarifier;
        const clarifier = new SecondaryClarifier(config.position);
        await clarifier.create();

        this.scene.add(clarifier.group);
        this.equipment.set('secondaryClarifier', clarifier);

        clarifier.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: clarifier
        };
    }

    // 9. 砂滤罐 (F-03)
    async loadSandFilter() {
        const config = this.equipmentConfig.sandFilter;
        const filter = new SandFilter(config.position, {
            name: config.name,
            id: config.id,
            sensors: config.sensors,
            valves: config.valves
        });
        await filter.create();

        this.scene.add(filter.group);
        this.equipment.set('sandFilter', filter);

        filter.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: filter
        };
    }

    // 10. 加氯罐 (T-07)
    async loadChlorinationTank() {
        const config = this.equipmentConfig.chlorinationTank;
        const tank = new Tank(config.position, {
            width: 6, height: 4, depth: 4,
            color: 0x00FF7F, opacity: 0.7,
            name: config.name, id: config.id,
            equipment: config.equipment
        });
        await tank.create();

        this.scene.add(tank.group);
        this.equipment.set('chlorinationTank', tank);

        tank.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: tank
        };
    }

    // 12. 三级产水池 (T-03)
    async loadTertiaryClarifier() {
        const config = this.equipmentConfig.tertiaryClarifier;
        const tank = new Tank(config.position, {
            width: 10, height: 4, depth: 6,
            color: 0x00BFFF, opacity: 0.8,
            name: config.name, id: config.id,
            sensors: config.sensors
        });
        await tank.create();

        this.scene.add(tank.group);
        this.equipment.set('tertiaryClarifier', tank);

        tank.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: tank
        };
    }

    // 11. 二级产水池 (T-02)
    async loadClearWaterTank() {
        const config = this.equipmentConfig.clearWaterTank;
        const tank = new Tank(config.position, {
            width: 12, height: 4, depth: 8,
            color: 0x00BFFF, opacity: 0.8,
            name: config.name, id: config.id,
            sensors: config.sensors,
            equipment: config.equipment
        });
        await tank.create();

        this.scene.add(tank.group);
        this.equipment.set('clearWaterTank', tank);

        tank.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: tank
        };
    }

    // 13. 污泥池 (T-04)
    async loadSludgeTank() {
        const config = this.equipmentConfig.sludgeTank;
        const tank = new Tank(config.position, {
            width: 6, height: 3, depth: 6,
            color: 0x8B4513, opacity: 0.9,
            name: config.name, id: config.id
        });
        await tank.create();

        this.scene.add(tank.group);
        this.equipment.set('sludgeTank', tank);

        tank.group.userData = {
            name: config.name,
            type: config.type,
            id: config.id,
            equipment: tank
        };
    }

    async createPipelineSystem() {
        try {
            // 使用增强管道系统（带半透明管道和水流效果）
            this.pipelineSystem = new EnhancedPipelineSystem();
            await this.pipelineSystem.create(this);

            this.scene.add(this.pipelineSystem.group);
            console.log('增强管道系统创建成功 - 包含半透明管道和水流动画效果');
        } catch (error) {
            console.warn('增强管道系统创建失败，回退到真实工艺流程管道系统:', error);
            try {
                // 回退到原有的管道系统
                this.pipelineSystem = new RealPipelineSystem();
                await this.pipelineSystem.create(this);
                this.scene.add(this.pipelineSystem.group);
                console.log('真实工艺流程管道系统创建成功（回退方案）');
            } catch (fallbackError) {
                console.warn('管道系统创建完全失败，跳过:', fallbackError);
                // 暂时跳过管道系统，避免阻塞其他设备加载
            }
        }
    }

    createLabels() {
        // 为每个设备创建3D文字标签
        this.equipment.forEach((equipment, id) => {
            try {
                const label = this.createTextLabel(equipment.group.userData.name || equipment.name || id);
                label.position.copy(equipment.position);

                // 安全地获取边界盒高度
                let heightOffset = 2;
                if (equipment.getBoundingBox && typeof equipment.getBoundingBox === 'function') {
                    try {
                        const boundingBox = equipment.getBoundingBox();
                        heightOffset = boundingBox.max.y + 2;
                    } catch (e) {
                        console.warn(`获取设备边界盒失败: ${id}`, e);
                    }
                }

                label.position.y += heightOffset;

                this.scene.add(label);
                this.labels.set(id, label);
            } catch (error) {
                console.error(`创建标签失败: ${id}`, error);
            }
        });
    }

    createTextLabel(text) {
        // 创建3D文字标签
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        // 设置画布大小
        canvas.width = 256;
        canvas.height = 64;
        
        // 设置字体样式
        context.font = 'Bold 24px Arial';
        context.fillStyle = 'white';
        context.strokeStyle = 'black';
        context.lineWidth = 2;
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        
        // 绘制背景
        context.fillStyle = 'rgba(0, 0, 0, 0.7)';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        // 绘制文字
        context.fillStyle = 'white';
        context.strokeText(text, canvas.width / 2, canvas.height / 2);
        context.fillText(text, canvas.width / 2, canvas.height / 2);
        
        // 创建纹理和材质
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        
        // 创建精灵
        const sprite = new THREE.Sprite(material);
        sprite.scale.set(8, 2, 1);
        
        return sprite;
    }

    // 设备控制方法
    startEquipment(equipmentId) {
        const equipment = this.equipment.get(equipmentId);
        if (equipment && equipment.start) {
            equipment.start();
        }
    }

    stopEquipment(equipmentId) {
        const equipment = this.equipment.get(equipmentId);
        if (equipment && equipment.stop) {
            equipment.stop();
        }
    }

    startAllEquipment() {
        this.equipment.forEach((equipment) => {
            if (equipment.start) {
                equipment.start();
            }
        });
    }

    stopAllEquipment() {
        this.equipment.forEach((equipment) => {
            if (equipment.stop) {
                equipment.stop();
            }
        });
    }

    // 显示控制
    setLabelsVisible(visible) {
        this.isLabelsVisible = visible;
        this.labels.forEach((label) => {
            label.visible = visible;
        });
    }

    setPipelineVisible(visible) {
        this.isPipelineVisible = visible;
        if (this.pipelineSystem) {
            this.pipelineSystem.setVisible(visible);
        }
    }

    // 设置管道透明度
    setPipelineOpacity(opacity) {
        if (this.pipelineSystem && this.pipelineSystem.setPipeOpacity) {
            this.pipelineSystem.setPipeOpacity(opacity);
        }
    }

    // 设置水流透明度
    setWaterFlowOpacity(opacity) {
        if (this.pipelineSystem && this.pipelineSystem.setWaterOpacity) {
            this.pipelineSystem.setWaterOpacity(opacity);
        }
    }

    // 设置水流速度
    setFlowSpeed(speed) {
        if (this.pipelineSystem && this.pipelineSystem.setFlowSpeed) {
            this.pipelineSystem.setFlowSpeed(speed);
        }
    }

    // 设置水流方向
    setFlowDirection(direction) {
        if (this.pipelineSystem && this.pipelineSystem.setFlowDirection) {
            this.pipelineSystem.setFlowDirection(direction);
        }
    }

    // 设置管道粒子效果
    setPipelineParticlesEnabled(enabled) {
        if (this.pipelineSystem && this.pipelineSystem.setParticlesEnabled) {
            this.pipelineSystem.setParticlesEnabled(enabled);
        }
    }

    // 设备查询方法
    getEquipment(id) {
        return this.equipment.get(id);
    }

    getAllEquipment() {
        return Array.from(this.equipment.values());
    }

    getEquipmentByType(type) {
        return Array.from(this.equipment.values()).filter(
            equipment => equipment.group.userData.type === type
        );
    }

    getEquipmentCount() {
        return this.equipment.size;
    }

    // 设备状态查询
    getEquipmentStatus(equipmentId) {
        const equipment = this.equipment.get(equipmentId);
        if (equipment && equipment.getStatus) {
            return equipment.getStatus();
        }
        return null;
    }

    getAllEquipmentStatus() {
        const status = {};
        this.equipment.forEach((equipment, id) => {
            if (equipment.getStatus) {
                status[id] = equipment.getStatus();
            }
        });
        return status;
    }

    // 设备参数设置
    setEquipmentParameter(equipmentId, parameter, value) {
        const equipment = this.equipment.get(equipmentId);
        if (equipment && equipment.setParameter) {
            equipment.setParameter(parameter, value);
        }
    }

    // 更新方法
    update(deltaTime) {
        // 更新所有设备
        this.equipment.forEach((equipment) => {
            if (equipment.update) {
                equipment.update(deltaTime);
            }
        });
        
        // 更新管道系统
        if (this.pipelineSystem && this.pipelineSystem.update) {
            this.pipelineSystem.update(deltaTime);
        }
        
        // 更新标签朝向相机
        this.updateLabels();
    }

    updateLabels() {
        // 让标签始终面向相机
        if (this.scene.userData.camera) {
            const camera = this.scene.userData.camera;
            this.labels.forEach((label) => {
                label.lookAt(camera.position);
            });
        }
    }

    // 资源清理
    dispose() {
        // 清理设备
        this.equipment.forEach((equipment) => {
            if (equipment.dispose) {
                equipment.dispose();
            }
            this.scene.remove(equipment.group);
        });
        
        // 清理标签
        this.labels.forEach((label) => {
            if (label.material.map) {
                label.material.map.dispose();
            }
            label.material.dispose();
            this.scene.remove(label);
        });
        
        // 清理管道系统
        if (this.pipelineSystem) {
            this.pipelineSystem.dispose();
            this.scene.remove(this.pipelineSystem.group);
        }
        
        this.equipment.clear();
        this.labels.clear();
    }

    // 原水设备加载方法
    async loadRawWaterTank() {
        try {
            console.log('开始加载原水设备...');
            const config = this.equipmentConfig.rawWaterTank;
            console.log('原水设备配置:', config);

            const rawWaterTank = await this.createWaterTank(config, 'raw');
            this.equipment.set('rawWaterTank', rawWaterTank);
            this.scene.add(rawWaterTank.group);

            console.log('原水设备加载成功，设备数量:', this.equipment.size);
        } catch (error) {
            console.error('原水设备加载失败:', error);
        }
    }

    // 总出水设备加载方法
    async loadFinalOutletTank() {
        try {
            console.log('开始加载总出水设备...');
            const config = this.equipmentConfig.finalOutletTank;
            console.log('总出水设备配置:', config);

            const finalOutletTank = await this.createWaterTank(config, 'clean');
            this.equipment.set('finalOutletTank', finalOutletTank);
            this.scene.add(finalOutletTank.group);

            console.log('总出水设备加载成功，设备数量:', this.equipment.size);
        } catch (error) {
            console.error('总出水设备加载失败:', error);
        }
    }

    // 创建水箱的通用方法
    async createWaterTank(config, waterType) {
        try {
            const tank = {
                group: new THREE.Group(),
                position: config.position.clone(),
                name: config.name,
                type: config.type,
                id: config.id
            };

            // 设置组位置
            tank.group.position.copy(config.position);

            // 创建半透明立方体容器
            const tankGeometry = new THREE.BoxGeometry(6, 4, 4);
            const tankMaterial = new THREE.MeshLambertMaterial({
                color: 0x666666,
                transparent: true,
                opacity: 0.3
            });
            const tankMesh = new THREE.Mesh(tankGeometry, tankMaterial);
            tankMesh.position.y = 2;
            tankMesh.castShadow = true;
            tankMesh.receiveShadow = true;
            tank.group.add(tankMesh);

            // 创建水体
            const waterGeometry = new THREE.BoxGeometry(5.5, 3.5, 3.5);
            let waterColor, waterOpacity;

            if (waterType === 'raw') {
                // 原水：深色，污浊
                waterColor = 0x4a4a2a;
                waterOpacity = 0.8;
            } else {
                // 总出水：浅色，清澈
                waterColor = 0x87ceeb;
                waterOpacity = 0.6;
            }

            const waterMaterial = new THREE.MeshLambertMaterial({
                color: waterColor,
                transparent: true,
                opacity: waterOpacity
            });
            const waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
            waterMesh.position.y = 2;
            tank.group.add(waterMesh);

            // 创建简单标识牌
            this.createSimpleTankLabel(tank, config.name);

            // 添加管道连接点
            this.createSimplePipeConnections(tank, waterType);

            // 添加getBoundingBox方法
            tank.getBoundingBox = function() {
                const box = new THREE.Box3();
                box.setFromObject(this.group);
                return box;
            };

            // 设置userData
            tank.group.userData = {
                name: config.name,
                type: config.type,
                id: config.id
            };

            console.log(`水箱创建成功: ${config.name}`);
            return tank;
        } catch (error) {
            console.error(`创建水箱失败: ${config.name}`, error);
            // 返回一个简单的组，避免系统崩溃
            return {
                group: new THREE.Group(),
                position: config.position.clone(),
                name: config.name,
                type: config.type,
                id: config.id
            };
        }
    }

    // 创建简单水箱标识牌
    createSimpleTankLabel(tank, text) {
        try {
            // 创建简单的文字标识
            const labelGeometry = new THREE.BoxGeometry(4, 0.5, 0.1);
            const labelMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });
            const labelMesh = new THREE.Mesh(labelGeometry, labelMaterial);
            labelMesh.position.set(0, 5, 0);
            tank.group.add(labelMesh);

            // 添加文字区域（用颜色表示）
            const textGeometry = new THREE.BoxGeometry(3.5, 0.3, 0.05);
            const textMaterial = new THREE.MeshLambertMaterial({
                color: 0xffffff,
                emissive: 0xffffff,
                emissiveIntensity: 0.2
            });
            const textMesh = new THREE.Mesh(textGeometry, textMaterial);
            textMesh.position.set(0, 5, 0.1);
            tank.group.add(textMesh);

            console.log(`标识牌创建成功: ${text}`);
        } catch (error) {
            console.error(`标识牌创建失败: ${text}`, error);
        }
    }

    // 创建简单管道连接点
    createSimplePipeConnections(tank, waterType) {
        try {
            const connectionGeometry = new THREE.CylinderGeometry(0.2, 0.2, 1);
            const connectionMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });

            if (waterType === 'raw') {
                // 原水：出水口在右侧
                const outlet = new THREE.Mesh(connectionGeometry, connectionMaterial);
                outlet.position.set(3, 2, 0);
                outlet.rotation.z = Math.PI / 2;
                outlet.castShadow = true;
                outlet.receiveShadow = true;
                tank.group.add(outlet);
            } else {
                // 总出水：进水口在左侧
                const inlet = new THREE.Mesh(connectionGeometry, connectionMaterial);
                inlet.position.set(-3, 2, 0);
                inlet.rotation.z = Math.PI / 2;
                inlet.castShadow = true;
                inlet.receiveShadow = true;
                tank.group.add(inlet);
            }

            console.log(`管道连接点创建成功: ${waterType}`);
        } catch (error) {
            console.error(`管道连接点创建失败: ${waterType}`, error);
        }
    }
}
