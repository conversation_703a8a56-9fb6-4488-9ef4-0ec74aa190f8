/**
 * 事件处理器 - 处理用户交互和3D场景事件
 */

import * as THREE from 'three';
import { EquipmentBubble } from '../ui/EquipmentBubble.js';

export class EventHandler {
    constructor(camera, scene, domElement) {
        this.camera = camera;
        this.scene = scene;
        this.domElement = domElement;

        // 射线投射器用于检测鼠标点击
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();

        // 交互状态
        this.isMouseDown = false;
        this.isDragging = false;
        this.dragThreshold = 5; // 像素
        this.mouseDownPosition = new THREE.Vector2();
        this.currentMousePosition = new THREE.Vector2();

        // 悬停状态
        this.hoveredObject = null;
        this.selectedObject = null;

        // 设备数据气泡
        this.equipmentBubble = new EquipmentBubble();

        // 事件回调
        this.onEquipmentClick = null;
        this.onEquipmentHover = null;
        this.onEquipmentUnhover = null;
        this.onBackgroundClick = null;
        
        // 高亮材质
        this.originalMaterials = new Map();
        this.hoverMaterial = new THREE.MeshPhongMaterial({
            color: 0x00ff88,
            emissive: 0x002200,
            transparent: true,
            opacity: 0.8
        });
        
        this.selectedMaterial = new THREE.MeshPhongMaterial({
            color: 0x0088ff,
            emissive: 0x001122,
            transparent: true,
            opacity: 0.9
        });
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 鼠标事件
        this.domElement.addEventListener('mousedown', (event) => this.onMouseDown(event));
        this.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        this.domElement.addEventListener('mouseup', (event) => this.onMouseUp(event));
        this.domElement.addEventListener('click', (event) => this.onClick(event));
        
        // 触摸事件（移动设备支持）
        this.domElement.addEventListener('touchstart', (event) => this.onTouchStart(event));
        this.domElement.addEventListener('touchmove', (event) => this.onTouchMove(event));
        this.domElement.addEventListener('touchend', (event) => this.onTouchEnd(event));
        
        // 键盘事件
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        
        // 窗口事件
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 防止右键菜单
        this.domElement.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });
    }

    onMouseDown(event) {
        this.isMouseDown = true;
        this.isDragging = false;
        
        this.mouseDownPosition.set(event.clientX, event.clientY);
        this.currentMousePosition.set(event.clientX, event.clientY);
        
        this.updateMousePosition(event);
    }

    onMouseMove(event) {
        this.currentMousePosition.set(event.clientX, event.clientY);
        
        // 检查是否开始拖拽
        if (this.isMouseDown && !this.isDragging) {
            const distance = this.mouseDownPosition.distanceTo(this.currentMousePosition);
            if (distance > this.dragThreshold) {
                this.isDragging = true;
            }
        }
        
        // 如果不在拖拽状态，检测悬停
        if (!this.isDragging) {
            this.updateMousePosition(event);
            this.checkHover();
        }
    }

    onMouseUp(event) {
        this.isMouseDown = false;
        
        // 如果没有拖拽，则处理点击
        if (!this.isDragging) {
            this.handleClick(event);
        }
        
        this.isDragging = false;
    }

    onClick(event) {
        // 防止在拖拽后触发点击
        if (this.isDragging) {
            event.stopPropagation();
        }
    }

    onTouchStart(event) {
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            this.onMouseDown({
                clientX: touch.clientX,
                clientY: touch.clientY,
                preventDefault: () => event.preventDefault()
            });
        }
    }

    onTouchMove(event) {
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            this.onMouseMove({
                clientX: touch.clientX,
                clientY: touch.clientY
            });
        }
    }

    onTouchEnd(event) {
        if (event.changedTouches.length === 1) {
            const touch = event.changedTouches[0];
            this.onMouseUp({
                clientX: touch.clientX,
                clientY: touch.clientY
            });
        }
    }

    updateMousePosition(event) {
        const rect = this.domElement.getBoundingClientRect();
        
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    checkHover() {
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // 获取可交互的对象
        const intersectableObjects = this.getIntersectableObjects();
        const intersects = this.raycaster.intersectObjects(intersectableObjects, true);
        
        if (intersects.length > 0) {
            const intersectedObject = this.findEquipmentObject(intersects[0].object);
            
            if (intersectedObject !== this.hoveredObject) {
                // 取消之前的悬停
                if (this.hoveredObject) {
                    this.unhoverObject(this.hoveredObject);
                }
                
                // 设置新的悬停
                if (intersectedObject) {
                    this.hoverObject(intersectedObject);
                }
                
                this.hoveredObject = intersectedObject;
            }
        } else {
            // 没有悬停对象
            if (this.hoveredObject) {
                this.unhoverObject(this.hoveredObject);
                this.hoveredObject = null;
            }
        }
    }

    handleClick(event) {
        // 确保鼠标位置是最新的
        this.updateMousePosition(event);
        this.raycaster.setFromCamera(this.mouse, this.camera);

        const intersectableObjects = this.getIntersectableObjects();
        const intersects = this.raycaster.intersectObjects(intersectableObjects, true);

        if (intersects.length > 0) {
            const intersectedObject = this.findEquipmentObject(intersects[0].object);

            if (intersectedObject) {
                this.selectObject(intersectedObject);

                // 隐藏设备数据气泡，使用UIManager的infoPanel
                this.equipmentBubble.hide();

                if (this.onEquipmentClick) {
                    this.onEquipmentClick(intersectedObject);
                }
            }
        } else {
            // 点击背景
            this.deselectObject();
            this.equipmentBubble.hide();

            if (this.onBackgroundClick) {
                this.onBackgroundClick();
            }
        }
    }

    getIntersectableObjects() {
        // 获取所有可以交互的对象
        const objects = [];

        this.scene.traverse((child) => {
            // 检查mesh对象，包括设备组内的mesh
            if (child.isMesh) {
                // 检查自身或父级是否有设备数据
                let current = child;
                while (current) {
                    if (current.userData && (current.userData.equipment || current.userData.name)) {
                        objects.push(child);
                        break;
                    }
                    current = current.parent;
                }
            }
        });

        return objects;
    }

    findEquipmentObject(object) {
        // 向上查找设备组对象
        let current = object;
        
        while (current) {
            if (current.userData && current.userData.equipment) {
                return current;
            }
            current = current.parent;
        }
        
        return null;
    }

    hoverObject(object) {
        if (!object) return;
        
        // 保存原始材质并应用悬停材质
        this.applyHoverEffect(object);
        
        // 显示工具提示
        this.showTooltip(object);
        
        // 改变鼠标样式
        this.domElement.style.cursor = 'pointer';
        
        if (this.onEquipmentHover) {
            this.onEquipmentHover(object);
        }
    }

    unhoverObject(object) {
        if (!object) return;
        
        // 恢复原始材质（如果没有被选中）
        if (object !== this.selectedObject) {
            this.removeHoverEffect(object);
        }
        
        // 隐藏工具提示
        this.hideTooltip();
        
        // 恢复鼠标样式
        this.domElement.style.cursor = 'default';
        
        if (this.onEquipmentUnhover) {
            this.onEquipmentUnhover(object);
        }
    }

    selectObject(object) {
        if (!object) return;
        
        // 取消之前的选择
        if (this.selectedObject) {
            this.deselectObject();
        }
        
        this.selectedObject = object;
        
        // 应用选中效果
        this.applySelectedEffect(object);
    }

    deselectObject() {
        if (!this.selectedObject) return;
        
        // 移除选中效果
        this.removeSelectedEffect(this.selectedObject);
        
        this.selectedObject = null;
    }

    applyHoverEffect(object) {
        this.traverseAndApplyMaterial(object, this.hoverMaterial, 'hover');
    }

    removeHoverEffect(object) {
        this.traverseAndRestoreMaterial(object, 'hover');
    }

    applySelectedEffect(object) {
        this.traverseAndApplyMaterial(object, this.selectedMaterial, 'selected');
    }

    removeSelectedEffect(object) {
        this.traverseAndRestoreMaterial(object, 'selected');
    }

    traverseAndApplyMaterial(object, material, effectType) {
        object.traverse((child) => {
            if (child.isMesh && child.material) {
                // 保存原始材质
                const key = `${child.uuid}_${effectType}`;
                if (!this.originalMaterials.has(key)) {
                    this.originalMaterials.set(key, child.material);
                }
                
                // 应用新材质
                child.material = material;
            }
        });
    }

    traverseAndRestoreMaterial(object, effectType) {
        object.traverse((child) => {
            if (child.isMesh) {
                const key = `${child.uuid}_${effectType}`;
                const originalMaterial = this.originalMaterials.get(key);
                
                if (originalMaterial) {
                    child.material = originalMaterial;
                    this.originalMaterials.delete(key);
                }
            }
        });
    }

    showTooltip(object) {
        const tooltip = this.getOrCreateTooltip();
        const userData = object.userData;
        
        tooltip.innerHTML = `
            <div class="tooltip-content">
                <h4>${userData.name || '设备'}</h4>
                <p>类型: ${userData.type || '未知'}</p>
                <p>状态: ${userData.equipment?.isRunning ? '运行中' : '停止'}</p>
                <p class="tooltip-hint">点击查看详细信息</p>
            </div>
        `;
        
        tooltip.style.display = 'block';
        this.updateTooltipPosition();
    }

    hideTooltip() {
        const tooltip = document.getElementById('equipment-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }

    getOrCreateTooltip() {
        let tooltip = document.getElementById('equipment-tooltip');
        
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'equipment-tooltip';
            tooltip.style.cssText = `
                position: fixed;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                pointer-events: none;
                z-index: 10000;
                display: none;
                max-width: 200px;
            `;
            document.body.appendChild(tooltip);
        }
        
        return tooltip;
    }

    updateTooltipPosition() {
        const tooltip = document.getElementById('equipment-tooltip');
        if (!tooltip || tooltip.style.display === 'none') return;
        
        const rect = this.domElement.getBoundingClientRect();
        const x = this.currentMousePosition.x + 10;
        const y = this.currentMousePosition.y - 10;
        
        tooltip.style.left = x + 'px';
        tooltip.style.top = y + 'px';
    }

    onKeyDown(event) {
        switch (event.code) {
            case 'Escape':
                this.deselectObject();
                this.hideTooltip();
                break;
            case 'Delete':
            case 'Backspace':
                if (this.selectedObject) {
                    // 可以添加删除设备的功能
                    console.log('删除设备功能待实现');
                }
                break;
        }
    }

    onKeyUp(event) {
        // 处理按键释放事件
    }

    onWindowResize() {
        // 更新工具提示位置
        this.updateTooltipPosition();
    }

    // 公共方法
    getSelectedObject() {
        return this.selectedObject;
    }

    getHoveredObject() {
        return this.hoveredObject;
    }

    clearSelection() {
        this.deselectObject();
    }

    clearHover() {
        if (this.hoveredObject) {
            this.unhoverObject(this.hoveredObject);
            this.hoveredObject = null;
        }
    }

    // 设置事件回调
    setEquipmentClickCallback(callback) {
        this.onEquipmentClick = callback;
    }

    setEquipmentHoverCallback(callback) {
        this.onEquipmentHover = callback;
    }

    setEquipmentUnhoverCallback(callback) {
        this.onEquipmentUnhover = callback;
    }

    setBackgroundClickCallback(callback) {
        this.onBackgroundClick = callback;
    }

    // 更新方法（在渲染循环中调用）
    update() {
        this.updateTooltipPosition();
    }

    // 资源清理
    dispose() {
        // 移除事件监听器
        this.domElement.removeEventListener('mousedown', this.onMouseDown);
        this.domElement.removeEventListener('mousemove', this.onMouseMove);
        this.domElement.removeEventListener('mouseup', this.onMouseUp);
        this.domElement.removeEventListener('click', this.onClick);
        this.domElement.removeEventListener('touchstart', this.onTouchStart);
        this.domElement.removeEventListener('touchmove', this.onTouchMove);
        this.domElement.removeEventListener('touchend', this.onTouchEnd);
        this.domElement.removeEventListener('contextmenu', this.onContextMenu);
        
        document.removeEventListener('keydown', this.onKeyDown);
        document.removeEventListener('keyup', this.onKeyUp);
        window.removeEventListener('resize', this.onWindowResize);
        
        // 清理工具提示
        const tooltip = document.getElementById('equipment-tooltip');
        if (tooltip) {
            document.body.removeChild(tooltip);
        }
        
        // 清理材质缓存
        this.originalMaterials.clear();
        
        // 清理材质
        this.hoverMaterial.dispose();
        this.selectedMaterial.dispose();
        
        // 清理状态
        this.selectedObject = null;
        this.hoveredObject = null;
    }
}
