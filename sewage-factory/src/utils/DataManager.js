/**
 * 数据管理器 - 管理污水处理系统的实时数据和历史数据
 */

export class DataManager {
    constructor() {
        this.currentData = {
            cod: 120,      // 化学需氧量 mg/L
            bod: 80,       // 生化需氧量 mg/L
            ss: 45,        // 悬浮物 mg/L
            nh3n: 15,      // 氨氮 mg/L
            tp: 2.5,       // 总磷 mg/L
            efficiency: 95.2, // 处理效率 %
            flowRate: 100, // 流量 m³/h
            temperature: 25, // 温度 °C
            ph: 7.2,       // pH值
            dissolvedOxygen: 2.5, // 溶解氧 mg/L
            turbidity: 15, // 浊度 NTU
            timestamp: Date.now()
        };

        this.historicalData = [];
        this.maxHistoryPoints = 1000;
        this.simulationActive = false;
        this.simulationInterval = null;
        
        // 数据变化趋势
        this.trends = {
            cod: { direction: -1, rate: 0.5 },
            bod: { direction: -1, rate: 0.3 },
            ss: { direction: -1, rate: 0.2 },
            nh3n: { direction: -1, rate: 0.1 },
            tp: { direction: -1, rate: 0.05 },
            efficiency: { direction: 1, rate: 0.1 }
        };

        // 数据范围限制
        this.dataRanges = {
            cod: { min: 30, max: 200, target: 50 },
            bod: { min: 15, max: 120, target: 20 },
            ss: { min: 10, max: 80, target: 20 },
            nh3n: { min: 2, max: 30, target: 5 },
            tp: { min: 0.2, max: 5, target: 0.5 },
            efficiency: { min: 80, max: 99, target: 95 },
            flowRate: { min: 50, max: 150, target: 100 },
            temperature: { min: 15, max: 35, target: 25 },
            ph: { min: 6.5, max: 8.5, target: 7.2 },
            dissolvedOxygen: { min: 1, max: 8, target: 2.5 },
            turbidity: { min: 5, max: 50, target: 10 }
        };

        // 报警阈值
        this.alarmThresholds = {
            cod: { high: 150, low: null },
            bod: { high: 100, low: null },
            ss: { high: 60, low: null },
            nh3n: { high: 25, low: null },
            tp: { high: 3, low: null },
            efficiency: { high: null, low: 85 },
            ph: { high: 8.2, low: 6.8 },
            dissolvedOxygen: { high: 6, low: 1.5 },
            temperature: { high: 32, low: 18 }
        };

        this.alarms = [];
    }

    // 启动数据模拟
    startDataSimulation() {
        if (this.simulationActive) return;

        this.simulationActive = true;
        this.simulationInterval = setInterval(() => {
            this.updateSimulatedData();
        }, 2000); // 每2秒更新一次

        console.log('数据模拟已启动');
    }

    // 停止数据模拟
    stopDataSimulation() {
        if (!this.simulationActive) return;

        this.simulationActive = false;
        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
            this.simulationInterval = null;
        }

        console.log('数据模拟已停止');
    }

    // 更新模拟数据
    updateSimulatedData() {
        const newData = { ...this.currentData };
        newData.timestamp = Date.now();

        // 更新各项参数
        Object.keys(this.trends).forEach(param => {
            if (newData[param] !== undefined) {
                newData[param] = this.simulateParameterChange(param, newData[param]);
            }
        });

        // 添加一些随机波动
        newData.flowRate = this.addRandomVariation(newData.flowRate, 5);
        newData.temperature = this.addRandomVariation(newData.temperature, 2);
        newData.ph = this.addRandomVariation(newData.ph, 0.2);
        newData.dissolvedOxygen = this.addRandomVariation(newData.dissolvedOxygen, 0.3);
        newData.turbidity = this.addRandomVariation(newData.turbidity, 3);

        // 模拟设备运行状态对数据的影响
        this.simulateEquipmentEffects(newData);

        // 检查报警条件
        this.checkAlarms(newData);

        // 更新当前数据
        this.currentData = newData;

        // 添加到历史数据
        this.addToHistory(newData);
    }

    // 模拟参数变化
    simulateParameterChange(param, currentValue) {
        const trend = this.trends[param];
        const range = this.dataRanges[param];
        
        if (!trend || !range) return currentValue;

        // 基础变化
        let change = trend.direction * trend.rate * (0.5 + Math.random());
        
        // 向目标值收敛
        const targetDiff = range.target - currentValue;
        const convergenceFactor = 0.1;
        change += targetDiff * convergenceFactor;

        // 添加随机噪声
        change += (Math.random() - 0.5) * trend.rate * 0.5;

        let newValue = currentValue + change;

        // 限制在合理范围内
        newValue = Math.max(range.min, Math.min(range.max, newValue));

        // 偶尔改变趋势方向
        if (Math.random() < 0.05) {
            trend.direction *= -1;
        }

        return parseFloat(newValue.toFixed(2));
    }

    // 添加随机变化
    addRandomVariation(value, maxVariation) {
        const variation = (Math.random() - 0.5) * maxVariation;
        return parseFloat((value + variation).toFixed(2));
    }

    // 模拟设备运行状态对数据的影响
    simulateEquipmentEffects(data) {
        // 这里可以根据设备运行状态调整数据
        // 例如：如果曝气设备停止，溶解氧下降
        
        // 模拟处理效率的计算
        const codRemoval = Math.max(0, (200 - data.cod) / 200);
        const bodRemoval = Math.max(0, (120 - data.bod) / 120);
        const ssRemoval = Math.max(0, (80 - data.ss) / 80);
        
        data.efficiency = parseFloat(((codRemoval + bodRemoval + ssRemoval) / 3 * 100).toFixed(1));
    }

    // 检查报警条件
    checkAlarms(data) {
        const currentTime = Date.now();
        
        Object.keys(this.alarmThresholds).forEach(param => {
            const threshold = this.alarmThresholds[param];
            const value = data[param];
            
            if (value === undefined) return;

            let alarmType = null;
            let message = '';

            if (threshold.high && value > threshold.high) {
                alarmType = 'high';
                message = `${this.getParameterName(param)}过高: ${value}`;
            } else if (threshold.low && value < threshold.low) {
                alarmType = 'low';
                message = `${this.getParameterName(param)}过低: ${value}`;
            }

            if (alarmType) {
                // 检查是否已有相同报警
                const existingAlarm = this.alarms.find(alarm => 
                    alarm.parameter === param && alarm.type === alarmType && !alarm.acknowledged
                );

                if (!existingAlarm) {
                    this.addAlarm({
                        id: this.generateAlarmId(),
                        parameter: param,
                        type: alarmType,
                        value: value,
                        threshold: threshold[alarmType],
                        message: message,
                        timestamp: currentTime,
                        acknowledged: false,
                        severity: this.getAlarmSeverity(param, alarmType)
                    });
                }
            }
        });
    }

    // 添加报警
    addAlarm(alarm) {
        this.alarms.unshift(alarm);
        
        // 限制报警数量
        if (this.alarms.length > 100) {
            this.alarms = this.alarms.slice(0, 100);
        }

        console.warn(`报警: ${alarm.message}`);
        
        // 触发报警事件
        this.triggerAlarmEvent(alarm);
    }

    // 触发报警事件
    triggerAlarmEvent(alarm) {
        const event = new CustomEvent('dataAlarm', {
            detail: alarm
        });
        document.dispatchEvent(event);
    }

    // 确认报警
    acknowledgeAlarm(alarmId) {
        const alarm = this.alarms.find(a => a.id === alarmId);
        if (alarm) {
            alarm.acknowledged = true;
            alarm.acknowledgedAt = Date.now();
        }
    }

    // 获取参数中文名称
    getParameterName(param) {
        const names = {
            cod: 'COD',
            bod: 'BOD',
            ss: '悬浮物',
            nh3n: '氨氮',
            tp: '总磷',
            efficiency: '处理效率',
            flowRate: '流量',
            temperature: '温度',
            ph: 'pH值',
            dissolvedOxygen: '溶解氧',
            turbidity: '浊度'
        };
        return names[param] || param;
    }

    // 获取报警严重程度
    getAlarmSeverity(param, type) {
        const severityMap = {
            cod: 'medium',
            bod: 'medium',
            ss: 'low',
            nh3n: 'high',
            tp: 'medium',
            efficiency: 'high',
            ph: 'high',
            dissolvedOxygen: 'high',
            temperature: 'medium'
        };
        return severityMap[param] || 'low';
    }

    // 生成报警ID
    generateAlarmId() {
        return 'alarm_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 添加到历史数据
    addToHistory(data) {
        this.historicalData.push({ ...data });
        
        // 限制历史数据数量
        if (this.historicalData.length > this.maxHistoryPoints) {
            this.historicalData.shift();
        }
    }

    // 获取当前数据
    getCurrentData() {
        return { ...this.currentData };
    }

    // 获取历史数据
    getHistoricalData(startTime, endTime) {
        if (!startTime && !endTime) {
            return [...this.historicalData];
        }

        return this.historicalData.filter(data => {
            const timestamp = data.timestamp;
            return (!startTime || timestamp >= startTime) && 
                   (!endTime || timestamp <= endTime);
        });
    }

    // 获取参数趋势
    getParameterTrend(param, timeRange = 3600000) { // 默认1小时
        const endTime = Date.now();
        const startTime = endTime - timeRange;
        
        const data = this.getHistoricalData(startTime, endTime);
        
        if (data.length < 2) return null;

        const values = data.map(d => d[param]).filter(v => v !== undefined);
        if (values.length < 2) return null;

        const firstValue = values[0];
        const lastValue = values[values.length - 1];
        const change = lastValue - firstValue;
        const changePercent = (change / firstValue) * 100;

        return {
            parameter: param,
            startValue: firstValue,
            endValue: lastValue,
            change: change,
            changePercent: changePercent,
            trend: change > 0 ? 'increasing' : change < 0 ? 'decreasing' : 'stable',
            dataPoints: values.length
        };
    }

    // 获取统计信息
    getStatistics(param, timeRange = 3600000) {
        const data = this.getHistoricalData(Date.now() - timeRange, Date.now());
        const values = data.map(d => d[param]).filter(v => v !== undefined);

        if (values.length === 0) return null;

        const sorted = [...values].sort((a, b) => a - b);
        const sum = values.reduce((a, b) => a + b, 0);

        return {
            parameter: param,
            count: values.length,
            min: sorted[0],
            max: sorted[sorted.length - 1],
            average: sum / values.length,
            median: sorted[Math.floor(sorted.length / 2)],
            standardDeviation: this.calculateStandardDeviation(values)
        };
    }

    // 计算标准差
    calculateStandardDeviation(values) {
        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
        const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
        return Math.sqrt(avgSquaredDiff);
    }

    // 获取未确认报警
    getUnacknowledgedAlarms() {
        return this.alarms.filter(alarm => !alarm.acknowledged);
    }

    // 获取所有报警
    getAllAlarms() {
        return [...this.alarms];
    }

    // 导出数据
    exportData(format = 'json', timeRange = null) {
        let data;
        
        if (timeRange) {
            const endTime = Date.now();
            const startTime = endTime - timeRange;
            data = this.getHistoricalData(startTime, endTime);
        } else {
            data = this.historicalData;
        }

        switch (format.toLowerCase()) {
            case 'csv':
                return this.convertToCSV(data);
            case 'json':
            default:
                return JSON.stringify(data, null, 2);
        }
    }

    // 转换为CSV格式
    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => row[header]).join(','))
        ].join('\n');

        return csvContent;
    }

    // 设置数据更新回调
    onDataUpdate(callback) {
        this.dataUpdateCallback = callback;
    }

    // 手动更新数据
    updateData(newData) {
        this.currentData = { ...this.currentData, ...newData, timestamp: Date.now() };
        this.addToHistory(this.currentData);
        
        if (this.dataUpdateCallback) {
            this.dataUpdateCallback(this.currentData);
        }
    }

    // 重置数据
    resetData() {
        this.currentData = {
            cod: 120,
            bod: 80,
            ss: 45,
            nh3n: 15,
            tp: 2.5,
            efficiency: 95.2,
            flowRate: 100,
            temperature: 25,
            ph: 7.2,
            dissolvedOxygen: 2.5,
            turbidity: 15,
            timestamp: Date.now()
        };
        
        this.historicalData = [];
        this.alarms = [];
    }

    // 资源清理
    dispose() {
        this.stopDataSimulation();
        this.historicalData = [];
        this.alarms = [];
        this.dataUpdateCallback = null;
    }
}
