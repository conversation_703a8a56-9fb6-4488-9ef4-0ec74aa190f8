/**
 * 音频管理器 - 管理系统音效和环境音
 */

export class AudioManager {
    constructor() {
        this.audioContext = null;
        this.sounds = new Map();
        this.isEnabled = true;
        this.masterVolume = 0.5;
        this.ambientSounds = [];
        this.isInitialized = false;
    }

    async loadSounds() {
        try {
            // 初始化音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建主音量控制节点
            this.masterGainNode = this.audioContext.createGain();
            this.masterGainNode.connect(this.audioContext.destination);
            this.masterGainNode.gain.value = this.masterVolume;
            
            // 加载音效文件（这里使用Web Audio API生成简单音效）
            await this.generateSounds();
            
            this.isInitialized = true;
            console.log('音频系统初始化完成');
            
        } catch (error) {
            console.warn('音频系统初始化失败:', error);
            this.isEnabled = false;
        }
    }

    async generateSounds() {
        // 生成水流声
        this.sounds.set('waterFlow', this.generateWaterFlowSound());
        
        // 生成泵运行声
        this.sounds.set('pumpRunning', this.generatePumpSound());
        
        // 生成气泡声
        this.sounds.set('bubbles', this.generateBubbleSound());
        
        // 生成机械运转声
        this.sounds.set('machinery', this.generateMachinerySound());
        
        // 生成报警声
        this.sounds.set('alarm', this.generateAlarmSound());
        
        // 生成点击音效
        this.sounds.set('click', this.generateClickSound());
    }

    generateWaterFlowSound() {
        const duration = 2;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        // 生成白噪声模拟水流声
        for (let i = 0; i < data.length; i++) {
            data[i] = (Math.random() * 2 - 1) * 0.1;
        }

        // 应用低通滤波器
        this.applyLowPassFilter(data, 800, sampleRate);

        return buffer;
    }

    generatePumpSound() {
        const duration = 1;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        // 生成低频嗡嗡声
        const frequency = 60; // 60Hz
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            data[i] = Math.sin(2 * Math.PI * frequency * t) * 0.3 +
                     Math.sin(2 * Math.PI * frequency * 2 * t) * 0.1 +
                     (Math.random() * 2 - 1) * 0.05;
        }

        return buffer;
    }

    generateBubbleSound() {
        const duration = 0.5;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        // 生成气泡破裂声
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            const envelope = Math.exp(-t * 10); // 快速衰减
            data[i] = (Math.random() * 2 - 1) * envelope * 0.2;
        }

        // 应用高通滤波器
        this.applyHighPassFilter(data, 1000, sampleRate);

        return buffer;
    }

    generateMachinerySound() {
        const duration = 2;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        // 生成机械运转声
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            data[i] = Math.sin(2 * Math.PI * 120 * t) * 0.2 +
                     Math.sin(2 * Math.PI * 240 * t) * 0.1 +
                     (Math.random() * 2 - 1) * 0.1;
        }

        return buffer;
    }

    generateAlarmSound() {
        const duration = 1;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        // 生成报警声（双音调）
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            const freq = t < 0.5 ? 800 : 1000;
            data[i] = Math.sin(2 * Math.PI * freq * t) * 0.5;
        }

        return buffer;
    }

    generateClickSound() {
        const duration = 0.1;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        // 生成点击声
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            const envelope = Math.exp(-t * 50);
            data[i] = Math.sin(2 * Math.PI * 1000 * t) * envelope * 0.3;
        }

        return buffer;
    }

    applyLowPassFilter(data, cutoff, sampleRate) {
        const RC = 1.0 / (cutoff * 2 * Math.PI);
        const dt = 1.0 / sampleRate;
        const alpha = dt / (RC + dt);

        let y = data[0];
        for (let i = 1; i < data.length; i++) {
            y = y + alpha * (data[i] - y);
            data[i] = y;
        }
    }

    applyHighPassFilter(data, cutoff, sampleRate) {
        const RC = 1.0 / (cutoff * 2 * Math.PI);
        const dt = 1.0 / sampleRate;
        const alpha = RC / (RC + dt);

        let y = data[0];
        let x_prev = data[0];
        for (let i = 1; i < data.length; i++) {
            y = alpha * (y + data[i] - x_prev);
            x_prev = data[i];
            data[i] = y;
        }
    }

    playSound(soundName, volume = 1.0, loop = false) {
        if (!this.isEnabled || !this.isInitialized || !this.sounds.has(soundName)) {
            return null;
        }

        try {
            const buffer = this.sounds.get(soundName);
            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();

            source.buffer = buffer;
            source.loop = loop;
            
            gainNode.gain.value = volume * this.masterVolume;
            
            source.connect(gainNode);
            gainNode.connect(this.masterGainNode);
            
            source.start();
            
            return source;
        } catch (error) {
            console.warn('播放音效失败:', error);
            return null;
        }
    }

    playAmbientSounds() {
        if (!this.isEnabled || !this.isInitialized) return;

        // 播放环境音效
        const waterFlow = this.playSound('waterFlow', 0.3, true);
        const machinery = this.playSound('machinery', 0.2, true);

        if (waterFlow) this.ambientSounds.push(waterFlow);
        if (machinery) this.ambientSounds.push(machinery);
    }

    stopAmbientSounds() {
        this.ambientSounds.forEach(source => {
            try {
                source.stop();
            } catch (error) {
                // 忽略已经停止的音源
            }
        });
        this.ambientSounds = [];
    }

    playEquipmentSound(equipmentType, action) {
        if (!this.isEnabled || !this.isInitialized) return;

        let soundName = null;
        let volume = 0.5;

        switch (equipmentType) {
            case 'pump':
                if (action === 'start') {
                    soundName = 'pumpRunning';
                    volume = 0.4;
                }
                break;
            case 'biological':
                if (action === 'aeration') {
                    soundName = 'bubbles';
                    volume = 0.3;
                }
                break;
            case 'clarifier':
                if (action === 'scraping') {
                    soundName = 'machinery';
                    volume = 0.2;
                }
                break;
        }

        if (soundName) {
            return this.playSound(soundName, volume);
        }
    }

    playAlarm() {
        if (!this.isEnabled || !this.isInitialized) return;
        return this.playSound('alarm', 0.7);
    }

    playClickSound() {
        if (!this.isEnabled || !this.isInitialized) return;
        return this.playSound('click', 0.5);
    }

    setEnabled(enabled) {
        this.isEnabled = enabled;
        
        if (!enabled) {
            this.stopAmbientSounds();
        }
    }

    setVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        
        if (this.masterGainNode) {
            this.masterGainNode.gain.value = this.masterVolume;
        }
    }

    getVolume() {
        return this.masterVolume;
    }

    isAudioEnabled() {
        return this.isEnabled && this.isInitialized;
    }

    // 创建音频可视化器
    createVisualizer(canvas) {
        if (!this.isInitialized) return null;

        const analyser = this.audioContext.createAnalyser();
        analyser.fftSize = 256;
        
        this.masterGainNode.connect(analyser);
        
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        
        const canvasCtx = canvas.getContext('2d');
        const WIDTH = canvas.width;
        const HEIGHT = canvas.height;

        const draw = () => {
            requestAnimationFrame(draw);
            
            analyser.getByteFrequencyData(dataArray);
            
            canvasCtx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            canvasCtx.fillRect(0, 0, WIDTH, HEIGHT);
            
            const barWidth = (WIDTH / bufferLength) * 2.5;
            let barHeight;
            let x = 0;
            
            for (let i = 0; i < bufferLength; i++) {
                barHeight = dataArray[i] / 255 * HEIGHT;
                
                const r = barHeight + 25 * (i / bufferLength);
                const g = 250 * (i / bufferLength);
                const b = 50;
                
                canvasCtx.fillStyle = `rgb(${r},${g},${b})`;
                canvasCtx.fillRect(x, HEIGHT - barHeight, barWidth, barHeight);
                
                x += barWidth + 1;
            }
        };
        
        draw();
        return analyser;
    }

    dispose() {
        this.stopAmbientSounds();
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        this.sounds.clear();
        this.isInitialized = false;
    }
}
