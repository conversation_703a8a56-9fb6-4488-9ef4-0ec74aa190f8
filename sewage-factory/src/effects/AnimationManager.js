/**
 * 动画管理器 - 管理所有设备和效果的动画
 */

import * as THREE from 'three';
import * as TWEEN from '@tweenjs/tween.js';

export class AnimationManager {
    constructor() {
        this.animations = new Map();
        this.equipment = [];
        this.isRunning = false;
        this.globalSpeed = 1.0;
        
        // 动画类型
        this.animationTypes = {
            ROTATION: 'rotation',
            TRANSLATION: 'translation',
            SCALE: 'scale',
            COLOR: 'color',
            OPACITY: 'opacity',
            CUSTOM: 'custom'
        };
    }

    // 注册设备
    registerEquipment(equipmentList) {
        this.equipment = equipmentList;
        this.setupEquipmentAnimations();
    }

    // 设置设备动画
    setupEquipmentAnimations() {
        this.equipment.forEach(equipment => {
            const equipmentType = equipment.group.userData.type;
            
            switch (equipmentType) {
                case 'pump':
                    this.setupPumpAnimations(equipment);
                    break;
                case 'clarifier':
                    this.setupClarifierAnimations(equipment);
                    break;
                case 'biological':
                    this.setupBiologicalAnimations(equipment);
                    break;
                case 'pretreatment':
                    this.setupPretreatmentAnimations(equipment);
                    break;
                case 'advanced':
                    this.setupAdvancedAnimations(equipment);
                    break;
            }
        });
    }

    // 泵站动画设置
    setupPumpAnimations(equipment) {
        // 叶轮旋转动画
        if (equipment.impeller) {
            this.createRotationAnimation({
                name: `${equipment.group.userData.id}_impeller`,
                target: equipment.impeller,
                axis: 'y',
                speed: 10, // 转/秒
                continuous: true
            });
        }

        // 电机振动动画
        if (equipment.motor) {
            this.createVibrationAnimation({
                name: `${equipment.group.userData.id}_motor`,
                target: equipment.motor,
                amplitude: 0.02,
                frequency: 50 // Hz
            });
        }

        // 指示灯闪烁
        this.createBlinkAnimation({
            name: `${equipment.group.userData.id}_indicator`,
            target: equipment.group.userData.indicatorLight,
            interval: 1000 // 毫秒
        });
    }

    // 沉淀池动画设置
    setupClarifierAnimations(equipment) {
        // 刮泥器旋转
        if (equipment.scraper) {
            this.createRotationAnimation({
                name: `${equipment.group.userData.id}_scraper`,
                target: equipment.scraper,
                axis: 'y',
                speed: 0.5, // 转/秒
                continuous: true
            });
        }

        // 水面波动
        if (equipment.waterSurface) {
            this.createWaveAnimation({
                name: `${equipment.group.userData.id}_waves`,
                target: equipment.waterSurface,
                amplitude: 0.02,
                frequency: 0.5
            });
        }
    }

    // 生化池动画设置
    setupBiologicalAnimations(equipment) {
        // 搅拌器旋转
        if (equipment.mixers) {
            equipment.mixers.forEach((mixer, index) => {
                this.createRotationAnimation({
                    name: `${equipment.group.userData.id}_mixer_${index}`,
                    target: mixer,
                    axis: 'y',
                    speed: 2, // 转/秒
                    continuous: true
                });
            });
        }

        // 曝气效果
        if (equipment.aerators) {
            equipment.aerators.forEach((aerator, index) => {
                this.createPulseAnimation({
                    name: `${equipment.group.userData.id}_aerator_${index}`,
                    target: aerator,
                    minScale: 0.8,
                    maxScale: 1.2,
                    duration: 500
                });
            });
        }
    }

    // 预处理动画设置
    setupPretreatmentAnimations(equipment) {
        // 格栅清理动画
        if (equipment.cleaningMechanism) {
            this.createLinearAnimation({
                name: `${equipment.group.userData.id}_cleaning`,
                target: equipment.cleaningMechanism,
                axis: 'x',
                distance: 4,
                duration: 10000, // 10秒
                loop: true
            });
        }
    }

    // 深度处理动画设置
    setupAdvancedAnimations(equipment) {
        // UV灯闪烁
        if (equipment.uvLights) {
            equipment.uvLights.forEach((light, index) => {
                this.createUVFlickerAnimation({
                    name: `${equipment.group.userData.id}_uv_${index}`,
                    target: light,
                    baseIntensity: 0.5,
                    flickerIntensity: 0.3
                });
            });
        }
    }

    // 创建旋转动画
    createRotationAnimation(config) {
        const { name, target, axis, speed, continuous } = config;
        
        const animation = {
            type: this.animationTypes.ROTATION,
            target: target,
            axis: axis,
            speed: speed * 2 * Math.PI, // 转换为弧度/秒
            continuous: continuous,
            isActive: false,
            currentRotation: 0
        };

        this.animations.set(name, animation);
    }

    // 创建振动动画
    createVibrationAnimation(config) {
        const { name, target, amplitude, frequency } = config;
        
        const animation = {
            type: this.animationTypes.CUSTOM,
            target: target,
            amplitude: amplitude,
            frequency: frequency,
            isActive: false,
            originalPosition: target.position.clone(),
            time: 0
        };

        this.animations.set(name, animation);
    }

    // 创建闪烁动画
    createBlinkAnimation(config) {
        const { name, target, interval } = config;
        
        if (!target) return;

        const animation = {
            type: this.animationTypes.OPACITY,
            target: target,
            interval: interval,
            isActive: false,
            lastBlink: 0,
            isVisible: true
        };

        this.animations.set(name, animation);
    }

    // 创建波浪动画
    createWaveAnimation(config) {
        const { name, target, amplitude, frequency } = config;
        
        const animation = {
            type: this.animationTypes.CUSTOM,
            target: target,
            amplitude: amplitude,
            frequency: frequency,
            isActive: false,
            time: 0,
            originalVertices: null
        };

        // 保存原始顶点位置
        if (target.geometry) {
            animation.originalVertices = target.geometry.attributes.position.array.slice();
        }

        this.animations.set(name, animation);
    }

    // 创建脉冲动画
    createPulseAnimation(config) {
        const { name, target, minScale, maxScale, duration } = config;
        
        const animation = {
            type: this.animationTypes.SCALE,
            target: target,
            minScale: minScale,
            maxScale: maxScale,
            duration: duration,
            isActive: false,
            time: 0
        };

        this.animations.set(name, animation);
    }

    // 创建线性运动动画
    createLinearAnimation(config) {
        const { name, target, axis, distance, duration, loop } = config;
        
        const animation = {
            type: this.animationTypes.TRANSLATION,
            target: target,
            axis: axis,
            distance: distance,
            duration: duration,
            loop: loop,
            isActive: false,
            startPosition: target.position[axis],
            time: 0,
            direction: 1
        };

        this.animations.set(name, animation);
    }

    // 创建UV灯闪烁动画
    createUVFlickerAnimation(config) {
        const { name, target, baseIntensity, flickerIntensity } = config;
        
        const animation = {
            type: this.animationTypes.CUSTOM,
            target: target,
            baseIntensity: baseIntensity,
            flickerIntensity: flickerIntensity,
            isActive: false,
            time: 0
        };

        this.animations.set(name, animation);
    }

    // 启动所有动画
    startAllAnimations() {
        this.isRunning = true;
        this.animations.forEach(animation => {
            animation.isActive = true;
        });
    }

    // 停止所有动画
    stopAllAnimations() {
        this.isRunning = false;
        this.animations.forEach(animation => {
            animation.isActive = false;
        });
    }

    // 启动特定动画
    startAnimation(name) {
        const animation = this.animations.get(name);
        if (animation) {
            animation.isActive = true;
        }
    }

    // 停止特定动画
    stopAnimation(name) {
        const animation = this.animations.get(name);
        if (animation) {
            animation.isActive = false;
        }
    }

    // 设置全局动画速度
    setGlobalSpeed(speed) {
        this.globalSpeed = speed;
    }

    // 更新动画
    update(deltaTime) {
        if (!this.isRunning) return;

        const adjustedDeltaTime = deltaTime * this.globalSpeed;

        this.animations.forEach((animation, name) => {
            if (!animation.isActive || !animation.target) return;

            switch (animation.type) {
                case this.animationTypes.ROTATION:
                    this.updateRotationAnimation(animation, adjustedDeltaTime);
                    break;
                case this.animationTypes.TRANSLATION:
                    this.updateTranslationAnimation(animation, adjustedDeltaTime);
                    break;
                case this.animationTypes.SCALE:
                    this.updateScaleAnimation(animation, adjustedDeltaTime);
                    break;
                case this.animationTypes.OPACITY:
                    this.updateOpacityAnimation(animation, adjustedDeltaTime);
                    break;
                case this.animationTypes.CUSTOM:
                    this.updateCustomAnimation(animation, adjustedDeltaTime, name);
                    break;
            }
        });

        // 更新TWEEN动画
        TWEEN.update();
    }

    // 更新旋转动画
    updateRotationAnimation(animation, deltaTime) {
        animation.currentRotation += animation.speed * deltaTime;
        animation.target.rotation[animation.axis] = animation.currentRotation;
    }

    // 更新平移动画
    updateTranslationAnimation(animation, deltaTime) {
        animation.time += deltaTime * 1000; // 转换为毫秒

        const progress = (animation.time % animation.duration) / animation.duration;
        let position;

        if (animation.loop) {
            // 来回运动
            if (progress < 0.5) {
                position = animation.startPosition + (progress * 2) * animation.distance;
            } else {
                position = animation.startPosition + ((1 - progress) * 2) * animation.distance;
            }
        } else {
            position = animation.startPosition + progress * animation.distance;
        }

        animation.target.position[animation.axis] = position;
    }

    // 更新缩放动画
    updateScaleAnimation(animation, deltaTime) {
        animation.time += deltaTime * 1000;
        const progress = (animation.time % animation.duration) / animation.duration;
        const scale = animation.minScale + (animation.maxScale - animation.minScale) * 
                     (0.5 + 0.5 * Math.sin(progress * Math.PI * 2));
        
        animation.target.scale.setScalar(scale);
    }

    // 更新透明度动画
    updateOpacityAnimation(animation, deltaTime) {
        const currentTime = Date.now();
        
        if (currentTime - animation.lastBlink > animation.interval) {
            animation.isVisible = !animation.isVisible;
            animation.lastBlink = currentTime;
            
            if (animation.target.material) {
                animation.target.material.opacity = animation.isVisible ? 1 : 0;
            } else {
                animation.target.visible = animation.isVisible;
            }
        }
    }

    // 更新自定义动画
    updateCustomAnimation(animation, deltaTime, name) {
        animation.time += deltaTime;

        if (name.includes('vibration')) {
            // 振动效果
            const vibration = Math.sin(animation.time * animation.frequency * 2 * Math.PI) * animation.amplitude;
            animation.target.position.y = animation.originalPosition.y + vibration;
            
        } else if (name.includes('wave')) {
            // 波浪效果
            if (animation.target.geometry && animation.originalVertices) {
                const vertices = animation.target.geometry.attributes.position.array;
                const time = animation.time * animation.frequency;
                
                for (let i = 0; i < vertices.length; i += 3) {
                    const x = animation.originalVertices[i];
                    const z = animation.originalVertices[i + 2];
                    vertices[i + 1] = animation.originalVertices[i + 1] + 
                                     Math.sin(time + x * 0.1 + z * 0.1) * animation.amplitude;
                }
                
                animation.target.geometry.attributes.position.needsUpdate = true;
            }
            
        } else if (name.includes('uv')) {
            // UV灯闪烁
            const flicker = animation.baseIntensity + 
                           Math.sin(animation.time * 10) * animation.flickerIntensity * Math.random();
            
            if (animation.target.material) {
                animation.target.material.emissiveIntensity = flicker;
            }
        }
    }

    // 创建补间动画
    createTweenAnimation(config) {
        const { target, property, from, to, duration, easing, onComplete } = config;
        
        const tween = new TWEEN.Tween(from)
            .to(to, duration)
            .easing(easing || TWEEN.Easing.Quadratic.InOut)
            .onUpdate((values) => {
                Object.keys(values).forEach(key => {
                    if (property.includes('.')) {
                        const props = property.split('.');
                        let obj = target;
                        for (let i = 0; i < props.length - 1; i++) {
                            obj = obj[props[i]];
                        }
                        obj[props[props.length - 1]] = values[key];
                    } else {
                        target[property] = values[key];
                    }
                });
            })
            .onComplete(() => {
                if (onComplete) onComplete();
            });

        return tween;
    }

    // 获取动画信息
    getAnimationInfo() {
        return {
            totalAnimations: this.animations.size,
            activeAnimations: Array.from(this.animations.values()).filter(a => a.isActive).length,
            isRunning: this.isRunning,
            globalSpeed: this.globalSpeed
        };
    }

    // 资源清理
    dispose() {
        this.stopAllAnimations();
        this.animations.clear();
        this.equipment = [];
        TWEEN.removeAll();
    }
}
