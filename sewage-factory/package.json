{"name": "sewage-treatment-3d", "version": "1.0.0", "description": "3D污水处理工艺流程可视化系统", "main": "src/main.js", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview --host", "serve": "vite preview --port 3000", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "format": "prettier --write src/**/*.js", "clean": "rm -rf dist"}, "keywords": ["three.js", "3d", "sewage-treatment", "visualization", "webgl"], "author": "Your Name", "license": "MIT", "dependencies": {"@tweenjs/tween.js": "^21.0.0", "chart.js": "^4.4.0", "three": "^0.158.0"}, "devDependencies": {"eslint": "^8.55.0", "prettier": "^3.1.0", "terser": "^5.43.1", "vite": "^4.5.3"}, "engines": {"node": ">=16.0.0"}, "browserslist": ["Chrome >= 80", "Firefox >= 75", "Safari >= 13", "Edge >= 80"]}