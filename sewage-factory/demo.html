<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>透明管道中的流动水流</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: Arial, sans-serif;
        }
        canvas {
            display: block;
        }
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="info">
        <h3>透明管道水流演示</h3>
        <p>鼠标拖拽旋转视角</p>
        <p>滚轮缩放视图</p>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <script>
        // 场景、相机、渲染器设置
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x001122);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.body.appendChild(renderer.domElement);

        // 添加灯光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        const pointLight = new THREE.PointLight(0x4488ff, 0.6, 100);
        pointLight.position.set(0, 0, 0);
        scene.add(pointLight);

        // 创建管道几何体
        const pipeGeometry = new THREE.CylinderGeometry(2, 2, 20, 32);
        const pipeMaterial = new THREE.MeshPhysicalMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.2,
            roughness: 0.1,
            metalness: 0.1,
            clearcoat: 1.0,
            clearcoatRoughness: 0.1,
            transmission: 0.9,
            thickness: 0.5
        });

        const pipe = new THREE.Mesh(pipeGeometry, pipeMaterial);
        pipe.rotation.z = Math.PI / 2; // 水平放置管道
        scene.add(pipe);

        // 创建水流几何体（多个小球组成流动效果）
        const waterBalls = [];
        const ballGeometry = new THREE.SphereGeometry(0.3, 16, 16);
        const waterMaterial = new THREE.MeshPhongMaterial({
            color: 0x2288ff,
            transparent: true,
            opacity: 0.8,
            shininess: 100
        });

        // 创建多个水球
        for (let i = 0; i < 50; i++) {
            const ball = new THREE.Mesh(ballGeometry, waterMaterial.clone());
            ball.position.x = (Math.random() - 0.5) * 20;
            ball.position.y = (Math.random() - 0.5) * 3;
            ball.position.z = (Math.random() - 0.5) * 3;
            
            // 给每个球一个随机的流动速度
            ball.userData = {
                speed: 0.05 + Math.random() * 0.05,
                originalY: ball.position.y,
                originalZ: ball.position.z,
                phase: Math.random() * Math.PI * 2
            };
            
            waterBalls.push(ball);
            scene.add(ball);
        }

        // 创建水流粒子效果
        const particleGeometry = new THREE.BufferGeometry();
        const particleCount = 200;
        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            positions[i * 3] = (Math.random() - 0.5) * 20;
            positions[i * 3 + 1] = (Math.random() - 0.5) * 3.5;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 3.5;
            
            velocities[i * 3] = 0.02 + Math.random() * 0.03; // x方向速度
            velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.01; // y方向随机波动
            velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.01; // z方向随机波动
        }

        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

        const particleMaterial = new THREE.PointsMaterial({
            color: 0x4499ff,
            size: 0.1,
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });

        const particles = new THREE.Points(particleGeometry, particleMaterial);
        scene.add(particles);

        // 鼠标控制
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let targetRotationX = 0, targetRotationY = 0;
        let currentRotationX = 0, currentRotationY = 0;

        document.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        document.addEventListener('mouseup', () => {
            isMouseDown = false;
        });

        document.addEventListener('mousemove', (e) => {
            if (isMouseDown) {
                const deltaX = e.clientX - mouseX;
                const deltaY = e.clientY - mouseY;
                
                targetRotationY += deltaX * 0.01;
                targetRotationX += deltaY * 0.01;
                
                mouseX = e.clientX;
                mouseY = e.clientY;
            }
        });

        // 滚轮缩放
        document.addEventListener('wheel', (e) => {
            camera.position.z += e.deltaY * 0.01;
            camera.position.z = Math.max(5, Math.min(50, camera.position.z));
        });

        // 设置相机位置
        camera.position.set(0, 0, 15);

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            // 平滑相机旋转
            currentRotationX += (targetRotationX - currentRotationX) * 0.05;
            currentRotationY += (targetRotationY - currentRotationY) * 0.05;

            // 应用旋转到整个场景
            scene.rotation.x = currentRotationX;
            scene.rotation.y = currentRotationY;

            const time = Date.now() * 0.001;

            // 更新水球位置
            waterBalls.forEach((ball, index) => {
                // 水平流动
                ball.position.x += ball.userData.speed;
                
                // 添加轻微的波浪运动
                ball.position.y = ball.userData.originalY + Math.sin(time * 2 + ball.userData.phase) * 0.2;
                ball.position.z = ball.userData.originalZ + Math.cos(time * 1.5 + ball.userData.phase) * 0.15;
                
                // 当球流出管道时，重置到起始位置
                if (ball.position.x > 10) {
                    ball.position.x = -10;
                }
                
                // 保持球在管道内
                const distFromCenter = Math.sqrt(ball.position.y * ball.position.y + ball.position.z * ball.position.z);
                if (distFromCenter > 1.5) {
                    const scale = 1.5 / distFromCenter;
                    ball.position.y *= scale;
                    ball.position.z *= scale;
                }
            });

            // 更新粒子位置
            const positions = particles.geometry.attributes.position.array;
            const velocities = particles.geometry.attributes.velocity.array;

            for (let i = 0; i < particleCount; i++) {
                const i3 = i * 3;
                
                positions[i3] += velocities[i3]; // x
                positions[i3 + 1] += velocities[i3 + 1]; // y
                positions[i3 + 2] += velocities[i3 + 2]; // z
                
                // 重置超出范围的粒子
                if (positions[i3] > 10) {
                    positions[i3] = -10;
                }
                
                // 保持粒子在管道内
                const y = positions[i3 + 1];
                const z = positions[i3 + 2];
                const dist = Math.sqrt(y * y + z * z);
                if (dist > 1.7) {
                    const scale = 1.7 / dist;
                    positions[i3 + 1] *= scale;
                    positions[i3 + 2] *= scale;
                }
            }

            particles.geometry.attributes.position.needsUpdate = true;

            // 管道轻微旋转
            pipe.rotation.x += 0.002;

            renderer.render(scene, camera);
        }

        // 响应窗口大小变化
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 开始动画
        animate();
    </script>
</body>
</html>